import 'package:flutter_test/flutter_test.dart';

void main() {
  // const channel = MethodChannel('flutter_kronos');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    // channel.setMockMethodCallHandler((MethodCall methodCall) async {
    //   return '42';
    // });
  });

  tearDown(() {
    // channel.setMockMethodCallHandler(null);
  });

  test('getPlatformVersion', () async {
    // expect(await FlutterKronos.platformVersion, '42');
  });
}
