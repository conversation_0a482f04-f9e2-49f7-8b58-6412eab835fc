name: flutter_kronos
description: Kronos is an open source Network Time Protocol (NTP) synchronization library for providing a trusted clock.
version: 0.1.0+1
homepage: 'https://github.com/hautvfami/flutter-kronos'
repository: 'https://github.com/hautvfami/flutter-kronos'

environment:
  sdk: ^3.5.3
  flutter: ">=2.5.2"

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter

module:
  androidX: true

flutter:
  plugin:
    platforms:
      android:
        package: com.stark.flutter_kronos
        pluginClass: FlutterKronosPlugin
      ios:
        pluginClass: FlutterKronosPlugin