import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/l10n/app_localizations.dart';
import 'package:transport_match/localizations/supported_locales.dart';
import 'package:transport_match/presentation/provider/app_provider.dart';
import 'package:transport_match/router/app_router.dart';
import 'package:transport_match/style/custom_theme.dart';

///App Initialization
class MyApp extends StatelessWidget {
  ///App Initialization constructor
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<AppProvider>(
          create: (context) => Injector.instance<AppProvider>(),
        ),
      ],
      child: ScreenUtilInit(
        minTextAdapt: true,
        designSize: const Size(375, 812),
        splitScreenMode: true,
        builder: (_, child) => Consumer<AppProvider>(
          builder: (context, appProvider, child) {
            return MaterialApp.router(
              title: 'Transport Match',
              debugShowCheckedModeBanner: false,
              theme: CustomTheme().light,
              themeMode: ThemeMode.light,
              locale: appProvider.locale,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: SupportedLocales.all,
              routerConfig: AppRouter.router,
            );
          },
        ),
      ),
    );
  }
}
