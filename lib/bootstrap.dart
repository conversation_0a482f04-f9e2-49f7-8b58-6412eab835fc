import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/firebase_options.dart';

/// initializes of required features
Future<void> bootstrap(FutureOr<Widget> Function() builder) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await _initialization();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitDown,
    DeviceOrientation.portraitUp,
  ]);
  runApp(
    await builder(),
  );
}

Future<void> _initialization() async {
  await _initHive();
  Injector.initModules();
  await Injector.instance.isReady<AppDB>();
  await Injector.instance.allReady();
}

Future<void> _initHive() async {
  await Hive.initFlutter();
}
