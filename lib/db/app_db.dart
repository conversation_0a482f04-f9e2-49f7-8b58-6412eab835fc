import 'dart:async';

import 'package:eraser/eraser.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:transport_match/presentation/models/signup_and_signin_model.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/logger.dart';

/// to store local data
class AppDB {
  AppDB._(this._box);
  static const _appDbBox = '_appDbBox';
  final Box<dynamic> _box;

  /// to get instance
  static Future<AppDB> getInstance() async {
    try {
      final box = await Hive.openBox<dynamic>(_appDbBox);
      return AppDB._(box);
    } catch (e) {
      final appDir = await getApplicationDocumentsDirectory();
      if (appDir.existsSync()) {
        appDir.deleteSync(recursive: true);
      }
      final box = await Hive.openBox<dynamic>(_appDbBox);
      return AppDB._(box);
    }
  }

  /// save value
  T getValue<T>(String key, {T? defaultValue}) =>
      _box.get(key, defaultValue: defaultValue) as T;

  /// save value
  Future<void> setValue<T>(String key, T value) => _box.put(key, value);

  /// to get user token
  String get token => getValue(AppStrings.token, defaultValue: '');

  ///to set user token
  set token(String update) => setValue(AppStrings.token, update);

  /// to get refresh token
  String get refreshToken =>
      getValue(AppStrings.refreshToken, defaultValue: '');

  ///to set refresh token
  set refreshToken(String update) => setValue(AppStrings.refreshToken, update);

  ///Removes all user data except
  Future<void> logoutUser() async {
    try {
      await _box.clear();
      unawaited(Eraser.clearAllAppNotifications());
    } catch (e) {
      e.logFatal;
    }
  }

  /// to set internet status
  set internetStatus(String status) =>
      setValue(AppStrings.internetStatus, status);

  /// to get internet status
  String get internetStatus =>
      getValue(AppStrings.internetStatus, defaultValue: AppStrings.connected);

  /// to check internet connection status is connected or not
  bool get isInternetConnected {
    return internetStatus == AppStrings.connected;
  }

  /// get user information
  SignUpAndSignInModel? get userModel =>
      getValue<dynamic>(AppStrings.userModel) != null
          ? SignUpAndSignInModel.fromJson(
              Map<String, dynamic>.from(getValue(AppStrings.userModel)),
            )
          : null;

  /// store user information
  set userModel(SignUpAndSignInModel? update) =>
      setValue(AppStrings.userModel, update?.toJson());

  /// get language preference
  String get languageCode =>
      getValue(AppStrings.languageCode, defaultValue: 'en');

  /// set language preference
  set languageCode(String update) => setValue(AppStrings.languageCode, update);

  /// get booking data
  String? get bookingData => getValue(AppStrings.bookingApiReqData);

  /// set booking data
  set bookingData(String? update) =>
      setValue(AppStrings.bookingApiReqData, update);

  /// get payment data from api
  String? get paymentData => getValue(AppStrings.paymentData);

  /// set payment data from api
  set paymentData(String? update) => setValue(AppStrings.paymentData, update);
}
