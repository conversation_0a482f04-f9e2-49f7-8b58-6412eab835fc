import 'package:flutter/material.dart';

@immutable

/// App theme colors
abstract class AppColors {
  //////
  static const Color transparent = Colors.transparent;
  static const Color red = Colors.red;
  static const Color primaryColor = Color(0xff009FE3);
  static const Color secondaryColor = Color(0xff67509C);
  static const Color tertiaryColor = Color(0xffE94E1B);
  static const Color black = Color(0xff000000);
  static const Color white = Color(0xffffffff);
  static const Color successColor = Color(0xff28a745);
  static const Color errorColor = Color(0xffdc3545);
  static const Color infoColor = Color(0xff17a2b8);
  static const Color warningColor = Color(0xffffc107);
  static const Color primaryColorLight = Color(0xffb3e3f8);
  static const Color pageBGColor = Color(0xffF8F9FA);
  static final Color shimmerColor = Colors.grey.shade300;
  static const Color greyColor = Color(0xff6C757D);
  static const Color unSelectedColor = Color(0xffADB5BD);

  /// Common colors
  static const Color ff343A40 = Color(0xff343A40);
  static const Color ff2B2829 = Color(0xff2B2829);
  static const Color ffADB5BD = Color(0xffADB5BD);
  static const Color ffF8F9FA = Color(0xffF8F9FA);
  static const Color ffDEE2E6 = Color(0xffDEE2E6);
  static const Color ff0087C7 = Color(0xff0087C7);
  static const Color ff67509C = Color(0xff67509C);
  static const Color ff6C757D = Color(0xff6C757D);
  static const Color ff17BD8D = Color(0xff17BD8D);
  static const Color ffEF4770 = Color(0xffEF4770);
  static const Color ffE6E6E6 = Color(0xffE6E6E6);
  static const Color ff495057 = Color(0xff495057);
  static const Color ff212529 = Color(0xff212529);
  static const Color ffCED4DA = Color(0xffCED4DA);
  static const Color ffFFC107 = Color(0xffFFC107);
  static const Color ffF2EEF8 = Color(0xffF2EEF8);
  static const Color ff9A7DCF = Color(0xff9A7DCF);
  static const Color ffD1ECF1 = Color(0xffD1ECF1);
  static const Color ffF5F0FF = Color(0xffF5F0FF);
  static const Color fff2eef8 = Color(0xfff2eef8);
}
