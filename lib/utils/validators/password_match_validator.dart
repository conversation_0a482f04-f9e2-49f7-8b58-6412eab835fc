import 'package:form_field_validator/form_field_validator.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';

class ConfirmPasswordValidator extends TextFieldValidator {
  ConfirmPasswordValidator({
    required String errorText,
    required this.password,
  }) : super(errorText);

  String password;

  @override
  bool get ignoreEmptyValues => true;

  @override
  bool isValid(String? value) {
    if (value == null) return false;

    return true;
  }

  @override
  String? call(String? value) {
    if (value?.isEmptyOrNull ?? true) {
      return errorText;
    }
    final testMessage = passwordValidator().call(value);
    if (testMessage == null) {
      if (value != password) {
        return rootNavKey.currentContext?.l10n.passwordDoesNotMatch;
      }
    } else {
      return testMessage;
    }
    return null;
  }
}
