import 'package:country_picker/country_picker.dart';
// ignore: implementation_imports
import 'package:country_picker/src/country_list_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_textfield.dart';

class MobileTextFiled extends StatelessWidget {
  const MobileTextFiled({
    super.key,
    required this.controller,
    required this.maxTextLength,
    required this.countryCode,
    required this.onSelect,
  });
  final TextEditingController controller;
  final int maxTextLength;
  final String countryCode;
  final void Function(Country country) onSelect;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h10),
      child: AppTextF<PERSON><PERSON>ield(
        controller: controller,
        title: context.l10n.mobileNumber,
        hintText: context.l10n.enterUrMobileNumber,
        keyboardType: TextInputType.number,
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        maxTextLength: maxTextLength,
        prefixIcon: GestureDetector(
          onTap: () => showModalBottomSheet(
            context: context,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppSize.r10),
                topRight: Radius.circular(AppSize.r10),
              ),
            ),
            isScrollControlled: true,
            constraints: BoxConstraints(maxHeight: context.height * 0.8),
            builder: (context) => Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.viewInsetsOf(context).bottom,
              ),
              child: CountryListView(onSelect: onSelect, showPhoneCode: true),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(countryCode),
              const Icon(Icons.arrow_drop_down_rounded),
            ],
          ),
        ),
        validator: (p0) {
          if (p0?.isEmpty ?? true) {
            return context.l10n.pleaseEnterUrMobileNumber;
          } else if (p0?.length != maxTextLength) {
            return context.l10n.pleaseEnterValidMobileNumber;
          } else {
            return null;
          }
        },
      ),
    );
  }
}
