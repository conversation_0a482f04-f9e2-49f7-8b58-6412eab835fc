import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_padding.dart';

/// Common Text filed
class AppTextFormField extends StatefulWidget {
  const AppTextFormField({
    this.title,
    this.titleStyle,
    this.controller,
    this.validator,
    this.prefixIcon,
    this.hintText,
    this.shadow,
    this.labelText,
    this.labelColor,
    this.inputFormatters,
    this.maxTextLength = 255,
    this.readOnly = false,
    this.keyboardType,
    this.onTap,
    this.subTitle,
    this.isStartTime = true,
    super.key,
    this.textAction,
    this.suffix,
    this.obscureText = false,
    this.suffixIcon,
    this.onChanged,
    this.cursorColor,
    this.inputBorder,
    this.minLine = 1,
    this.maxLine = 1,
    this.fillColor = AppColors.white,
    this.titleColor = AppColors.ff343A40,
    this.focusNode,
    this.fontSize,
    this.onSaved,
    this.contentHeight,
    this.borderRadius,
    this.style,
    this.contentWidth,
    this.hintStyle,
    this.borderSide,
    this.textAlignVertical,
    this.prefix,
    this.isDense,
    this.prefixIconConstraints,
    this.autofocus,
    this.suffixIconConstraints,
    this.floatingLabelColor,
    this.autofillHints,
    this.textAlign = TextAlign.start,
    this.textColor,
    this.autovalidateMode,
  });
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final String? title;
  final TextStyle? titleStyle;
  final void Function(String?)? onChanged;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool? isStartTime;
  final Widget? suffix;
  final BoxShadow? shadow;
  final String? hintText;
  final String? labelText;
  final Color? labelColor;
  final Color? floatingLabelColor;
  final Color? titleColor;
  final int? maxTextLength;
  final Color? cursorColor;
  final bool readOnly;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textAction;
  final void Function()? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final InputBorder? inputBorder;
  final int? maxLine;
  final int? minLine;
  final FocusNode? focusNode;
  final double? fontSize;
  final void Function(String?)? onSaved;
  final double? contentHeight;
  final double? contentWidth;
  final double? borderRadius;
  final Color? fillColor;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final BorderSide? borderSide;
  final TextAlignVertical? textAlignVertical;
  final Widget? prefix;
  final bool? isDense;
  final BoxConstraints? prefixIconConstraints;
  final String? subTitle;
  final bool? autofocus;
  final BoxConstraints? suffixIconConstraints;
  final Iterable<String>? autofillHints;
  final TextAlign textAlign;
  final Color? textColor;
  final AutovalidateMode? autovalidateMode;

  @override
  State<AppTextFormField> createState() => _AppTextFormFieldState();
}

class _AppTextFormFieldState extends State<AppTextFormField> {
  final FocusNode _focusNode = FocusNode();
  final ValueNotifier<bool> _isFocused = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    if (widget.focusNode == null) {
      _focusNode.addListener(() {
        _isFocused.value = _focusNode.hasFocus;
      });
    } else {
      widget.focusNode!.addListener(() {
        _isFocused.value = widget.focusNode!.hasFocus;
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Text(
            '  ${widget.title}',
            style:
                widget.titleStyle ??
                context.textTheme.titleSmall!.copyWith(
                  overflow: TextOverflow.ellipsis,
                  fontWeight: FontWeight.w500,
                  color: widget.titleColor,
                ),
          ),
        if (widget.title != null) Gap(AppSize.h4),
        ValueListenableBuilder(
          valueListenable: _isFocused,
          builder: (context, isFocused, child) {
            return TextFormField(
              focusNode: widget.focusNode ?? _focusNode,
              controller: widget.controller,
              keyboardType: widget.keyboardType,
              autovalidateMode:
                  widget.autovalidateMode ??
                  ((widget.readOnly || widget.onTap != null)
                      ? AutovalidateMode.onUserInteraction
                      : (widget.focusNode ?? _focusNode).hasFocus
                      ? AutovalidateMode.onUserInteraction
                      : AutovalidateMode.disabled),
              // widget.autovalidateMode ?? AutovalidateMode.onUserInteraction,
              maxLength: widget.maxTextLength,
              cursorColor: widget.cursorColor ?? AppColors.primaryColor,
              validator: widget.validator,
              textInputAction: widget.textAction,
              onChanged: widget.onChanged,
              readOnly: widget.readOnly,
              obscureText: widget.obscureText,
              onTap: widget.onTap,
              onTapOutside: (event) =>
                  FocusManager.instance.primaryFocus?.unfocus(),
              onSaved: widget.onSaved,
              maxLines: widget.maxLine,
              inputFormatters: widget.inputFormatters,
              textAlignVertical: widget.textAlignVertical,
              textAlign: widget.textAlign,
              autofocus: widget.autofocus ?? false,
              autofillHints: widget.autofillHints,
              minLines: widget.minLine,
              style:
                  widget.style ??
                  context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: widget.textColor ?? AppColors.ff343A40,
                    fontSize: AppSize.sp14,
                  ),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(
                  horizontal: widget.contentWidth ?? AppSize.w12,
                  vertical: widget.contentHeight ?? AppSize.h9,
                ),
                errorMaxLines: 2,
                counterText: '',
                prefixIconConstraints: widget.prefixIconConstraints,
                suffixIconConstraints: widget.suffixIconConstraints,
                isDense: widget.isDense ?? false,
                border:
                    widget.inputBorder ??
                    OutlineInputBorder(
                      borderRadius: BorderRadius.circular(
                        widget.borderRadius ?? AppSize.r4,
                      ),
                      borderSide: widget.borderSide ?? BorderSide.none,
                    ),
                prefixIcon: widget.prefixIcon == null
                    ? null
                    : AppPadding(
                        left: AppSize.w14,
                        right: AppSize.w8,
                        child: widget.prefixIcon,
                      ),
                prefix: widget.prefix == null
                    ? null
                    : AppPadding(
                        left: AppSize.w14,
                        right: AppSize.w8,
                        child: widget.prefix,
                      ),
                suffix: widget.suffix == null
                    ? null
                    : AppPadding(
                        right: AppSize.w14,
                        left: AppSize.w8,
                        child: widget.suffix,
                      ),
                suffixIcon: widget.suffixIcon == null
                    ? null
                    : AppPadding(
                        right: AppSize.w14,
                        left: AppSize.w8,
                        child: widget.suffixIcon,
                      ),
                hintText: widget.hintText,
                labelText: widget.labelText,
                floatingLabelStyle: TextStyle(
                  fontSize: widget.fontSize ?? AppSize.sp14,
                  color: widget.floatingLabelColor ?? AppColors.black,
                ),
                labelStyle: TextStyle(
                  fontSize: widget.fontSize ?? AppSize.sp14,
                  color: widget.labelColor ?? AppColors.ffADB5BD,
                ),
                hintStyle:
                    widget.hintStyle ??
                    context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: AppColors.ffADB5BD,
                      fontSize: AppSize.sp14,
                    ),
                errorStyle: TextStyle(
                  color: AppColors.errorColor,
                  fontSize: AppSize.sp12,
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? AppSize.r4,
                  ),
                  borderSide:
                      widget.borderSide ??
                      BorderSide(
                        width: AppSize.w2,
                        color: AppColors.errorColor,
                      ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? AppSize.r4,
                  ),
                  borderSide:
                      widget.borderSide ??
                      const BorderSide(color: AppColors.ffDEE2E6),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? AppSize.r4,
                  ),
                  borderSide:
                      widget.borderSide ??
                      BorderSide(
                        color: AppColors.primaryColor,
                        width: AppSize.w2,
                      ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? AppSize.r4,
                  ),
                  borderSide:
                      widget.borderSide ??
                      BorderSide(
                        width: AppSize.w2,
                        color: AppColors.errorColor,
                      ),
                ),
                filled: widget.fillColor != null,
                fillColor: widget.fillColor ?? AppColors.ffF8F9FA,
              ),
            );
          },
        ),
        if (widget.subTitle != null) Gap(AppSize.h3) else const SizedBox(),
        if (widget.subTitle != null)
          AppPadding(
            left: AppSize.w10,
            child: Text(
              widget.subTitle!,
              style:
                  widget.titleStyle ??
                  context.textTheme.titleMedium!.copyWith(
                    fontSize: AppSize.sp11,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
            ),
          ),
      ],
    );
  }
}
