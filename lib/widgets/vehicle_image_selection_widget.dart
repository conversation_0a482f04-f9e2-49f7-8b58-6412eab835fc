import 'dart:io';

import 'package:flutter/material.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/widget_zoom/widget_zoom.dart';

class VehicleImageSelectionWidget extends StatelessWidget {
  const VehicleImageSelectionWidget({
    super.key,
    this.fileImageList,
    required this.mainIndex,
    required this.carIndex,
    required this.onImageListChange,
    this.isReadOnly = false,
  });
  final List<String>? fileImageList;
  final int mainIndex;
  final int carIndex;
  final bool isReadOnly;
  final Function(List<String> imgList) onImageListChange;

  @override
  Widget build(BuildContext context) {
    final fileImage = fileImageList ?? [];
    return SizedBox(
      height: AppSize.h50 + AppSize.h5,
      child: ListView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.zero,
        itemCount:
            (fileImage.length) + (fileImage.length >= 7 || isReadOnly ? 0 : 1),
        itemBuilder: (context, index) {
          final removeIndex = fileImage.length < 7 && !isReadOnly
              ? index - 1
              : index;
          return Stack(
            alignment: Alignment.topRight,
            children: [
              Padding(
                padding: EdgeInsets.only(top: AppSize.h5, right: AppSize.w8),
                child: index == 0 && fileImage.length < 7 && !isReadOnly
                    ? GestureDetector(
                        onTap: () =>
                            AppCommonFunctions.showImagePickerPopup(
                              context: context,
                            ).then((value) {
                              if (value != null) {
                                final dummyList = [
                                  ...fileImageList ?? <String>[],
                                  ...value.map((e) => e.path),
                                ];

                                final limitedList = dummyList.length > 7
                                    ? dummyList.sublist(0, 7)
                                    : dummyList;

                                onImageListChange(limitedList);
                              }
                            }),
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            color: AppColors.transparent,
                            border: Border.all(
                              color: AppColors.primaryColorLight,
                            ),
                            borderRadius: BorderRadius.circular(AppSize.h5),
                          ),
                          child: SizedBox(
                            height: AppSize.h50,
                            width: AppSize.h50,
                            child: const Icon(Icons.add),
                          ),
                        ),
                      )
                    : WidgetZoom(
                        heroAnimationTag: fileImageList?[removeIndex] ?? '',
                        zoomWidget: DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(AppSize.h5),
                            image: DecorationImage(
                              image: FileImage(
                                File(fileImageList?[removeIndex] ?? ''),
                              ),
                            ),
                          ),
                          child: SizedBox(
                            width: AppSize.h50,
                            height: AppSize.h50,
                          ),
                        ),
                      ),
              ),
              if ((index != 0 || fileImage.length >= 7) && !isReadOnly)
                Padding(
                  padding: EdgeInsets.only(right: AppSize.w3),
                  child: GestureDetector(
                    onTap: () {
                      final dummyList = [...fileImageList ?? <String>[]]
                        ..removeAt(removeIndex);

                      onImageListChange(dummyList);
                    },
                    child: CircleAvatar(
                      radius: AppSize.h9,
                      backgroundColor: AppColors.errorColor,
                      child: Icon(
                        Icons.delete,
                        size: AppSize.w12,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
