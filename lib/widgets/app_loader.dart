import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';

/// Add loader on any screen by wrapping it with
class AppLoader extends StatelessWidget {
  const AppLoader({
    required this.isShowLoader,
    required this.child,
    this.isFullOpacity = false,
    super.key,
  });
  final bool isShowLoader;
  final bool isFullOpacity;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      // alignment: Alignment.center,
      children: [
        AbsorbPointer(
          absorbing: isShowLoader,
          child: Opacity(
            opacity: isFullOpacity
                ? 1
                : isShowLoader
                    ? 0.3
                    : 1,
            child: child,
          ),
        ),
        if (isShowLoader)
          Positioned.fill(
            child: SizedBox(
              height: AppSize.h100,
              width: AppSize.h100,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Lottie.asset(
                  AppAssets.animationLoaderAppLoader.path,
                  height: AppSize.h100,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
