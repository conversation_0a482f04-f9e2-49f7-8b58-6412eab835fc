import 'package:flutter/material.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// Dashed divider widget
class DashedDivider extends StatelessWidget {
  /// Constructor for DashedDivider
  const DashedDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Flexible(
      fit: FlexFit.tight,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSize.w4),
        child: CustomPaint(
          size: Size(double.infinity, AppSize.h1),
          painter: DashedLinePainter(
            dashWidth: AppSize.w6,
            dashSpace: AppSize.w6,
            thickness: AppSize.h1,
            color: AppColors.ff495057,
          ),
        ),
      ),
    );
  }
}

/// Custom painter for dashed lines
class DashedLinePainter extends CustomPainter {
  /// Constructor
  DashedLinePainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.thickness,
    required this.color,
  });

  /// Width of the dash
  final double dashWidth;

  /// Space between two dashes
  final double dashSpace;

  /// Line thickness
  final double thickness;

  /// Divider color
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke;

    var startX = 0.0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
