//

// import 'package:flutter/material.dart';

// /// Common app sized box
// class AppSizedBox extends SizedBox {
//   /// Creates a fixed size box. The [width] and [height] parameters can be null
//   /// to indicate that the size of the box should not be constrained in
//   /// the corresponding dimension.
//   const AppSizedBox({
//     super.key,
//     super.height,
//     super.width,
//     super.child,
//   });
// }
