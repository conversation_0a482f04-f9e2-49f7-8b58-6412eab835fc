import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/dashed_divider.dart';

/// Location info widget
class _LocationInfo extends StatelessWidget {
  /// Constructor
  const _LocationInfo({
    required this.title,
    required this.date,
    required this.latitude,
    required this.longitude,
  });

  /// Location title
  final String title;

  /// Location date
  final String date;

  final double? latitude;
  final double? longitude;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 0.5,
      children: [
        if (title.isNotEmpty)
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: context.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.ff343A40,
            ),
          ),
        if (date.isNotEmpty)
          Row(
            spacing: AppSize.w5,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppAssets.iconsCalendar.image(
                width: AppSize.w16,
                height: AppSize.w16,
              ),
              Text(
                date,
                style: context.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.ff6C757D,
                ),
              ),
            ],
          ),
        GestureDetector(
          onTap: () => latitude != null && longitude != null
              ? AppCommonFunctions.openMap(latitude!, longitude!)
              : null,
          child: Text(
            context.l10n.openMap,
            style: context.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.primaryColor.withValues(alpha: 0.7),
              decoration: TextDecoration.underline,
              decorationColor: AppColors.primaryColor.withValues(alpha: 0.7),
            ),
          ),
        ),
      ],
    );
  }
}

class LocationInfoWidget extends StatelessWidget {
  const LocationInfoWidget({
    super.key,
    required this.startLocationTitle,
    required this.startLocationDate,
    required this.endLocationTitle,
    required this.endLocationDate,
    required this.startLatitude,
    required this.startLongitude,
    required this.endLatitude,
    required this.endLongitude,
    this.centerWidget,
  });
  final String startLocationTitle;
  final String startLocationDate;
  final String endLocationTitle;
  final String endLocationDate;
  final String? startLatitude;
  final String? startLongitude;
  final String? endLatitude;
  final String? endLongitude;
  final Widget? Function(Widget child)? centerWidget;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: AppSize.w8,
      children: [
        // kilometer is use for location
        Flexible(
          child: _LocationInfo(
            title: startLocationTitle,
            date: startLocationDate,
            latitude: double.tryParse(startLatitude ?? ''),
            longitude: double.tryParse(startLongitude ?? ''),
          ),
        ),
        Flexible(
          child:
              centerWidget?.call(_LocationCenterDashWidget()) ??
              _LocationCenterDashWidget(),
        ),
        Flexible(
          child: _LocationInfo(
            title: endLocationTitle,
            date: endLocationDate,
            latitude: double.tryParse(endLatitude ?? ''),
            longitude: double.tryParse(endLongitude ?? ''),
          ),
        ),
      ],
    );
  }
}

class _LocationCenterDashWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: AppSize.sp4,
      children: [
        AppAssets.iconsLocationOrigin.image(height: AppSize.h16),
        const DashedDivider(),
        AppAssets.iconsLocation.image(height: AppSize.h16),
      ],
    );
  }
}
