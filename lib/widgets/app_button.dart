import 'dart:async';

import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/loading_indicator.dart';

/// Global app button [FilledButton]
class AppButton extends StatefulWidget {
  /// constructor
  const AppButton({
    required this.text,
    super.key,
    this.isLoading = false,
    this.isDisabled = false,
    this.showIconOnly = false,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.buttonStyle,
    this.icon,
    this.visualDensity,
    this.textStyle,
    this.buttonColor,
    this.isFillButton = true,
    this.isBottomBtn = true,
    this.borderColor,
    this.horizontalPad,
  });

  /// text of button
  final String text;

  /// on pressed function
  final VoidCallback? onPressed;

  /// is loading
  final bool isLoading;

  /// is disabled
  final bool isDisabled;

  /// background color of button
  final Color? backgroundColor;

  /// foreground color of button for which is apply to text
  final Color? foregroundColor;

  /// button Style
  final ButtonStyle? buttonStyle;

  /// Visual density for button
  final VisualDensity? visualDensity;

  /// Text style for button
  final TextStyle? textStyle;

  /// Icon for button
  final Widget? icon;

  /// Flag to show only icon
  final bool showIconOnly;

  /// Flag to get button is for bottom or not
  final bool isBottomBtn;

  /// Button Color
  final Color? buttonColor;

  /// border Color
  final Color? borderColor;

  /// Is Fill Button
  final bool isFillButton;

  /// this padding is used if there were two button in row
  final double? horizontalPad;

  @override
  State<AppButton> createState() => _AppButtonState();
}

class _AppButtonState extends State<AppButton> {
  bool _isProcessing = false;
  Timer? _debounceTimer;

  void _handleTap() {
    if (_isProcessing) return;

    _isProcessing = true;
    widget.onPressed?.call();

    _debounceTimer = Timer(
      const Duration(milliseconds: 500),
      () => _isProcessing = false,
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isDisabled
          ? null
          : widget.isLoading
          ? () {}
          : _handleTap,
      child: Container(
        height: AppSize.h42,
        margin: widget.isBottomBtn
            ? EdgeInsets.symmetric(
                horizontal: widget.horizontalPad ?? AppSize.appPadding,
              ).add(EdgeInsets.only(bottom: AppSize.h32, top: AppSize.h5))
            : null,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color:
              widget.buttonColor ??
              (widget.isFillButton
                  ? AppColors.primaryColor
                  : AppColors.transparent),
          borderRadius: BorderRadius.circular(AppSize.r4),
          border: Border.all(
            width: 2,
            color: widget.isFillButton
                ? AppColors.transparent
                : widget.borderColor ?? AppColors.primaryColor,
          ),
        ),
        child: widget.isLoading
            ? Padding(
                padding: EdgeInsets.symmetric(vertical: AppSize.h2),
                child: const FittedBox(
                  fit: BoxFit.scaleDown,
                  child: LoadingIndicator(),
                ),
              )
            : (widget.icon != null && widget.showIconOnly)
            ? widget.icon!
            : (widget.icon != null)
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: AppSize.w6,
                children: [
                  widget.icon!,
                  Text(
                    widget.text,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style:
                        widget.textStyle ??
                        context.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: widget.isFillButton
                              ? AppColors.white
                              : AppColors.primaryColor,
                          fontSize: AppSize.sp16,
                        ),
                  ),
                ],
              )
            : Text(
                widget.text,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style:
                    widget.textStyle ??
                    context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: widget.isFillButton
                          ? AppColors.white
                          : AppColors.primaryColor,
                      fontSize: AppSize.sp16,
                    ),
              ),
      ),
    );
  }
}
