import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart' show AppColors;
import 'package:transport_match/utils/app_size.dart';

class _TabBarAnimation extends StatefulWidget {
  const _TabBarAnimation({
    required this.animation,
    required this.builder,
    required this.index,
  });
  final Animation<double> animation;
  final int index;
  final Widget Function(BuildContext, Widget?, double opacity) builder;

  @override
  State<_TabBarAnimation> createState() => _TabBarAnimationState();
}

class _TabBarAnimationState extends State<_TabBarAnimation> {
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.animation,
      builder: (context, child) {
        final value = widget.animation.value;
        final opacity = value == widget.index
            ? 1.0
            : (widget.index - value).abs() <= 1.0
                ? 1 - (widget.index - value).abs()
                : 0.0;
        return widget.builder(context, child, opacity);
      },
    );
  }
}

class CommonTabBar extends StatelessWidget {
  const CommonTabBar({
    super.key,
    required this.title,
    required this.index,
    required this.animation,
  });
  final String title;
  final int index;
  final Animation<double> animation;

  @override
  Widget build(BuildContext context) {
    return _TabBarAnimation(
      index: index,
      animation: animation,
      builder: (context, child, opacity) => Container(
        alignment: Alignment.center,
        child: Text(
          title,
          style: context.textTheme.titleMedium?.copyWith(
            fontSize: AppSize.sp16,
            color: Color.lerp(
              AppColors.unSelectedColor,
              AppColors.primaryColor,
              opacity,
            ),
            fontWeight: FontWeight.lerp(
              FontWeight.w400,
              FontWeight.w600,
              opacity,
            ),
          ),
        ),
      ),
    );
  }
}
