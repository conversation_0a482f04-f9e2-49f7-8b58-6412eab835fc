import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_dropdown.dart';
import 'package:transport_match/widgets/transporter_card.dart';

class AddToCarWidget extends StatelessWidget {
  const AddToCarWidget({
    super.key,
    required this.providerData,
    this.isTrip = false,
    this.isInAllVehicle = false,
    this.isAddMoreBtn = false,
    required this.onSlotRemove,
    this.onRemoveAll,
    required this.slotList,
    required this.items,
    required this.onAddMoreCar,
    required this.onSaveBtn,
  }) : assert(
         !isInAllVehicle || onRemoveAll != null,
         'If isInAllVehicle is true then onRemoveAll must not be null',
       );
  final ProviderListData providerData;
  final bool isTrip;
  final bool isInAllVehicle;
  final bool isAddMoreBtn;
  final List<String> slotList;
  final void Function(int index) onSlotRemove;
  final void Function() onAddMoreCar;
  final void Function()? onRemoveAll;
  final void Function() onSaveBtn;
  final List<Widget> Function(int index) items;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.bottomSheetPadding),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppSize.r16),
          topRight: Radius.circular(AppSize.r16),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              context.l10n.howManyCar,
              textAlign: TextAlign.start,
              style: context.textTheme.bodyMedium?.copyWith(
                fontSize: AppSize.sp16,
                fontWeight: FontWeight.w400,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: AppSize.h16),
              child: TransporterCard(
                data: providerData,
                isBorder: true,
                isSelected: false,
                isTrip: isTrip,
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: slotList.length,
              itemBuilder: (context, index) {
                if (items(index).isNotEmpty) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '  ${context.l10n.chooseSlot} ${index + 1} ${context.l10n.car}',
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (!isInAllVehicle && (slotList.first.isNotEmpty))
                            GestureDetector(
                              onTap: () => onSlotRemove(index),
                              child: const Icon(
                                Icons.remove_circle_outline_rounded,
                                color: AppColors.errorColor,
                              ),
                            ),
                        ],
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                          top: AppSize.h4,
                          bottom: AppSize.h16,
                        ),
                        child: AppDropBottomSheet(
                          labelText: context.l10n.chooseVehicle,
                          selectedItem: slotList[index].nullCheck,
                          items: items(index),
                        ),
                      ),
                    ],
                  );
                } else {
                  return const SizedBox();
                }
              },
            ),
            if (!isAddMoreBtn && !isInAllVehicle)
              GestureDetector(
                onTap: onAddMoreCar,
                child: Text(
                  '+ ${context.l10n.assignMoreCar}',
                  style: TextStyle(
                    fontSize: AppSize.sp16,
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            if (isInAllVehicle)
              GestureDetector(
                onTap: onRemoveAll,
                child: Text(
                  context.l10n.removeAllVehicle,
                  style: TextStyle(
                    fontSize: AppSize.sp16,
                    color: AppColors.errorColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            Gap(AppSize.h16),
            AppButton(
              text: context.l10n.save,
              isBottomBtn: false,
              onPressed: onSaveBtn,
            ),
          ],
        ),
      ),
    );
  }
}
