import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_padding.dart';

/// Custom app-bar
// class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
//   /// Constructor
//   const CustomAppBar({
//     required this.title,
//     this.onTap,
//     super.key,
//     this.bottom,
//     this.height,
//     this.horizontalPadding,
//     this.verticalPadding,
//     this.actions,
//   });

//   /// On back button tap
//   final void Function()? onTap;

//   /// AppBar title
//   final String title;

//   /// bottom widget
//   final PreferredSizeWidget? bottom;

//   /// horizontal padding
//   final double? horizontalPadding;

//   /// vertical padding
//   final double? verticalPadding;

//   /// AppBar height
//   final double? height;

//   /// AppBar actions
//   final List<Widget>? actions;

//   @override
//   Widget build(BuildContext context) {
//     return AppPadding.symmetric(
//       horizontal: horizontalPadding ?? AppSize.w20,
//       vertical: verticalPadding ?? AppSize.h16,
//       child: AppBar(
//         centerTitle: false,
//         leadingWidth: AppSize.w16,
//         surfaceTintColor: Colors.transparent,
//         backgroundColor: AppColors.ffF8F9FA,
//         leading: GestureDetector(
//           onTap: onTap ??
//               () {
//                 if (FocusScope.of(context).isFirstFocus) {
//                   FocusScope.of(context).unfocus();
//                 }
//                 AppNavigationService.pop(context);
//               },
//           child: AppAssets.iconsLeftArrow.image(),
//         ),
//         title: Text(
//           title,
//           style: context.textTheme.titleLarge
//               ?.copyWith(fontWeight: FontWeight.w500),
//         ),
//         bottom: bottom,
//         actions: actions,
//       ),
//     );
//   }

//   @override
//   Size get preferredSize => Size.fromHeight(
//         bottom != null
//             ? bottom!.preferredSize.height + (height ?? 57.h)
//             : (height ?? 57.h),
//       );
// }

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Constructor
  const CustomAppBar({
    required this.title,
    this.leading,
    super.key,
    this.actions,
    this.centerTitle = true,
    this.canPop = true,
    this.onBackPress,
    this.horizontalPadding,
    this.verticalPadding,
    this.leadingWidth,
    this.backgroundColor,
  });

  /// appbar title
  final String title;

  /// appbar background color
  final Color? backgroundColor;

  /// appbar leading
  final Widget? leading;

  /// actions
  final List<Widget>? actions;

  /// center title
  final bool centerTitle;

  /// Can pop screen
  final bool canPop;

  /// Back press callback
  final VoidCallback? onBackPress;

  //Horizontal Padding
  final double? horizontalPadding;

  //Vertical Padding
  final double? verticalPadding;

  /// Leading Width
  final double? leadingWidth;

  @override
  Widget build(BuildContext context) {
    return AppPadding.symmetric(
      horizontal: horizontalPadding ?? AppSize.appPadding,
      vertical: verticalPadding,
      child: AppBar(
        centerTitle: false,
        actions: actions,
        leadingWidth: leadingWidth ?? AppSize.w16,
        surfaceTintColor: Colors.transparent,
        backgroundColor: backgroundColor ?? AppColors.ffF8F9FA,
        leading:
            (canPop && Navigator.canPop(context)) ||
                (onBackPress != null && leading == null)
            ? InkWell(
                onTap:
                    onBackPress ??
                    () {
                      Navigator.pop(context);
                    },
                child: Icon(
                  Icons.arrow_back,
                  // color: context.themeTextColors.text,
                  size: AppSize.sp20,
                ),
              )
            : (leading != null)
            ? leading
            : null,
        title: Text(
          title,
          style: context.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
