import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/utils/app_size.dart';

/// Common App Snackbar
class AppSnackbar {
  /// common snackbar show method
  static void showSnackBar({
    required String message,
    int? durationInSecond,
    Widget? prefixIcon,
    Color? color,
  }) {
    ScaffoldMessenger.of(rootNavKey.currentContext!)
      ..hideCurrentSnackBar()
      ..showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          backgroundColor: color ?? Colors.black,
          duration: Duration(seconds: durationInSecond ?? 1),
          showCloseIcon: true,
          content: Row(
            children: [
              if (prefixIcon != null) ...[prefixIcon, Gap(AppSize.w8)],
              Flexible(
                child: Text(
                  message,
                  style: rootNavKey.currentContext!.textTheme.titleSmall
                      ?.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      );
  }
}
