// import 'package:flutter/material.dart';
// import 'package:flutter/rendering.dart';

// class Gap extends LeafRenderObjectWidget {
//   const Gap(
//     this.mainAxisExtent, {
//     super.key,
//     this.crossAxisExtent,
//     this.color,
//     this.fallbackDirection,
//     this.borderRadius,
//   })  : assert(
//           mainAxisExtent >= 0 && mainAxisExtent < double.infinity,
//           'mainAxisExtent >= 0 && mainAxisExtent < double.infinity',
//         ),
//         assert(
//           crossAxisExtent == null || crossAxisExtent >= 0,
//           'crossAxisExtent == null || crossAxisExtent >= 0',
//         );

//   final double mainAxisExtent;

//   final double? crossAxisExtent;

//   final Color? color;

//   final BorderRadius? borderRadius;

//   final Axis? fallbackDirection;

//   @override
//   RenderObject createRenderObject(BuildContext context) {
//     return RenderGap(
//       mainAxisExtent: mainAxisExtent,
//       crossAxisExtent: crossAxisExtent ?? 0,
//       color: color,
//       borderRadius: borderRadius,
//       fallbackDirection: fallbackDirection,
//     );
//   }

//   @override
//   void updateRenderObject(BuildContext context, RenderGap renderObject) {
//     renderObject
//       ..mainAxisExtent = mainAxisExtent
//       ..crossAxisExtent = crossAxisExtent ?? 0
//       ..color = color
//       ..borderRadius = borderRadius
//       ..fallbackDirection = fallbackDirection;
//   }

//   @override
//   void debugFillProperties(DiagnosticPropertiesBuilder properties) {
//     super.debugFillProperties(properties);
//     properties.add(DoubleProperty('mainAxisExtent', mainAxisExtent));
//     properties.add(
//       DoubleProperty('crossAxisExtent', crossAxisExtent, defaultValue: 0),
//     );
//     properties.add(ColorProperty('color', color));
//     properties.add(EnumProperty<Axis>('fallbackDirection', fallbackDirection));
//   }
// }

// class RenderGap extends RenderBox {
//   RenderGap({
//     required double mainAxisExtent,
//     double? crossAxisExtent,
//     Axis? fallbackDirection,
//     Color? color,
//     BorderRadius? borderRadius,
//   })  : _mainAxisExtent = mainAxisExtent,
//         _crossAxisExtent = crossAxisExtent,
//         _color = color,
//         _borderRadius = borderRadius,
//         _fallbackDirection = fallbackDirection;

//   double get mainAxisExtent => _mainAxisExtent;
//   double _mainAxisExtent;
//   set mainAxisExtent(double value) {
//     if (_mainAxisExtent != value) {
//       _mainAxisExtent = value;
//       markNeedsLayout();
//     }
//   }

//   double? get crossAxisExtent => _crossAxisExtent;
//   double? _crossAxisExtent;
//   set crossAxisExtent(double? value) {
//     if (_crossAxisExtent != value) {
//       _crossAxisExtent = value;
//       markNeedsLayout();
//     }
//   }

//   Axis? get fallbackDirection => _fallbackDirection;
//   Axis? _fallbackDirection;
//   set fallbackDirection(Axis? value) {
//     if (_fallbackDirection != value) {
//       _fallbackDirection = value;
//       markNeedsLayout();
//     }
//   }

//   Axis? get _direction {
//     final parentNode = parent;
//     if (parentNode is RenderFlex) {
//       return parentNode.direction;
//     } else {
//       return fallbackDirection;
//     }
//   }

//   Color? get color => _color;
//   Color? _color;
//   set color(Color? value) {
//     if (_color != value) {
//       _color = value;
//       markNeedsPaint();
//     }
//   }

//   BorderRadius? get borderRadius => _borderRadius;
//   BorderRadius? _borderRadius;
//   set borderRadius(BorderRadius? value) {
//     if (_borderRadius != value) {
//       _borderRadius = value;
//       markNeedsPaint();
//     }
//   }

//   @override
//   double computeMinIntrinsicWidth(double height) {
//     return _computeIntrinsicExtent(
//       Axis.horizontal,
//       () => super.computeMinIntrinsicWidth(height),
//     )!;
//   }

//   @override
//   double computeMaxIntrinsicWidth(double height) {
//     return _computeIntrinsicExtent(
//       Axis.horizontal,
//       () => super.computeMaxIntrinsicWidth(height),
//     )!;
//   }

//   @override
//   double computeMinIntrinsicHeight(double width) {
//     return _computeIntrinsicExtent(
//       Axis.vertical,
//       () => super.computeMinIntrinsicHeight(width),
//     )!;
//   }

//   @override
//   double computeMaxIntrinsicHeight(double width) {
//     return _computeIntrinsicExtent(
//       Axis.vertical,
//       () => super.computeMaxIntrinsicHeight(width),
//     )!;
//   }

//   double? _computeIntrinsicExtent(Axis axis, double Function() compute) {
//     final direction = _direction;
//     if (direction == axis) {
//       return _mainAxisExtent;
//     } else {
//       if (_crossAxisExtent!.isFinite) {
//         return _crossAxisExtent;
//       } else {
//         return compute();
//       }
//     }
//   }

//   @override
//   Size computeDryLayout(BoxConstraints constraints) {
//     final direction = _direction;

//     if (direction != null) {
//       if (direction == Axis.horizontal) {
//         return constraints.constrain(Size(mainAxisExtent, crossAxisExtent!));
//       } else {
//         return constraints.constrain(Size(crossAxisExtent!, mainAxisExtent));
//       }
//     } else {
//       throw FlutterError(
//         'A Gap widget must be placed directly inside a Flex widget '
//         'or its fallbackDirection must not be null',
//       );
//     }
//   }

//   @override
//   void performLayout() {
//     size = computeDryLayout(constraints);
//   }

//   @override
//   void paint(PaintingContext context, Offset offset) {
//     if (color != null) {
//       final paint = Paint()..color = color!;
//       final rect = offset & size;

//       if (borderRadius != null) {
//         final rrect = borderRadius!.toRRect(rect);
//         context.canvas.drawRRect(rrect, paint);
//       } else {
//         context.canvas.drawRect(rect, paint);
//       }
//     }
//   }

//   @override
//   void debugFillProperties(DiagnosticPropertiesBuilder properties) {
//     super.debugFillProperties(properties);
//     properties.add(DoubleProperty('mainAxisExtent', mainAxisExtent));
//     properties.add(DoubleProperty('crossAxisExtent', crossAxisExtent));
//     properties.add(ColorProperty('color', color));
//     properties.add(EnumProperty<Axis>('fallbackDirection', fallbackDirection));
//   }
// }
