import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class AssignCarWidget extends StatelessWidget {
  const AssignCarWidget({
    super.key,
    required this.value,
    required this.totalCar,
  });
  final int value;
  final int totalCar;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.l10n.assignedCars,
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.ff343A40,
              ),
            ),
            Text(
              '$value/$totalCar',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.ff343A40,
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(top: AppSize.h4, bottom: AppSize.h24),
          child: LinearProgressIndicator(
            value: value / totalCar,
            borderRadius: BorderRadius.circular(AppSize.r20),
            minHeight: AppSize.h10,
            backgroundColor: AppColors.ffDEE2E6,
            valueColor: const AlwaysStoppedAnimation(AppColors.ff67509C),
          ),
        ),
      ],
    );
  }
}
