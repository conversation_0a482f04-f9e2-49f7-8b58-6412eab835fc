import 'package:flutter/material.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/title_info.dart';

class NoOfVehicleWidget extends StatelessWidget {
  const NoOfVehicleWidget({
    super.key,
    required this.noOfVehicle,
    this.isTitleWidget = false,
    this.subTitleColor,
    this.titleColor,
    this.subTitleFontWeight,
    this.titleFontWeight,
    this.titleSize,
    this.subTitleSize,
  });
  final String noOfVehicle;
  final bool isTitleWidget;
  final Color? titleColor;
  final Color? subTitleColor;
  final FontWeight? subTitleFontWeight;
  final FontWeight? titleFontWeight;
  final double? titleSize;
  final double? subTitleSize;

  @override
  Widget build(BuildContext context) {
    return isTitleWidget
        ? TitleInfo(
            title: context.l10n.noOfVehicle,
            subTitle: noOfVehicle,
            subTitleColor: subTitleColor,
            subTitleFontWeight: subTitleFontWeight,
            titleColor: titleColor,
            titleFontWeight: titleFontWeight,
          )
        : Text.rich(
            TextSpan(
              text: '${context.l10n.noOfVehicle}: ',
              style: TextStyle(
                fontWeight: titleFontWeight ?? FontWeight.w500,
                fontSize: titleSize ?? AppSize.sp12,
                color: titleColor ?? AppColors.ffADB5BD,
              ),
              children: [
                TextSpan(
                  text: noOfVehicle,
                  style: TextStyle(
                    fontWeight: subTitleFontWeight ?? FontWeight.w700,
                    fontSize: subTitleSize ?? AppSize.sp12,
                    color: subTitleColor ?? AppColors.black,
                  ),
                ),
              ],
            ),
          );
  }
}
