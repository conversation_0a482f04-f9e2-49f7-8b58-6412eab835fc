import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// Custom widget to show custom checkmark
class AppConfirmCheckBox extends StatefulWidget {
  /// Constructor
  const AppConfirmCheckBox({
    required this.onSelectionChanged,
    this.description,
    this.value,
    this.isDisabledValueChange = false,
    super.key,
  });

  /// Checkmark description.
  final String? description;
  final bool? value;
  // This variable is used to disable the value change of the checkmark but it will still trigger the callback
  final bool isDisabledValueChange;

  /// Callback to handle checkmark selection.
  final void Function({required bool value}) onSelectionChanged;

  @override
  State<AppConfirmCheckBox> createState() => _AppConfirmCheckBoxState();
}

class _AppConfirmCheckBoxState extends State<AppConfirmCheckBox> {
  /// Notifier for selection change
  final ValueNotifier<bool> _selectionNotifier = ValueNotifier(false);

  @override
  void dispose() {
    _selectionNotifier.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AppConfirmCheckBox oldWidget) {
    if (widget.value != null) {
      _selectionNotifier.value = widget.value!;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize:
          widget.description == null ? MainAxisSize.min : MainAxisSize.max,
      spacing: AppSize.w8,
      children: [
        ValueListenableBuilder(
          valueListenable: _selectionNotifier,
          builder: (context, selectionNotifier, child) {
            return GestureDetector(
              onTap: () {
                if (widget.isDisabledValueChange) {
                  return;
                }
                _selectionNotifier.value = !_selectionNotifier.value;
                widget.onSelectionChanged(value: _selectionNotifier.value);
              },
              child: Container(
                height: AppSize.h20,
                width: AppSize.h20,
                decoration: BoxDecoration(
                  color: selectionNotifier
                      ? AppColors.primaryColor.withValues(
                          alpha: widget.isDisabledValueChange ? 0.3 : 1,
                        )
                      : AppColors.transparent,
                  borderRadius: BorderRadius.circular(AppSize.r4),
                  border: Border.all(
                    width: AppSize.w2,
                    color: selectionNotifier
                        ? AppColors.transparent
                        : AppColors.primaryColor.withValues(
                            alpha: widget.isDisabledValueChange ? 0.3 : 1,
                          ),
                  ),
                ),
                child: selectionNotifier
                    ? Center(
                        child: Icon(
                          Icons.done,
                          color: AppColors.white,
                          size: AppSize.sp15,
                        ),
                      )
                    : const SizedBox(),
              ),
            );
          },
        ),
        if (widget.description != null) ...[
          Flexible(
            child: Text(
              widget.description!,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: AppSize.sp14,
                color: AppColors.ff343A40,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
