import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path/path.dart' as p;
import 'package:shimmer/shimmer.dart';
import 'package:transport_match/utils/app_colors.dart';

enum _ImageSource {
  fromAsset,
  fromNetwork,
}

/// Common App Image Widget
class AppImage extends StatelessWidget {
  const AppImage.asset(
    this.image, {
    this.width = 24,
    this.height = 24,
    this.fit,
    this.color,
    this.radius,
    this.placeHolder,
    this.matchTextDirection = true,
    super.key,
  }) : _source = _ImageSource.fromAsset;

  const AppImage.squareAsset(
    this.image, {
    double size = 24,
    this.fit,
    this.color,
    this.radius,
    this.matchTextDirection = true,
    this.placeHolder,
    super.key,
  })  : width = size,
        height = size,
        _source = _ImageSource.fromAsset;

  const AppImage.circleAsset(
    this.image, {
    double size = 24,
    this.fit,
    this.color,
    this.matchTextDirection = true,
    this.placeHolder,
    super.key,
  })  : width = size,
        radius = size / 2,
        height = size,
        _source = _ImageSource.fromAsset;

  const AppImage.network(
    this.image, {
    this.width = 24,
    this.height = 24,
    this.fit,
    this.color,
    this.radius,
    this.matchTextDirection = true,
    this.placeHolder,
    super.key,
  }) : _source = _ImageSource.fromNetwork;

  const AppImage.squareNetwork(
    this.image, {
    double size = 24,
    this.fit,
    this.color,
    this.radius,
    this.matchTextDirection = true,
    super.key,
    this.placeHolder,
  })  : width = size,
        height = size,
        _source = _ImageSource.fromNetwork;

  const AppImage.circleNetwork(
    this.image, {
    double size = 24,
    this.fit,
    this.color,
    this.matchTextDirection = true,
    super.key,
    this.placeHolder,
  })  : width = size,
        radius = size / 2,
        height = size,
        _source = _ImageSource.fromNetwork;

  const AppImage.feed(
    this.image, {
    this.width,
    this.height,
    this.fit,
    this.color,
    this.radius,
    this.matchTextDirection = true,
    this.placeHolder,
    super.key,
  }) : _source = _ImageSource.fromNetwork;
  final String image;
  final double? width;
  final double? height;
  final double? radius;
  final BoxFit? fit;
  final Color? color;
  final String? placeHolder;
  final bool matchTextDirection;
  final _ImageSource _source;

  @override
  Widget build(BuildContext context) {
    Widget child;
    final isSvgImage = p.extension(image) == '.svg';
    switch (_source) {
      case _ImageSource.fromAsset:
        if (isSvgImage) {
          child = SvgPicture.asset(
            image,
            // color: color,
            width: width,
            height: height,
            fit: fit ?? BoxFit.scaleDown,
            matchTextDirection: matchTextDirection,
          );
        } else {
          child = Image.asset(
            image,
            color: color,
            width: width,
            height: height,
            fit: fit ?? BoxFit.scaleDown,
            matchTextDirection: matchTextDirection,
          );
        }
      case _ImageSource.fromNetwork:
        if (isSvgImage) {
          child = SvgPicture.network(
            image,
            // color: color,
            width: width,
            height: height,
            fit: fit ?? BoxFit.cover,
            matchTextDirection: matchTextDirection,
            placeholderBuilder: (context) => Container(
              height: height,
              width: width,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(radius ?? 10),
                border: Border.all(
                  color: AppColors.red.withValues(alpha: 0.1),
                ),
              ),
              child: Icon(
                Icons.error,
                color: AppColors.red.withValues(alpha: 0.7),
                size: 30,
              ),
            ),
            // AppImage.circleAsset(
            //   placeHolder ?? AppAssets.placeHolder,
            //   fit: BoxFit.cover,
            // ),
          );
        } else {
          child = CachedNetworkImage(
            imageUrl: image,
            color: color,
            width: width,
            height: height,
            fit: fit ?? BoxFit.cover,
            matchTextDirection: matchTextDirection,
            placeholder: (context, url) {
              return Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: height,
                  width: width,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(radius ?? 10),
                  ),
                ),
              );
              // return AppImage.circleAsset(
              //   placeHolder ?? AppAssets.placeHolder,
              //   fit: BoxFit.cover,
              // );
            },
            errorWidget: (context, url, error) {
              return Container(
                height: height,
                width: width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(radius ?? 10),
                  border: Border.all(
                    color: AppColors.red.withValues(alpha: 0.1),
                  ),
                ),
                child: Icon(
                  Icons.error,
                  color: AppColors.red.withValues(alpha: 0.7),
                  size: 30,
                ),
              );
              // AppImage.circleAsset(
              //   placeHolder ?? AppAssets.placeHolder,
              //   fit: BoxFit.contain,
              // );
            },
          );
        }
      // default:
      //   child = const SizedBox.shrink();
      //   break;
    }
    if (radius != null && radius! > 0) {
      child = ClipRRect(
        clipBehavior: Clip.hardEdge,
        borderRadius: BorderRadius.circular(radius!),
        child: child,
      );
    }
    return child;
  }
}

// class CachedImage extends StatelessWidget {
//   const CachedImage({
//     required this.imageUrl,
//     this.placeholder,
//     this.placeholderWidget,
//     this.width = 24,
//     this.height = 24,
//     this.fit = BoxFit.none,
//     this.color,
//     this.matchTextDirection = true,
//     super.key,
//   }) : assert(
//           placeholder == null || placeholderWidget == null,
//           'placeholder and placeholderWidget cannot be both not null',
//         );
//   final String imageUrl;
//   final String? placeholder;
//   final Widget? placeholderWidget;
//   final double width;
//   final double height;
//   final BoxFit fit;
//   final Color? color;
//   final bool matchTextDirection;

//   @override
//   Widget build(BuildContext context) {
//     return CachedNetworkImage(
//       width: width,
//       height: height,
//       imageUrl: imageUrl,
//       placeholder: (context, url) =>
//           placeholderWidget ??
//           (placeholder!.isEmpty
//               ? Shimmer.fromColors(
//                   baseColor: Colors.grey[300]!,
//                   highlightColor: Colors.grey[100]!,
//                   child: Container(
//                     height: height,
//                     width: width,
//                     decoration: BoxDecoration(
//                       color: Colors.grey,
//                       borderRadius: BorderRadius.circular(10),
//                     ),
//                   ),
//                 )
//               : Container(
//                   height: height,
//                   width: width,
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(10),
//                     border: Border.all(
//                       color: AppColors.red.withValues(alpha: 0.1),
//                     ),
//                   ),
//                   child: Icon(
//                     Icons.error,
//                     color: AppColors.red.withValues(alpha: 0.7),
//                     size: 30,
//                   ),
//                 )
//           // Image.asset(placeholder!, fit: BoxFit.contain)
//           ),
//       errorWidget: (context, url, error) =>
//           placeholderWidget ??
//           (placeholder!.isEmpty
//               ? const SizedBox.shrink()
//               : Container(
//                   height: height,
//                   width: width,
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(10),
//                     border: Border.all(
//                       color: AppColors.red.withValues(alpha: 0.1),
//                     ),
//                   ),
//                   child: Icon(
//                     Icons.error,
//                     color: AppColors.red.withValues(alpha: 0.7),
//                     size: 30,
//                   ),
//                 )
//           // Image.asset(placeholder!, fit: BoxFit.contain)
//           ),
//       fit: fit,
//       useOldImageOnUrlChange: true,
//     );
//   }
// }

// enum _ImageSource {
//   fromAsset,
//   fromNetwork,
// }

// /// Common App Image Widget
// class AppImage extends StatelessWidget {

//   const AppImage.asset(
//     this.image, {
//     this.width = 24,
//     this.height = 24,
//     this.fit,
//     this.color,
//     this.radius,
//     this.placeHolder,
//     this.matchTextDirection = true,
//     super.key,
//   })  : _source = _ImageSource.fromAsset;

//   const AppImage.squareAsset(
//     this.image, {
//     double size = 24,
//     this.fit,
//     this.color,
//     this.radius,
//     this.matchTextDirection = true,
//     this.placeHolder,
//     super.key,
//   })  : width = size,
//         height = size,
//         _source = _ImageSource.fromAsset;

//   const AppImage.circleAsset(
//     this.image, {
//     double size = 24,
//     this.fit,
//     this.color,
//     this.matchTextDirection = true,
//     this.placeHolder,
//     super.key,
//   })  : width = size,
//         radius = size / 2,
//         height = size,
//         _source = _ImageSource.fromAsset;

//   const AppImage.network(
//     this.image, {
//     this.width = 24,
//     this.height = 24,
//     this.fit,
//     this.color,
//     this.radius,
//     this.matchTextDirection = true,
//     this.placeHolder,
//     super.key,
//   })  : _source = _ImageSource.fromNetwork;

//   const AppImage.squareNetwork(
//     this.image, {
//     double size = 24,
//     this.fit,
//     this.color,
//     this.radius,
//     this.matchTextDirection = true,
//     super.key,
//     this.placeHolder,
//   })  : width = size,
//         height = size,
//         _source = _ImageSource.fromNetwork;

//   const AppImage.circleNetwork(
//     this.image, {
//     double size = 24,
//     this.fit,
//     this.color,
//     this.matchTextDirection = true,
//     super.key,
//     this.placeHolder,
//   })  : width = size,
//         radius = size / 2,
//         height = size,
//         _source = _ImageSource.fromNetwork;

//   const AppImage.feed(
//       this.image, {
//         this.width,
//         this.height,
//         this.fit,
//         this.color,
//         this.radius,
//         this.matchTextDirection = true,
//         this.placeHolder,
//         super.key,
//       })  : _source = _ImageSource.fromNetwork;
//   final String image;
//   final double? width;
//   final double? height;
//   final double? radius;
//   final BoxFit? fit;
//   final Color? color;
//   final String? placeHolder;
//   final bool matchTextDirection;
//   final _ImageSource _source;

//   @override
//   Widget build(BuildContext context) {
//     Widget child;
//     final isSvgImage = p.extension(image) == '.svg';
//     switch (_source) {
//       case _ImageSource.fromAsset:
//         if (isSvgImage) {
//           child = SvgPicture.asset(
//             image,
//             // color: color,
//             width: width,
//             height: height,
//             fit: fit ?? BoxFit.scaleDown,
//             matchTextDirection: matchTextDirection,
//           );
//         } else {
//           child = Image.asset(
//             image,
//             color: color,
//             width: width,
//             height: height,
//             fit: fit ?? BoxFit.scaleDown,
//             matchTextDirection: matchTextDirection,
//           );
//         }
//       case _ImageSource.fromNetwork:
//         if (isSvgImage) {
//           child = SvgPicture.network(
//             image,
//             // color: color,
//             width: width,
//             height: height,
//             fit: fit ?? BoxFit.cover,
//             matchTextDirection: matchTextDirection,
//             placeholderBuilder: (context) => AppImage.circleAsset(
//                 placeHolder ?? AppAssets.placeHolder,
//                 fit: BoxFit.cover,),
//           );
//         } else {
//           child = CachedNetworkImage(
//               imageUrl: image,
//               color: color,
//               width: width,
//               height: height,
//               fit: fit ?? BoxFit.cover,
//               matchTextDirection: matchTextDirection,
//               placeholder: (context, url) {
//                 return AppImage.circleAsset(
//                   placeHolder ?? AppAssets.placeHolder,
//                   fit: BoxFit.cover,
//                 );
//               },
//               errorWidget: (context, url, error) {
//                 return AppImage.circleAsset(
//                   placeHolder ?? AppAssets.placeHolder,
//                   fit: BoxFit.cover,
//                 );
//               },);
//         }
//       // default:
//       //   child = const SizedBox.shrink();
//       //   break;
//     }
//     if (radius != null && radius! > 0) {
//       child = ClipRRect(
//         clipBehavior: Clip.hardEdge,
//         borderRadius: BorderRadius.circular(radius!),
//         child: child,
//       );
//     }
//     return child;
//   }
// }

// class CachedImage extends StatelessWidget {

//   const CachedImage({
//     required this.imageUrl,
//     this.placeholder,
//     this.placeholderWidget,
//     this.width = 24,
//     this.height = 24,
//     this.fit = BoxFit.none,
//     this.color,
//     this.matchTextDirection = true,
//     super.key,
//   })  : assert(placeholder == null || placeholderWidget == null);
//   final String imageUrl;
//   final String? placeholder;
//   final Widget? placeholderWidget;
//   final double width;
//   final double height;
//   final BoxFit fit;
//   final Color? color;
//   final bool matchTextDirection;

//   @override
//   Widget build(BuildContext context) {
//     return CachedNetworkImage(
//       width: width,
//       height: height,
//       imageUrl: imageUrl,
//       placeholder: (context, url) =>
//           placeholderWidget ??
//           (placeholder!.isEmpty
//               ? const SizedBox.shrink()
//               : Image.asset(placeholder!, fit: fit)),
//       errorWidget: (context, url, error) =>
//           placeholderWidget ??
//           (placeholder!.isEmpty
//               ? const SizedBox.shrink()
//               : Image.asset(placeholder!, fit: fit)),
//       fit: fit,
//       useOldImageOnUrlChange: true,
//     );
//   }
// }
