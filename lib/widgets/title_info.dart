import 'package:flutter/cupertino.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Title info
class TitleInfo extends StatelessWidget {
  /// Constructor
  const TitleInfo({
    super.key,
    required this.title,
    required this.subTitle,
    this.subTitleColor,
    this.titleColor,
    this.subTitleFontWeight,
    this.titleFontWeight,
    this.titleSize,
    this.subTitleSize,
    this.isAxisEnd = false,
  });

  ///  title
  final String title;

  ///  subTitle
  final String subTitle;

  ///  title
  final Color? titleColor;

  ///  subTitle
  final Color? subTitleColor;

  ///  subTitle FontWeight
  final FontWeight? subTitleFontWeight;

  ///  subTitle FontWeight
  final FontWeight? titleFontWeight;

  /// to align the widget at the end of the axis
  final bool isAxisEnd;
  final double? titleSize;
  final double? subTitleSize;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: isAxisEnd
          ? CrossAxisAlignment.end
          : CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: titleSize ?? AppSize.sp12,
            color: titleColor ?? AppColors.ffADB5BD,
            fontWeight: titleFontWeight ?? FontWeight.w500,
          ),
        ),
        MarqueeWidget(
          child: Text(
            subTitle,
            style: TextStyle(
              fontSize: subTitleSize ?? AppSize.sp18,
              fontWeight: subTitleFontWeight ?? FontWeight.w400,
              color: subTitleColor ?? AppColors.ff343A40,
            ),
          ),
        ),
      ],
    );
  }
}
