import 'package:transport_match/env/env_types_enum.dart';

/// Both DebugEnv and ReleaseEnv must implement all these values
abstract class EnvFields {
  /// field name for BaseUrl
  abstract final String baseUrl;

  /// field name for google map key (android only)
  abstract final String googleMapAndroidKey;

  /// field name for google map key (ios only)
  abstract final String googleMapIosKey;

  /// field name for Socket URL
  abstract final String socketUrl;
}

/// Global variable for setting environment
late EnvTypes currentEnv;
