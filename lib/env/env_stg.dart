import 'package:envied/envied.dart';
import 'package:transport_match/env/env.dart';

part 'env_stg.g.dart';

/// Staging Environment
@Envied(name: 'Env', path: '.env_stg', obfuscate: false)
class EnvStaging implements AppEnv {
  /// constructor
  EnvStaging();

  @EnviedField(varName: EnvKeys.baseUrl)
  @override
  final String baseUrl = _Env.baseUrl;

  @EnviedField(varName: EnvKeys.googleMapAndroidKey)
  @override
  final String googleMapAndroidKey = _Env.googleMapAndroidKey;

  @EnviedField(varName: EnvKeys.googleMapIosKey)
  @override
  final String googleMapIosKey = _Env.googleMapIosKey;

  @EnviedField(varName: EnvKeys.socketUrl)
  @override
  final String socketUrl = _Env.socketUrl;
}
