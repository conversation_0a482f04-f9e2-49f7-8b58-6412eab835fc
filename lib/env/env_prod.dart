import 'package:envied/envied.dart';
import 'package:transport_match/env/env.dart';

part 'env_prod.g.dart';

/// Production environment
@Envied(name: 'Env', path: '.env_prod', obfuscate: true)
class EnvProd implements AppEnv {
  ///constructor
  EnvProd();

  @EnviedField(varName: EnvKeys.baseUrl)
  @override
  final String baseUrl = _Env.baseUrl;

  @EnviedField(varName: EnvKeys.googleMapAndroidKey)
  @override
  final String googleMapAndroidKey = _Env.googleMapAndroidKey;

  @EnviedField(varName: EnvKeys.googleMapIosKey)
  @override
  final String googleMapIosKey = _Env.googleMapIosKey;

  @EnviedField(varName: EnvKeys.socketUrl)
  @override
  final String socketUrl = _Env.socketUrl;
}
