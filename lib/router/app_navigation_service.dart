import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

final GlobalKey<NavigatorState> rootNavKey = GlobalKey<NavigatorState>();

/// Router Service to provide a consistent interface for navigation
class AppNavigationService {
  /// Navigate to a named route
  static Future<void> pushNamed<T extends Object?>(
    BuildContext context,
    String routeName, {
    Map<String, String> params = const {},
    Map<String, dynamic> queryParams = const {},
    Object? extra,
    Function(Object? value)? afterBack,
  }) async {
    final result = await GoRouter.of(context).pushNamed<T>(
      routeName,
      pathParameters: params,
      queryParameters: queryParams,
      extra: extra,
    );
    if (afterBack != null) afterBack(result);
  }

  /// Replace the current screen with a named route
  static Future<T?> replaceNamed<T extends Object?>(
    BuildContext context,
    String routeName, {
    Map<String, String> params = const {},
    Map<String, dynamic> queryParams = const {},
    Object? extra,
  }) async {
    return GoRouter.of(context).replaceNamed<T>(
      routeName,
      pathParameters: params,
      queryParameters: queryParams,
      extra: extra,
    );
  }

  /// This will use when we need to navigate to a base route page
  static Future<void> goNamed(
    BuildContext context,
    String routeName, {
    Object? extra,
  }) async {
    return GoRouter.of(context).goNamed(routeName, extra: extra);
  }

  /// Push and remove all screens using GoRouter
  static Future<void> pushAndRemoveAllPreviousRoute<T extends Object?>(
    BuildContext context,
    String routeName, {
    // Map<String, String> params = const {},
    // Map<String, dynamic> queryParams = const {},
    Object? extra,
    bool isBaseRoute = false,
  }) async {
    while (GoRouter.of(context).canPop()) {
      GoRouter.of(context).pop();
    }
    if (!isBaseRoute) {
      unawaited(
        GoRouter.of(context).pushReplacementNamed<T>(routeName, extra: extra),
      );
    } else {
      GoRouter.of(rootNavKey.currentContext!).go(routeName, extra: extra);
    }
  }

  /// Go back to the previous screen
  static void pop<T extends Object?>(BuildContext context, [T? result]) {
    if (GoRouter.of(context).canPop()) {
      context.pop(result);
      // GoRouter.of(context).pop(result);
    }
  }
}
