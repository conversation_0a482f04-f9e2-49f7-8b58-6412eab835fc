import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/home_module/home_screen.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/empty_transporter_screen/empty_transporter_screen.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/empty_transporter_screen/models/empty_transporter_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/models/address_search_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/search_address_screen.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/models/transporter_list_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/transporter_list_screen.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/user_info_page_screen/user_info_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';

/// Home module routes configuration.
class HomeRoutes {
  static StatefulShellBranch buildHomeBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.homeBase,
          builder: (context, state) => const HomeScreen(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedHomeRoutes() {
    return [
      GoRoute(
        path: AppRoutes.homeTransporterListPath,
        name: AppRoutes.homeTransporterListScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra as TransporterListParams?;
          return TransporterListScreen(transporterListParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.homeEmptyTransporterPath,
        name: AppRoutes.homeEmptyTransporterScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as EmptyTransporterParams;
          return EmptyTransporterScreen(emptyTransporterParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.homeUserInfoPath,
        name: AppRoutes.homeUserInfoScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra as String?;
          return UserInfoScreen(bookingId: params?.toString());
        },
      ),
      GoRoute(
        path: AppRoutes.homeAddressSearchPath,
        name: AppRoutes.homeAddressSearchScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as AddressSearchParams;
          return AddressSearchScreen(addressSearchParams: params);
        },
      ),
    ];
  }
}
