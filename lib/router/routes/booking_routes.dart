import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/booking_shipment_confirmation_page/models/booking_shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/model/payment_param.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/payment_screen.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/booking_shipment_confirmation_page/booking_shipment_confirmation_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';

/// Booking module routes configuration.
class BookingRoutes {
  static List<GoRoute> buildBookingRoutes() {
    return [
      GoRoute(
        path: AppRoutes.bookingShipmentConfirmationPath,
        name: AppRoutes.bookingShipmentConfirmationScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as BookingShipmentConfirmationParams;
          return BookingShipmentConfirmationScreen(
            shipmentConfirmationParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.bookingPaymentPath,
        name: AppRoutes.bookingPaymentScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as PaymentParam;
          return PaymentScreen(
            isExclusiveTrip: params.isExclusiveTrip,
            bookingId: params.bookingId,
            bookingProviderData: params.bookingProviderData,
            paymentDataModel: params.paymentDataModel,
          );
        },
      ),
    ];
  }
}
