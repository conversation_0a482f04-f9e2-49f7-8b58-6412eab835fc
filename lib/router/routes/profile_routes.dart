import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_detail_page/customer_detail_screen.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/customer_support_screen.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/edit_profile_screen.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/models/edit_profile_param.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/set_new_password_page/set_new_password_screen.dart';
import 'package:transport_match/presentation/modules/profile_module/profile_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';

/// Profile module routes configuration.
class ProfileRoutes {
  static StatefulShellBranch buildProfileBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.profileBase,
          builder: (context, state) => const ProfileScreen(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedProfileRoutes() {
    return [
      GoRoute(
        path: AppRoutes.profileEditPath,
        name: AppRoutes.profileEditScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => EditProfileScreen(
          editProfileParam: state.extra! as EditProfileParam,
        ),
      ),
      GoRoute(
        path: AppRoutes.profileSetNewPasswordPath,
        name: AppRoutes.profileSetNewPasswordScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          return const SetNewPasswordScreen();
        },
      ),
      GoRoute(
        path: AppRoutes.profileCustomerSupportPath,
        name: AppRoutes.profileCustomerSupportScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const CustomerSupportScreen(),
      ),
      GoRoute(
        path: AppRoutes.profileCustomerDetailPath,
        name: AppRoutes.profileCustomerDetailScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const CustomerDetailScreen(),
      ),
    ];
  }
}
