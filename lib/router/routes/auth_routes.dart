import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/check_otp_screen.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/check_otp_params.dart';
import 'package:transport_match/presentation/modules/auth_module/forgot_password_page/forgot_password_screen.dart';
import 'package:transport_match/presentation/modules/auth_module/login_screen_page/login_screen.dart';
import 'package:transport_match/presentation/modules/auth_module/reset_password_page/reset_password_screen.dart';
import 'package:transport_match/presentation/modules/auth_module/signup_page/signup_screen.dart';
import 'package:transport_match/router/app_routes.dart';

/// Auth module routes configuration.
class AuthRoutes {
  static ShellRoute buildAuthShellRoute() {
    return ShellRoute(
      builder: (context, state, child) => child,
      routes: [
        GoRoute(
          path: AppRoutes.authBase,
          name: AppRoutes.authLoginScreen,
          builder: (context, state) => const LoginScreen(),
          routes: [
            GoRoute(
              path: AppRoutes.authSignupPath,
              name: AppRoutes.authSignupScreen,
              builder: (context, state) => const SignupScreen(),
            ),
            GoRoute(
              path: AppRoutes.authResetPasswordPath,
              name: AppRoutes.authResetPasswordScreen,
              builder: (context, state) {
                return const ResetPasswordScreen();
              },
            ),
            GoRoute(
              path: AppRoutes.authForgotPasswordPath,
              name: AppRoutes.authForgotPasswordScreen,
              builder: (context, state) => const ForgotPasswordScreen(),
            ),
            GoRoute(
              path: AppRoutes.authCheckOtpPath,
              name: AppRoutes.authCheckOtpScreen,
              builder: (context, state) {
                final params = state.extra! as CheckOtpParams;
                return CheckOtpScreen(
                  checkOtpParams: params,
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
