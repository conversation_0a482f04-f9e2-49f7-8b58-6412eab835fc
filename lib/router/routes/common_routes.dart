import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/common_webview_screen.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';

/// Common routes configuration.
class CommonRoutes {
  static List<GoRoute> buildCommonRoutes() {
    return [
      GoRoute(
        path: '${AppRoutes.commonBase}/${AppRoutes.commonWebViewPath}',
        name: AppRoutes.commonWebViewScreen,
        parentNavigatorKey: root<PERSON><PERSON><PERSON><PERSON>,
        builder: (context, state) {
          final params = state.extra! as CommonWebViewParams;
          return CommonWebViewScreen(
            commonWebViewParams: params,
          );
        },
      ),
    ];
  }
}
