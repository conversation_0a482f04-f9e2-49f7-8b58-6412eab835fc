import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/notifications_module/notifications_screen.dart';
import 'package:transport_match/router/app_routes.dart';

/// Notifications module routes configuration.
class NotificationsRoutes {
  static StatefulShellBranch buildNotificationsBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.notificationBase,
          builder: (context, state) => const NotificationsScreen(),
        ),
      ],
    );
  }
}
