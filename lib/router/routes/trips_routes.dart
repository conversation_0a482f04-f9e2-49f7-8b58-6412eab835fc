import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/car_info_screen.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/models/trip_shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/stock_locations_page/models/stock_locations_params.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/stock_locations_page/stock_locations_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/my_trip_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/checklist_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/show_checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/pages/show_checklist_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/car_payment_detail_page/car_payment_detail_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/car_payment_detail_page/models/car_payment_detail_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/remain_payment_summary_page/models/remain_payment_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/remain_payment_summary_page/remain_payment_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/trip_user_info_page/models/trip_user_info_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/trip_user_info_page/trip_user_info_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/trip_details_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/rate_page/models/rate_page_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/rate_page/rating_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/chat_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/pages/add_note_page/add_note_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/pages/add_note_page/model/add_note_param.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/requested_trips_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/models/trip_transporter_list_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/trip_confirmation_page/trip_shipment_confirmation_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/trip_transporter_list_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/models/requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/edit_rested_trip_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/rested_requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/page/edit_rested_trip_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/rested_requested_trips_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/pages/auction_page/auction_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';

/// Trips module routes configuration.
class TripsRoutes {
  static StatefulShellBranch buildTripsBranch() {
    return StatefulShellBranch(
      routes: [
        GoRoute(
          path: AppRoutes.tripsBase,
          builder: (context, state) => const MyTripScreen(),
        ),
      ],
    );
  }

  static List<GoRoute> buildNestedTripsRoutes() {
    return [
      GoRoute(
        path: AppRoutes.tripsTripDetailsPath,
        name: AppRoutes.tripsTripDetailsScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as TripDetailsParams;
          return TripDetailsScreen(tripDetailsParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsTripShipmentConfirmationPath,
        name: AppRoutes.tripsTripShipmentConfirmationScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as TripShipmentConfirmationParams;
          return TripShipmentConfirmationScreen(
            tripShipmentConfirmationParams: params,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsChatPath,
        name: AppRoutes.tripsChatScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ChatParams;
          return ChatScreen(chatParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsCarPaymentDetailPath,
        name: AppRoutes.tripsCarPaymentDetailScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as CarPaymentDetailParams;
          return CarPaymentDetailScreen(carPaymentDetailParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsChecklistPath,
        name: AppRoutes.tripsChecklistScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ChecklistParams;
          return ChecklistScreen(checklistParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsShowChecklistPath,
        name: AppRoutes.tripsShowChecklistScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as ShowChecklistParams;
          return ShowChecklistScreen(showChecklistParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsRemainPaymentPath,
        name: AppRoutes.tripsRemainPaymentScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as RemainPaymentParams;
          return RemainPaymentScreen(remainPaymentParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsUserInfoPath,
        name: AppRoutes.tripsUserInfoScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as TripUserInfoParams;
          return TripUserInfoPage(tripUserInfoParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsRequestedTripsPath,
        name: AppRoutes.tripsRequestedTripsScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra as RequestedTripsParams?;
          return RequestedTripsScreen(requestedTripsParams: params!);
        },
      ),
      GoRoute(
        path: AppRoutes.carInfoPath,
        name: AppRoutes.carInfoSheet,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as CarInfoParams;
          return CarInfoScreen(carInfoParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsAuctionPath,
        name: AppRoutes.tripsAuctionScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const AuctionScreen(),
      ),
      GoRoute(
        path: AppRoutes.tripsAddNotePath,
        name: AppRoutes.tripsAddNoteScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final tripId = state.extra as AddNoteParam?;
          return AddNoteScreen(addNoteParam: tripId);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsEditRestedTripPath,
        name: AppRoutes.tripsEditRestedTripScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as EditRestedTripParams;
          return EditRestedTripScreen(editRestedTripParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsRatePath,
        name: AppRoutes.tripsRateScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as RateParams;
          return RatingScreen(rateParams: params);
        },
      ),
      GoRoute(
        path: AppRoutes.tripsRestedRequestedTripsPath,
        name: AppRoutes.tripsRestedRequestedTripsScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as RestedRequestedTripsParams;
          return ChangeNotifierProvider(
            create: (context) => params.requestedTripsProvider,
            builder: (context, child) {
              return RestedRequestedTripsScreen(
                restedRequestedTripsParams: params,
              );
              // return RestedRequestedTripsScreen(
              //   value: params?.value ?? 2,
              //   id: params?.id ?? '0',
              //   index: params?.index,
              //   requestedTripsProvider: context.read(),
              // );
            },
          );
        },
      ),
      GoRoute(
        path: AppRoutes.tripsTripTransporterListPath,
        name: AppRoutes.tripsTripTransporterListScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as TripTransporterListParams;
          return TripTransporterListScreen(tripTransporterListParams: params);
        },
      ),

      GoRoute(
        path: AppRoutes.stockLocationsPath,
        name: AppRoutes.stockLocationsScreen,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as StockLocationsParams;
          return StockLocationsScreen(stockLocationsParams: params);
        },
      ),
    ];
  }
}
