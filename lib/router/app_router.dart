import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/app_upgrade_page/app_upgrade_screen.dart';
import 'package:transport_match/presentation/app_upgrade_page/models/app_upgrade_params.dart';
import 'package:transport_match/presentation/initial_screen.dart';
import 'package:transport_match/presentation/modules/bottom_bar_screen/bottom_bar_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/router/routes/auth_routes.dart';
import 'package:transport_match/router/routes/booking_routes.dart';
import 'package:transport_match/router/routes/common_routes.dart';
import 'package:transport_match/router/routes/home_routes.dart';
import 'package:transport_match/router/routes/notifications_routes.dart';
import 'package:transport_match/router/routes/profile_routes.dart';
import 'package:transport_match/router/routes/trips_routes.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    navigatorKey: rootNavKey,
    initialLocation: AppRoutes.initialPath,
    debugLogDiagnostics: true,
    routes: [
      // Initial route
      GoRoute(
        path: AppRoutes.initialPath,
        name: AppRoutes.initial,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) => const InitialScreen(),
        redirect: (context, state) {
          final value =
              Injector.instance<AppDB>().token.isNotEmptyAndNotNull ||
              Injector.instance<AppDB>().userModel != null;
          return value ? AppRoutes.homeBase : AppRoutes.authBase;
        },
      ),
      GoRoute(
        path: AppRoutes.upgradePath,
        name: AppRoutes.upgradePath,
        parentNavigatorKey: rootNavKey,
        builder: (context, state) {
          final params = state.extra! as AppUpgradeParams;
          return AppUpdateScreen(appUpgradeParams: params);
        },

      ),


      // Auth module routes
      AuthRoutes.buildAuthShellRoute(),

      // Common routes
      ...CommonRoutes.buildCommonRoutes(),

      // Booking module routes
      ...BookingRoutes.buildBookingRoutes(),

      // Bottom bar shell route for top-level screens
      StatefulShellRoute.indexedStack(
        parentNavigatorKey: rootNavKey,
        builder: (context, state, navigationShell) {
          return BottomBarScreen(navigationShell: navigationShell);
        },
        branches: [
          // Home branch
          HomeRoutes.buildHomeBranch(),
          // Trips branch
          TripsRoutes.buildTripsBranch(),
          // Notifications branch
          NotificationsRoutes.buildNotificationsBranch(),
          // Profile branch
          ProfileRoutes.buildProfileBranch(),
        ],
      ),

      // Nested routes (full-screen, no bottom bar)
      ...HomeRoutes.buildNestedHomeRoutes(),
      ...TripsRoutes.buildNestedTripsRoutes(),
      ...ProfileRoutes.buildNestedProfileRoutes(),
    ],
    redirect: (context, state) => null,
    errorBuilder: (context, state) =>
        Scaffold(body: Center(child: Text('Error: ${state.error}'))),
  );
}

// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';
// import 'package:provider/provider.dart';
// import 'package:transport_match/db/app_db.dart';
// import 'package:transport_match/di/injector.dart';
// import 'package:transport_match/extensions/ext_string_null.dart';
// import 'package:transport_match/presentation/initial_screen.dart';
// import 'package:transport_match/presentation/modules/auth_module/check_otp_page/check_otp_screen.dart';
// import 'package:transport_match/presentation/modules/auth_module/forgot_password_page/forgot_password_screen.dart';
// import 'package:transport_match/presentation/modules/auth_module/login_screen_page/login_screen.dart';
// import 'package:transport_match/presentation/modules/auth_module/reset_password_page/reset_password_screen.dart';
// import 'package:transport_match/presentation/modules/auth_module/signup_page/signup_screen.dart';
// import 'package:transport_match/presentation/modules/booking_module/pages/payment_screen.dart';
// import 'package:transport_match/presentation/modules/booking_module/pages/shipment_confirmation_screen.dart';
// import 'package:transport_match/presentation/modules/booking_module/provider/booking_provider.dart';
// import 'package:transport_match/presentation/modules/bottom_bar_screen/bottom_bar_screen.dart';
// import 'package:transport_match/presentation/modules/home_module/home_screen.dart';
// import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
// import 'package:transport_match/presentation/modules/home_module/pages/pages/empty_transporter_page/empty_transporter_widget.dart';
// import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_page/search_address_screen.dart';
// import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_page/transporter_list_screen.dart';
// import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/user_info_page/user_info_screen.dart';
// import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/my_trip_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_summary_model.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/checklist_page/checklist_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/rate_page/rating_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_screen/chat_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/payment_summary/remain_payment_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/trip_detail_page/pages/car_payment_detail_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/trip_detail_page/provider/trip_detail_provider.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/trip_detail_page/trip_details_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/widgets/add_note_page.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/exclusive_trip_page/trip_transporter_list_page/trip_transporter_list.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/edit_rested_trip_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/rested_requested_trips_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/pages/auction_page/auction_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/requested_trips_screen.dart';
// import 'package:transport_match/presentation/modules/my_trips_module/widgets/trip_user_info_page.dart';
// import 'package:transport_match/presentation/modules/notifications_module/notifications_screen.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/customer_detail_page/customer_detail_screen.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/customer_support_screen.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/edit_profile_screen.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/set_new_password_page/set_new_password_screen.dart';
// import 'package:transport_match/presentation/modules/profile_module/profile_screen.dart';
// import 'package:transport_match/router/app_navigation_service.dart';
// import 'package:transport_match/router/app_routes.dart';
// import 'package:transport_match/router/routes/auth_routes.dart';
// import 'package:transport_match/router/routes/booking_routes.dart';
// import 'package:transport_match/router/routes/common_routes.dart';
// import 'package:transport_match/router/routes/home_routes.dart';
// import 'package:transport_match/router/routes/notifications_routes.dart';
// import 'package:transport_match/router/routes/profile_routes.dart';
// import 'package:transport_match/router/routes/trips_routes.dart';
// import 'package:transport_match/widgets/car_info_sheet.dart';
// import 'package:transport_match/widgets/screens/common_webview.dart';

// class AppRouter {
//   static final GoRouter router = GoRouter(
//     navigatorKey: rootNavKey,
//     initialLocation: AppRoutes.initialPath,
//     debugLogDiagnostics: true,
//     routes: [
//       // Initial route
//       GoRoute(
//         path: AppRoutes.initialPath,
//         name: AppRoutes.initial,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => const InitialScreen(),
//         redirect: (context, state) {
//           final value = Injector.instance<AppDB>().token.isNotEmptyAndNotNull ||
//               Injector.instance<AppDB>().userModel != null;
//           return value ? AppRoutes.homeBase : AppRoutes.authBase;
//         },
//       ),

//       // Auth module routes
//       ShellRoute(
//         builder: (context, state, child) => child,
//         routes: [
//           GoRoute(
//             path: AppRoutes.authBase,
//             name: AppRoutes.authLoginScreen,
//             builder: (context, state) => const LoginScreen(),
//             routes: [
//               GoRoute(
//                 path: AppRoutes.authSignupPath,
//                 name: AppRoutes.authSignupScreen,
//                 builder: (context, state) => const SignupScreen(),
//               ),
//               GoRoute(
//                 path: AppRoutes.authResetPasswordPath,
//                 name: AppRoutes.authResetPasswordScreen,
//                 builder: (context, state) {
//                   final email = state.extra as String?;
//                   return ResetPasswordScreen(email: email ?? '');
//                 },
//               ),
//               GoRoute(
//                 path: AppRoutes.authForgotPasswordPath,
//                 name: AppRoutes.authForgotPasswordScreen,
//                 builder: (context, state) => const ForgotPasswordScreen(),
//               ),
//               GoRoute(
//                 path: AppRoutes.authCheckOtpPath,
//                 name: AppRoutes.authCheckOtpScreen,
//                 builder: (context, state) {
//                   final params = state.extra as Map<String, dynamic>? ?? {};
//                   return CheckOtpScreen(
//                     email: params['email'] as String? ?? '',
//                     isFromForgotPassword:
//                         params['isFromForgotPassword'] as bool? ?? false,
//                   );
//                 },
//               ),
//             ],
//           ),
//         ],
//       ),

//       // Common routes
//       GoRoute(
//         path: '${AppRoutes.commonBase}/${AppRoutes.commonWebViewPath}',
//         name: AppRoutes.commonWebViewScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => state.extra! as CommonWebView,
//       ),

//       // Booking routes
//       GoRoute(
//         path: AppRoutes.bookingShipmentConfirmationPath,
//         name: AppRoutes.bookingShipmentConfirmationScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return ShipmentConfirmationScreen(
//             bookingProvider: params['bookingProvider'] as BookingProvider? ??
//                 BookingProvider([]),
//             homeProvider:
//                 params['homeProvider'] as HomeProvider? ?? HomeProvider(),
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.bookingPaymentPath,
//         name: AppRoutes.bookingPaymentScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return PaymentScreen(
//             bookingProvider: params['bookingProvider'] as BookingProvider? ??
//                 BookingProvider([]),
//             homeProvider:
//                 params['homeProvider'] as HomeProvider? ?? HomeProvider(),
//             requestedTripsProvider:
//                 params['requestedTripsProvider'] as RequestedTripsProvider? ??
//                     RequestedTripsProvider(),
//             isExclusiveTrip: params['isExclusiveTrip'] as bool? ?? false,
//           );
//         },
//       ),

//       // Bottom bar shell route
//       StatefulShellRoute.indexedStack(
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state, navigationShell) {
//           return BottomBarScreen(navigationShell: navigationShell);
//         },
//         branches: [
//           // Home branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.homeBase,
//                 builder: (context, state) => const HomeScreen(),
//               ),
//             ],
//           ),
//           // Trips branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.tripsBase,
//                 builder: (context, state) => const MyTripScreen(),
//               ),
//             ],
//           ),
//           // Notification branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.notificationBase,
//                 builder: (context, state) => const NotificationsScreen(),
//               ),
//             ],
//           ),
//           // Profile branch
//           StatefulShellBranch(
//             routes: [
//               GoRoute(
//                 path: AppRoutes.profileBase,
//                 builder: (context, state) => const ProfileScreen(),
//               ),
//             ],
//           ),
//         ],
//       ),

//       // Nested child routes (full-screen, no bottom bar)
//       GoRoute(
//         path: AppRoutes.homeTransporterListPath,
//         name: AppRoutes.homeTransporterListScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => state.extra! as TransporterListScreen,
//       ),
//       GoRoute(
//         path: AppRoutes.homeEmptyTransporterPath,
//         name: AppRoutes.homeEmptyTransporterScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => state.extra! as EmptyTransporterWidget,
//       ),
//       GoRoute(
//         path: AppRoutes.homeUserInfoPath,
//         name: AppRoutes.homeUserInfoScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => state.extra! as UserInfoScreen,
//       ),
//       GoRoute(
//         path: AppRoutes.homeAddressSearchPath,
//         name: AppRoutes.homeAddressSearchScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => state.extra! as AddressSearchScreen,
//       ),
//       GoRoute(
//         path: AppRoutes.tripsTripDetailsPath,
//         name: AppRoutes.tripsTripDetailsScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return TripDetailsScreen(
//             isCompleted: params['isCompleted'] as bool? ?? false,
//             id: params['id'] as String? ?? '',
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsChatPath,
//         name: AppRoutes.tripsChatScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => state.extra! as ChatScreen,
//       ),
//       GoRoute(
//         path: AppRoutes.tripsCarPaymentDetailPath,
//         name: AppRoutes.tripsCarPaymentDetailScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return CarPaymentDetailScreen(
//             bookingDetail: params['bookingDetail'] as BookingDetail?,
//             paymentData: params['paymentData'] as TransporterModel?,
//             tripDetailProvider:
//                 params['tripDetailProvider'] as TripDetailProvider,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsChecklistPath,
//         name: AppRoutes.tripsChecklistScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return ChecklistScreen(
//             clientName: params['clientName'] as String? ?? '',
//             carId: params['carId'] as String? ?? '',
//             checkListId: params['checkListId'] as String?,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsRemainPaymentPath,
//         name: AppRoutes.tripsRemainPaymentScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return RemainPaymentScreen(
//             tripDetailProvider:
//                 params['tripDetailProvider'] as TripDetailProvider,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsUserInfoPath,
//         name: AppRoutes.tripsUserInfoScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return TripUserInfoPage(
//             tripDetailProvider:
//                 params['tripDetailProvider'] as TripDetailProvider,
//             isEdit: params['isEdit'] as bool? ?? false,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsRequestedTripsPath,
//         name: AppRoutes.tripsRequestedTripsScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return ChangeNotifierProvider(
//             create: (context) =>
//                 params['requestedTripsProvider'] as RequestedTripsProvider? ??
//                 RequestedTripsProvider(),
//             builder: (context, child) {
//               return RequestedTripsScreen(
//                 value: params['value'] as int? ?? 0,
//                 id: params['id'] as String? ?? '0',
//                 requestedTripsProvider: context.read<RequestedTripsProvider>(),
//               );
//             },
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.carInfoPath,
//         name: AppRoutes.carInfoSheet,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return CarInfoSheet(
//             carDetail: params['carDetail'] as CarDetailModel,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsAuctionPath,
//         name: AppRoutes.tripsAuctionScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => const AuctionScreen(),
//       ),
//       GoRoute(
//         path: AppRoutes.tripsAddNotePath,
//         name: AppRoutes.tripsAddNoteScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return AddNotePage(
//             tripDetailProvider:
//                 params['tripDetailProvider'] as TripDetailProvider,
//             id: params['id'] as String? ?? '',
//             note: params['note'] as NoteModel?,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsEditRestedTripPath,
//         name: AppRoutes.tripsEditRestedTripScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           final data = params['data'] as TripModel? ?? TripModel();
//           return EditRestedTripScreen(data: data);
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsRatePath,
//         name: AppRoutes.tripsRateScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return RatingScreen(
//             tripId: params['tripId'] as String? ?? '',
//             providerId: params['providerId'] as String? ?? '',
//             bookingDetail: params['bookingDetail'] as String? ?? '',
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsRestedRequestedTripsPath,
//         name: AppRoutes.tripsRestedRequestedTripsScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return ChangeNotifierProvider(
//             create: (context) =>
//                 params['requestedTripsProvider'] as RequestedTripsProvider? ??
//                 RequestedTripsProvider(),
//             builder: (context, child) {
//               return RestedRequestedTripsScreen(
//                 value: params['value'] as int? ?? 2,
//                 id: params['id'] as String? ?? '0',
//                 index: params['index'] as int?,
//                 requestedTripsProvider: context.read<RequestedTripsProvider>(),
//               );
//             },
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.tripsTripTransporterListPath,
//         name: AppRoutes.tripsTripTransporterListScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final params = state.extra as Map<String, dynamic>? ?? {};
//           return TripTransporterListPage(
//             requestedTripsProvider:
//                 params['requestedTripsProvider'] as RequestedTripsProvider,
//           );
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.profileEditPath,
//         name: AppRoutes.profileEditScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => const EditProfileScreen(),
//       ),
//       GoRoute(
//         path: AppRoutes.profileSetNewPasswordPath,
//         name: AppRoutes.profileSetNewPasswordScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) {
//           final email = state.extra as String?;
//           return SetNewPasswordScreen(email: email ?? '');
//         },
//       ),
//       GoRoute(
//         path: AppRoutes.profileCustomerSupportPath,
//         name: AppRoutes.profileCustomerSupportScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => const CustomerSupportScreen(),
//       ),
//       GoRoute(
//         path: AppRoutes.profileCustomerDetailPath,
//         name: AppRoutes.profileCustomerDetailScreen,
//         parentNavigatorKey: rootNavKey,
//         builder: (context, state) => const CustomerDetailScreen(),
//       ),
//     ],
//     redirect: (context, state) => null,
//     errorBuilder: (context, state) => Scaffold(
//       body: Center(child: Text('Error: ${state.error}')),
//     ),
//   );
// }
