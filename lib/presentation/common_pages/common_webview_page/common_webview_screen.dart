import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/provider/webview_provider.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CommonWebViewScreen extends StatelessWidget {
  const CommonWebViewScreen({super.key, required this.commonWebViewParams});
  final CommonWebViewParams commonWebViewParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ChangeNotifierProvider(
        create: (context) => WebViewProvider(commonWebViewParams),
        builder: (context, child) {
          final webViewProvider = Provider.of<WebViewProvider>(context);
          return ValueListenableBuilder(
            valueListenable: webViewProvider.isWebViewShowLoader,
            builder: (context, isWebViewShowLoader, mainChild) =>
                ValueListenableBuilder(
                  valueListenable: webViewProvider.isShowLoader,
                  builder: (context, isShowLoader, child) {
                    return AppLoader(
                      isShowLoader: isShowLoader || isWebViewShowLoader,
                      child: mainChild!,
                    );
                  },
                ),
            child: Selector<WebViewProvider, WebViewController?>(
              selector: (p0, webViewProvider) =>
                  webViewProvider.webViewController,
              builder: (context, webviewController, child) {
                return SafeArea(
                  child: webviewController != null
                      ? WebViewWidget(controller: webviewController)
                      : const SizedBox(),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
