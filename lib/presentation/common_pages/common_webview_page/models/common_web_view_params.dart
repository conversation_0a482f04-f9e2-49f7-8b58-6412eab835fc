import 'package:flutter/material.dart';

class CommonWebViewParams {
  const CommonWebViewParams({
    this.bodyData,
    required this.context,
    this.url,
    this.bookingId,
    this.isExclusiveTrip = false,
    this.isFromHome = false,
    this.isRemainAmountAPI = false,
  });

  final bool isExclusiveTrip;
  final bool isFromHome;
  final String? url;
  final String? bookingId;
  final bool isRemainAmountAPI;
  final BuildContext context;
  final Map<String, dynamic>? bodyData;
}
