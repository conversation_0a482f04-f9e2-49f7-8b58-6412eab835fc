import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
//

class InitialScreen extends StatelessWidget {
  const InitialScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Lottie.asset(
          AppAssets.animationLoaderAppLoader.path,
          height: AppSize.h100,
        ),
      ),
    );
  }
}
