import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';

class StockLocationsParams {
  /// Constructor
  const StockLocationsParams({
    required this.homeProvider,
    required this.transporterDetail,
    required this.bookingSessionId,
    required this.oldId,
  });
  final HomeProvider homeProvider;
  final List<TransporterDetailModel> transporterDetail;
  final String bookingSessionId;
  final int? oldId;
}
