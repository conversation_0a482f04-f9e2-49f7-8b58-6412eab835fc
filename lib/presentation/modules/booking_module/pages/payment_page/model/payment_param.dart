import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';

class PaymentParam {
  const PaymentParam({
    required this.isExclusiveTrip,
    required this.bookingId,
    required this.paymentDataModel,
    required this.bookingProviderData,
  }) : assert(
          bookingProviderData != null || paymentDataModel != null,
          'Please pass payment and booking data',
        );
  final bool isExclusiveTrip;
  final String? bookingId;
  final PaymentDataModel? paymentDataModel;
  final ProviderData? bookingProviderData;
}
