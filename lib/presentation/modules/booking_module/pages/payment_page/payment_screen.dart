import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/num_extension.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/provider/payment_provider.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/widgets/payment_summary_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Payment page
class PaymentScreen extends StatelessWidget {
  /// Constructor
  const PaymentScreen({
    super.key,
    required this.isExclusiveTrip,
    required this.bookingId,
    required this.paymentDataModel,
    required this.bookingProviderData,
  }) : assert(
         bookingProviderData != null || paymentDataModel != null,
         'Please pass payment and booking data',
       );
  final bool isExclusiveTrip;
  final String? bookingId;
  final PaymentDataModel? paymentDataModel;
  final ProviderData? bookingProviderData;

  @override
  Widget build(BuildContext context) {
    final paymentData = paymentDataModel;
    return ChangeNotifierProvider(
      create: (context) => PaymentProvider(paymentData),
      builder: (context, child) {
        final paymentProvider = context.watch<PaymentProvider>();
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.payments),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: AppButton(
            onPressed: () => paymentProvider.navigateToWebViewForPayment(
              context,
              bookingData: bookingProviderData,
              bookingId: bookingId,
              isExclusiveTrip: isExclusiveTrip,
            ),
            text: '${paymentProvider.netTotalAmount}'.smartFormat(),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: AppSize.appPadding,
            ).add(EdgeInsets.only(bottom: AppSize.h16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: AppSize.h8,
              children: [
                ...paymentData?.paymentSummary?.map(
                      (e) => Padding(
                        padding: EdgeInsets.only(bottom: AppSize.h8),
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(AppSize.r8),
                            color: Colors.white,
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(AppSize.h16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  e.companyName ?? '',
                                  style: context.textTheme.titleMedium
                                      ?.copyWith(
                                        fontSize: AppSize.sp18,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                                SummaryRow(
                                  title: context.l10n.actualTransportationCost,
                                  amount:
                                      e.actualTransportationChargeForCustomer
                                          ?.toString()
                                          .smartFormat() ??
                                      '',
                                ),
                                SummaryRow(
                                  title: context.l10n.extraTransportationCost,
                                  amount:
                                      e.extraTransportationChargeForCustomer
                                          ?.toString()
                                          .smartFormat() ??
                                      '',
                                ),
                                if (e
                                    .extraTransportationChargeForCustomer
                                    .isNotNullNotZero)
                                  Text(
                                    context.l10n.exclusiveTripExtraCostNote,
                                    style: TextStyle(
                                      fontSize: AppSize.sp10,
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.red,
                                    ),
                                  ),
                                SummaryRow(
                                  title: context.l10n.transportationCost,
                                  amount:
                                      e.transportationCharge
                                          ?.toString()
                                          .smartFormat() ??
                                      '',
                                ),
                                // SummaryRow(
                                //   title: context.l10n.dropOffStorageFee,
                                //   amount:
                                //       e.customerLocationToStartLocationServiceCharge
                                //               ?.toString()
                                //               .smartFormat() ??
                                //           '',
                                // ),
                                // SummaryRow(
                                //   title: context.l10n.pickupStorageFee,
                                //   amount: e.startStopLocationStorageCharge
                                //           ?.toString()
                                //           .smartFormat() ??
                                //       '',
                                // ),
                                SummaryRow(
                                  title: context.l10n.insuranceCost,
                                  amount:
                                      e.insuranceCharge
                                          ?.toString()
                                          .smartFormat() ??
                                      '',
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ) ??
                    [],
                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(AppSize.h16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.l10n.summary,
                          style: context.textTheme.titleLarge,
                        ),
                        Gap(AppSize.h8),
                        SummaryRow(
                          title: context.l10n.transportationCost,
                          amount:
                              paymentData?.totalTransportationCharge
                                  ?.toString()
                                  .smartFormat() ??
                              '',
                        ),
                        SummaryRow(
                          title: context.l10n.dropOffStorageFee,
                          amount:
                              paymentData?.totalStartLocationStorageCharge
                                  ?.toString()
                                  .smartFormat() ??
                              '',
                        ),
                        SummaryRow(
                          title: context.l10n.pickupStorageFee,
                          amount:
                              paymentData?.totalEndLocationStorageCharge
                                  ?.toString()
                                  .smartFormat() ??
                              '',
                        ),
                        if (paymentData
                                ?.totalInsuranceCharge
                                ?.isNotNullNotZero ??
                            false)
                          SummaryRow(
                            title: context.l10n.totalInsuranceCost,
                            amount:
                                paymentData?.totalInsuranceCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),
                        SummaryRow(
                          title: context.l10n.serviceFee,
                          amount:
                              paymentData?.totalAppFee
                                  ?.toString()
                                  .smartFormat() ??
                              '',
                        ),
                        if (paymentData
                                ?.totalCustomerLocationToStartStopLocationServiceCharge
                                .isNotNullNotZero ??
                            false)
                          SummaryRow(
                            title: context.l10n.dropTransportation,
                            amount:
                                paymentData
                                    ?.totalCustomerLocationToStartStopLocationServiceCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(AppSize.h16),
                  margin: EdgeInsets.only(top: AppSize.h8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.l10n.total,
                        style: context.textTheme.titleLarge,
                      ),
                      Flexible(
                        child: MarqueeWidget(
                          child: Text(
                            '${paymentProvider.totalAmount}'.smartFormat(),
                            style: context.textTheme.titleLarge,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Builder(
                  builder: (context) {
                    final pay =
                        (paymentProvider.netTotalAmount /
                            paymentProvider.totalAmount) *
                        100;
                    final remain = 100 - pay;
                    return Text(
                      context.l10n
                          .noteUHaveToPay(
                            pay.toStringAsFixed(1),
                            remain.toStringAsFixed(1),
                          )
                          .smartFormat(),
                      style: context.textTheme.bodySmall?.copyWith(
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
