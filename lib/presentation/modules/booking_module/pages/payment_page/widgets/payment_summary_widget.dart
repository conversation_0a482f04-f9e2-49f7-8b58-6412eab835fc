import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_size.dart';

/// Payment summary row
class SummaryRow extends StatelessWidget {
  /// Constructor
  const SummaryRow({required this.title, required this.amount, super.key});

  /// Title
  final String title;

  /// Amount
  final String amount;

  @override
  Widget build(BuildContext context) {
    return amount.isEmpty
        ? const SizedBox()
        : Padding(
            padding: EdgeInsets.only(top: AppSize.h8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: AppSize.w10,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                ),
                Text(
                  amount,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: AppSize.sp16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          );
  }
}
