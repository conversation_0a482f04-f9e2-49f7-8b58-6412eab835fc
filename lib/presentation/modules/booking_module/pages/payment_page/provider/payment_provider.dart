// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_common_functions.dart';

class PaymentProvider extends ChangeNotifier {
  PaymentProvider(PaymentDataModel? paymentData) {
    totalAmount = paymentData?.totalBookingCost ?? 0;
    netTotalAmount = paymentData?.totalNetBookingPay ?? 0;
  }

  num totalAmount = 0;
  num netTotalAmount = 0;

  Future<void> navigateToWebViewForPayment(
    BuildContext context, {
    bool isExclusiveTrip = false,
    required String? bookingId,
    required ProviderData? bookingData,
  }) async =>
      AppCommonFunctions.getDeviceId().then(
        (deviceId) => AppNavigationService.pushNamed(
          context,
          AppRoutes.commonWebViewScreen,
          extra: CommonWebViewParams(
            context: context,
            bodyData: bookingData?.toJson(),
            isExclusiveTrip: isExclusiveTrip,
            isFromHome: true,
            bookingId: bookingId,
          ),
        ),
      );
}
