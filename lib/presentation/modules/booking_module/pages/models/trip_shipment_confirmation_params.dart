import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';

class TripShipmentConfirmationParams {
  /// Constructor
  const TripShipmentConfirmationParams({
    required this.transporterDetailList,
    required this.bookingSessionId,
    required this.providerList,
    this.pickupTime,
    this.dropTime,
    this.selectedTrip,
  });
  final String? pickupTime;
  final String? dropTime;
  final List<TransporterDetailModel> transporterDetailList;
  final String bookingSessionId;
  final List<ProviderListData> providerList;
  final TripModel? selectedTrip;
}
