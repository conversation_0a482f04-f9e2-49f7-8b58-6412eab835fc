import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';

class ReqTransporterDetailModel {
  const ReqTransporterDetailModel({
    required this.trip,
    required this.isDisclaimerAccepted,
    required this.carDetails,
    required this.providerPickUpLocation,
  });

  factory ReqTransporterDetailModel.fromJson(Map<String, dynamic> json) =>
      ReqTransporterDetailModel(
        trip: json['trip'] as int,
        isDisclaimerAccepted: json['is_disclaimer_accepted'] as bool,
        carDetails:
            (json['car_details'] as List<dynamic>?)
                ?.map(
                  (e) => ReqSharedCarDetailModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList() ??
            [],
        providerPickUpLocation: json['provider_pick_up_location'] == null
            ? null
            : DateTime.parse(json['provider_pick_up_location'] as String),
      );

  final int trip;
  final bool isDisclaimerAccepted;
  final List<CommonCarDetailModel> carDetails;
  final DateTime? providerPickUpLocation;

  Map<String, dynamic> toJson() => {
    'trip': trip,
    'is_disclaimer_accepted': isDisclaimerAccepted,
    'car_details': List<dynamic>.from(carDetails.map((x) => x.toJson())),
  };
}

abstract class CommonCarDetailModel {
  const CommonCarDetailModel({
    this.serialNumber,
    this.car,
    this.isWinchRequired = false,
    this.isInsuranceIncluded = false,
    this.insurance,
    this.carDescription,
    this.imageList,
  });

  final String? serialNumber;
  final int? car;
  final bool isWinchRequired;
  final bool isInsuranceIncluded;
  final String? insurance;
  final String? carDescription;
  final List<String>? imageList;

  Map<String, dynamic> toJson() => {
    if (serialNumber != null) 'serial_number': serialNumber,
    if (car != null) 'car': car,
    if (isInsuranceIncluded) 'is_insured': isInsuranceIncluded,
    if (insurance != null && insurance!.isNotEmpty) 'insurance': insurance,
    if (isWinchRequired) 'is_winch_required': isWinchRequired,
    if (carDescription != null) 'car_description': carDescription,
    if (imageList?.isNotEmpty ?? false)
      'aws_image_keys': imageList?.map((e) => e).toList(),
  };
}

class ReqSharedCarDetailModel implements CommonCarDetailModel {
  const ReqSharedCarDetailModel({
    required this.serialNumber,
    required this.car,
    this.isWinchRequired = false,
    this.isInsuranceIncluded = false,
    this.insurance,
    this.imageList,
    this.carDescription,
    this.isCarPickedUp = false,
    this.pickUpServiceAndDropOffService,
    this.dropOffDate,
    this.distanceFromCustomerLocationToStopLocation,
    this.fromCarToBePickedUpLocation,
  });

  factory ReqSharedCarDetailModel.fromJson(
    Map<String, dynamic> json,
  ) => ReqSharedCarDetailModel(
    serialNumber: json['serial_number'] as String?,
    car: json['car'] as int?,
    isWinchRequired: json['is_winch_required'] as bool? ?? false,
    isInsuranceIncluded: json['is_insured'] as bool? ?? false,
    insurance: json['insurance'] as String?,
    carDescription: json['car_description'] as String?,
    isCarPickedUp: json['is_car_picked_up_to_stop_location'] as bool? ?? false,
    imageList: json['aws_image_keys'] != null
        ? List<String>.from(
            (json['aws_image_keys'] as List<dynamic>).map((e) => e as String),
          )
        : null,
    pickUpServiceAndDropOffService:
        json['pick_up_service_and_drop_off_service'] as int?,
    dropOffDate: json['customer_drop_date_at_stop_location'] != null
        ? DateTime.parse(json['customer_drop_date_at_stop_location'] as String)
        : null,
    distanceFromCustomerLocationToStopLocation:
        json['distance_from_customer_location_to_stop_location'] as double?,
    fromCarToBePickedUpLocation:
        json['from_car_to_be_picked_up_location'] != null
        ? AddressModel.fromJson(
            json['from_car_to_be_picked_up_location'] as Map<String, dynamic>,
          )
        : null,
  );

  @override
  final String? serialNumber;
  @override
  final int? car;
  @override
  final bool isWinchRequired;
  @override
  final bool isInsuranceIncluded;
  @override
  final String? insurance;
  @override
  final String? carDescription;
  @override
  final List<String>? imageList;
  final bool isCarPickedUp;
  final int? pickUpServiceAndDropOffService;
  final double? distanceFromCustomerLocationToStopLocation;
  final DateTime? dropOffDate;
  final AddressModel? fromCarToBePickedUpLocation;

  @override
  Map<String, dynamic> toJson() => {
    if (serialNumber != null) 'serial_number': serialNumber,
    if (car != null) 'car': car,
    if (isInsuranceIncluded) 'is_insured': isInsuranceIncluded,
    if (insurance != null && insurance!.isNotEmpty) 'insurance': insurance,
    if (dropOffDate != null)
      'customer_drop_date_at_stop_location': dropOffDate
          ?.toUtc()
          .passDateFormateWithTime,
    if (isCarPickedUp) 'is_car_picked_up_to_stop_location': isCarPickedUp,
    if (isCarPickedUp)
      'pick_up_service_and_drop_off_service': pickUpServiceAndDropOffService,
    if (fromCarToBePickedUpLocation != null && isCarPickedUp)
      'from_car_to_be_picked_up_location': fromCarToBePickedUpLocation
          ?.toJson(),
    if (isCarPickedUp && distanceFromCustomerLocationToStopLocation != 0)
      'distance_from_customer_location_to_stop_location':
          distanceFromCustomerLocationToStopLocation,
    if (isWinchRequired) 'required_winch': isWinchRequired,
    if (imageList?.isNotEmpty ?? false)
      'aws_image_keys': imageList?.map((e) => e).toList(),
    if (carDescription != null) 'car_description': carDescription,
  };
}

class ReqExclusiveCarDetailModel implements CommonCarDetailModel {
  const ReqExclusiveCarDetailModel({
    required this.serialNumber,
    required this.car,
    this.isWinchRequired = false,
    this.isInsuranceIncluded = false,
    this.insurance,
    this.carDescription,
    this.imageList,
  });

  factory ReqExclusiveCarDetailModel.fromJson(Map<String, dynamic> json) =>
      ReqExclusiveCarDetailModel(
        serialNumber: json['serial_number'] as String?,
        car: json['car'] as int?,
        isWinchRequired: json['is_winch_required'] as bool? ?? false,
        isInsuranceIncluded: json['is_insured'] as bool? ?? false,
        insurance: json['insurance'] as String?,
        carDescription: json['car_description'] as String?,
        imageList: json['aws_image_keys'] != null
            ? List<String>.from(
                (json['aws_image_keys'] as List<dynamic>).map(
                  (e) => e as String,
                ),
              )
            : null,
      );

  @override
  final String? serialNumber;
  @override
  final int? car;
  @override
  final bool isWinchRequired;
  @override
  final bool isInsuranceIncluded;
  @override
  final String? insurance;
  @override
  final String? carDescription;
  @override
  final List<String>? imageList;

  @override
  Map<String, dynamic> toJson() => {
    if (serialNumber != null) 'serial_number': serialNumber,
    if (car != null) 'car': car,
    if (isInsuranceIncluded) 'is_insured': isInsuranceIncluded,
    if (insurance != null && insurance!.isNotEmpty) 'insurance': insurance,
    if (isWinchRequired) 'is_winch_required': isWinchRequired,
    if (carDescription != null) 'car_description': carDescription,
    if (imageList?.isNotEmpty ?? false)
      'aws_image_keys': imageList?.map((e) => e).toList(),
  };
}
