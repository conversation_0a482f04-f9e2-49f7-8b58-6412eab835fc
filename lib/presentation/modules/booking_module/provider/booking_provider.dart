// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/models/requested_booking_model.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/model/payment_param.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/insurance_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/rest_api.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/logger.dart';

class BookingProvider extends ChangeNotifier {
  BookingProvider(
    List<TransporterDetailModel> transporterDetail, {
    this.bookingSessionId,
    this.oldIdValue,
  }) {
    transporterDetailList.value.clear();
    transporterDetailList.value.addAll(transporterDetail);
    for (var i = 0; i < transporterDetailList.value.length; i++) {
      for (
        var j = 0;
        j < transporterDetailList.value[i].carDetails.length;
        j++
      ) {
        transporterDetailList.value[i].carDetails[j]
          ..insurance = ''
          ..isInsuranceIncluded = false
          ..dropOffDate = null;
      }
    }
    for (final element in transporterDetailList.value) {
      for (final car in element.carDetails) {
        car
          ..isInsuranceIncluded = false
          ..insurance = '';
      }
    }
  }

  /// is close variable
  bool _isClosed = false;

  /// cancel token
  CancelToken? getBookingCancelToken;
  CancelToken? getInsuranceToken;
  CancelToken? getPaymentToken;
  CancelToken? generateImgToken;

  final isShowLoader = ValueNotifier<bool>(false);
  final isDisclaimerAccepted = ValueNotifier<bool>(false);
  final paymentData = ValueNotifier<PaymentDataModel?>(null);
  String? bookingSessionId;
  int? oldIdValue;
  final userDetailData =
      Injector.instance<AppDB>().userModel?.user?.userDetailData;

  ValueNotifier<List<InsuranceModel>> insuranceList = ValueNotifier([]);

  /// Trip repository
  final homeRepository = Injector.instance<HomeRepository>();
  ValueNotifier<List<TransporterDetailModel>> transporterDetailList =
      ValueNotifier([]);

  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// to get full address
  String fullAddress(AddressModel? address) =>
      '${address?.street?.addComma()}'
      '${address?.city?.addComma()}${address?.state?.addComma(isLast: true)} '
      '${address?.postalCode?.addComma()}${address?.country?.addComma(isLast: true)}';

  /// to get payment request data from ahead
  /// provider and vehicle list for exclusive trip only
  ProviderData getExclusiveProviderData(TripModel tripData, String deviceId) {
    return ProviderData(
      customerStartDate:
          tripData.customerStartDate?.toLocal() ?? DateTime.now(),
      customerEndDate: tripData.customerEndDate?.toLocal() ?? DateTime.now(),
      allVehicleInOneTrip: false,
      bookingCountryCode: userDetailData?.bookingCountryCode,
      bookingNumber: userDetailData?.bookingNumber,
      startStopLocation: tripData.startStopLocation?.id,
      endStopLocation: tripData.endStopLocation?.id,
      transporterDetails: List.generate(
        transporterDetailList.value.length,
        (index) => ReqTransporterDetailModel(
          isDisclaimerAccepted: isDisclaimerAccepted.value,
          trip: transporterDetailList.value[index].trip,
          carDetails: transporterDetailList.value[index].carDetails
              .map(
                (car) => ReqExclusiveCarDetailModel(
                  serialNumber: car.serialNumber,
                  car: car.car,
                  isWinchRequired: car.isWinchRequired,
                  isInsuranceIncluded: car.isInsuranceIncluded,
                  insurance: car.insurance,
                  carDescription: car.carDescription,
                  imageList: car.awsImageKeys,
                ),
              )
              .toList(),
          providerPickUpLocation:
              transporterDetailList.value[index].providerPickUpLocation,
        ),
      ),
      deviceId: deviceId,
      bookingSessionId: bookingSessionId,
      booking: tripData.id?.toString(),
    );
  }

  /// to get payment request data from ahead
  /// provider and vehicle list for shared trip only
  ProviderData getSharedProviderData(
    HomeProvider homeProvider,
    String deviceId,
  ) {
    return ProviderData(
      customerStartDate:
          homeProvider.pickupDate.value?.toLocal() ?? DateTime.now(),
      oldId: oldIdValue,
      bookingCountryCode: userDetailData?.bookingCountryCode,
      bookingNumber: userDetailData?.bookingNumber,
      customerEndDate:
          homeProvider.deliveryDate.value?.toLocal() ?? DateTime.now(),
      allVehicleInOneTrip: homeProvider.isInAllVehicle.value,
      startStopLocation: homeProvider.selectedOriginStockLocation.value?.id
          ?.toInt(),
      endStopLocation: homeProvider.selectedDropStockLocation.value?.id
          ?.toInt(),
      transporterDetails: List.generate(
        transporterDetailList.value.length,
        (index) => ReqTransporterDetailModel(
          trip: transporterDetailList.value[index].trip,
          isDisclaimerAccepted: isDisclaimerAccepted.value,
          carDetails: transporterDetailList.value[index].carDetails
              .map(
                (car) => ReqSharedCarDetailModel(
                  serialNumber: car.serialNumber,
                  car: car.car,
                  isWinchRequired: car.isWinchRequired,
                  isInsuranceIncluded: car.isInsuranceIncluded,
                  insurance: car.insurance,
                  carDescription: car.carDescription,
                  isCarPickedUp: car.isCarPickedUp,
                  pickUpServiceAndDropOffService:
                      car.pickUpServiceAndDropOffService,
                  dropOffDate: car.dropOffDate,
                  distanceFromCustomerLocationToStopLocation: car
                      .totalDistanceFromCustomerLocationToStopLocation
                      .twoDecimal,
                  fromCarToBePickedUpLocation: car.fromCarToBePickedUpLocation,
                  imageList: car.awsImageKeys,
                ),
              )
              .toList(),
          providerPickUpLocation:
              transporterDetailList.value[index].providerPickUpLocation,
        ),
      ),
      deviceId: deviceId,
      bookingSessionId: bookingSessionId,
    );
  }

  /// get payment information
  Future<void> getPayment(
    BuildContext context, {
    HomeProvider? homeProvider,
    TripModel? selectedTrip,
  }) async {
    if (_isClosed) return;
    isShowLoader.value = true;

    if (transporterDetailList.value.any(
      (element) => element.carDetails.any(
        (car) => car.isInsuranceIncluded && (car.insurance?.isEmpty ?? true),
      ),
    )) {
      context.l10n.pleaseSelectInsurance.showErrorAlert();
      return;
    }

    for (var i = 0; i < transporterDetailList.value.length; i++) {
      for (
        var j = 0;
        j < transporterDetailList.value[i].carDetails.length;
        j++
      ) {
        final imageList =
            transporterDetailList.value[i].carDetails[j].fileImage;
        generateImgToken?.cancel();
        generateImgToken = CancelToken();
        await AppCommonFunctions.generateEmptyListImages(
          imgList: imageList?.map(File.new).toList() ?? [],
          generateImgToken: generateImgToken,
          isClosed: _isClosed,
          whenLoaderChange: (isLoader) {},
        )?.then(
          (value) =>
              transporterDetailList.value[i].carDetails[j].awsImageKeys = value,
        );
      }
    }

    getPaymentToken?.cancel();
    getPaymentToken = CancelToken();
    final deviceId = await AppCommonFunctions.getDeviceId();

    final bodyData = homeProvider != null
        ? getSharedProviderData(homeProvider, deviceId)
        : getExclusiveProviderData(selectedTrip!, deviceId);
    '====>>>>'.logE;
    bodyData.toJson().logE;
    try {
      if (_isClosed) return;
      isShowLoader.value = true;
      final response = await homeRepository.getPayment(
        ApiRequest(
          path: selectedTrip != null
              ? EndPoints.getExclusivePaymentSummary
              : EndPoints.getPayment,
          data: bodyData.toJson(),
          cancelToken: getPaymentToken,
        ),
      );
      isShowLoader.value = false;
      response.when(
        success: (data) {
          if (_isClosed || (getPaymentToken?.isCancelled ?? true)) return;

          paymentData.value = data;
          AppNavigationService.pushNamed(
            context,
            AppRoutes.bookingPaymentScreen,
            extra: PaymentParam(
              isExclusiveTrip: selectedTrip != null,
              paymentDataModel: data,
              bookingProviderData: bodyData,
              bookingId: bodyData.booking,
            ),
          );
          notify();
        },
        error: (error) {
          if (_isClosed || (getPaymentToken?.isCancelled ?? true)) return;
          if (error.code == 308) {
            AppNavigationService.pushAndRemoveAllPreviousRoute(
              context,
              AppRoutes.homeBase,
              isBaseRoute: true,
            );
          }
          '===>>>>> ${error.code}'.logE;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getPaymentToken?.isCancelled ?? true)) return;
      'getPayment error: $e'.logE;
    }
  }

  Future<void> getInsurance() async {
    if (_isClosed || insuranceList.value.isNotEmpty) return;

    getInsuranceToken?.cancel();
    getInsuranceToken = CancelToken();
    isShowLoader.value = true;
    try {
      final response = await homeRepository.getInsurance(
        ApiRequest(
          path: EndPoints.getInsurance,
          cancelToken: getInsuranceToken,
        ),
      );
      response.when(
        success: (data) {
          isShowLoader.value = false;
          if (_isClosed || (getInsuranceToken?.isCancelled ?? true)) return;
          insuranceList.value = data;
        },
        error: (error) {
          isShowLoader.value = false;
          if (_isClosed || (getInsuranceToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getInsuranceToken?.isCancelled ?? true)) return;
      'getInsurance error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    getPaymentToken?.cancel();
    getBookingCancelToken?.cancel();
    getInsuranceToken?.cancel();
    super.dispose();
  }
}
