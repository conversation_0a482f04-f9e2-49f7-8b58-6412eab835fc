import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/widgets/contact_detail_widget/provider/contact_detail_widget_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/mobile_text_filed.dart';

class ContactDetailWidget extends StatelessWidget {
  const ContactDetailWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ContactDetailWidgetProvider(),
      child: Builder(
        builder: (context) {
          final contactDetailWidgetProvider =
              Provider.of<ContactDetailWidgetProvider>(context);
          final userDetailData =
              Injector.instance<AppDB>().userModel?.user?.userDetailData;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.l10n.customerDetail,
                style: context.textTheme.titleSmall?.copyWith(
                  fontSize: AppSize.sp16,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: AppSize.h8, bottom: AppSize.h16),
                child: GestureDetector(
                  onTap: () {
                    if (userDetailData
                            ?.bookingCountryCode
                            .isNotEmptyAndNotNull ??
                        false) {
                      contactDetailWidgetProvider.countryCode =
                          userDetailData?.bookingCountryCode ?? '';
                    }
                    if (userDetailData?.bookingNumber?.isNotEmpty ?? false) {
                      contactDetailWidgetProvider.mobileNumberController.text =
                          userDetailData?.bookingNumber ?? '';
                    }
                    if (contactDetailWidgetProvider
                        .mobileNumberController
                        .text
                        .isNotEmpty) {
                      contactDetailWidgetProvider.mobileNumberLength =
                          contactDetailWidgetProvider
                              .mobileNumberController
                              .text
                              .length;
                    }

                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(AppSize.r10),
                          topRight: Radius.circular(AppSize.r10),
                        ),
                      ),
                      builder: (context) => ValueListenableBuilder(
                        valueListenable:
                            contactDetailWidgetProvider.isShowLoader,
                        builder: (context, isShowLoader, child) {
                          return PopScope(
                            onPopInvokedWithResult: (didPop, result) {
                              contactDetailWidgetProvider
                                ..updateDetailCancelToken?.cancel()
                                ..isShowLoader.value = false;
                            },
                            child: AppLoader(
                              isShowLoader: isShowLoader,
                              // isFullScreen: false,
                              child: child!,
                            ),
                          );
                        },
                        child: Padding(
                          padding: EdgeInsets.all(AppSize.sp16).add(
                            EdgeInsets.only(
                              bottom: MediaQuery.viewInsetsOf(context).bottom,
                            ),
                          ),
                          child: Form(
                            key: contactDetailWidgetProvider.contactKey,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  context.l10n.addCustomerDetail,
                                  style: context.textTheme.titleSmall?.copyWith(
                                    fontSize: AppSize.sp16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                ChangeNotifierProvider.value(
                                  value: contactDetailWidgetProvider,
                                  child:
                                      Selector<
                                        ContactDetailWidgetProvider,
                                        (String, int)
                                      >(
                                        selector:
                                            (p0, contactDetailWidgetProvider) =>
                                                (
                                                  contactDetailWidgetProvider
                                                      .countryCode,
                                                  contactDetailWidgetProvider
                                                      .mobileNumberLength,
                                                ),
                                        builder: (context, codeLen, child) {
                                          return MobileTextFiled(
                                            controller:
                                                contactDetailWidgetProvider
                                                    .mobileNumberController,
                                            maxTextLength: codeLen.$2,
                                            countryCode: codeLen.$1,
                                            onSelect:
                                                contactDetailWidgetProvider
                                                    .changeCountry,
                                          );
                                        },
                                      ),
                                ),
                                Text(
                                  context.l10n.noteForMobile,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: AppColors.errorColor,
                                    fontSize: AppSize.sp10,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                Gap(AppSize.h30),
                                Builder(
                                  builder: (context) {
                                    return AppButton(
                                      text: context.l10n.save,
                                      isBottomBtn: false,
                                      onPressed: () {
                                        final form = contactDetailWidgetProvider.contactKey.currentState;
                                        if (form?.validate()??false) {
                                          contactDetailWidgetProvider
                                              .addUpdateUserDetailData(context);
                                        }
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppSize.r4),
                      color: AppColors.white,
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(AppSize.sp16),
                      child: Row(
                        children: [
                          if (userDetailData?.bookingNumber?.isNotEmpty ??
                              false)
                            RichText(
                              text: TextSpan(
                                text: '${context.l10n.mobileNumber}: ',
                                style: context.textTheme.bodySmall?.copyWith(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.ff343A40,
                                ),
                                children: [
                                  TextSpan(
                                    text:
                                        '${userDetailData?.bookingCountryCode}'
                                        ' ${userDetailData?.bookingNumber}',
                                    style: context.textTheme.bodySmall
                                        ?.copyWith(
                                          fontSize: AppSize.sp14,
                                          fontWeight: FontWeight.w700,
                                          color: AppColors.ff67509C,
                                        ),
                                  ),
                                ],
                              ),
                            )
                          else
                            Text(
                              '+ Add your mobile number',
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
