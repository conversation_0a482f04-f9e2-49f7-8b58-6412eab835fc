import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/signup_page/provider/signup_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/utils/validators/password_match_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Signup Screen
class SignupScreen extends StatelessWidget {
  /// Signup Screens
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SignupProvider>(
      create: (context) => SignupProvider(),
      child: Consumer<SignupProvider>(
        builder: (context, signupProvider, _) {
          return Scaffold(
            appBar: const CustomAppBar(
              title: '',
              backgroundColor: AppColors.white,
            ),
            body: GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: signupProvider.isShowLoader,
                builder: (context, isLoading, _) {
                  return AppLoader(
                    isShowLoader: isLoading,
                    child: Form(
                      key: signupProvider.formKeySignup,
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSize.appPadding,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: AppSize.h30,
                                bottom: AppSize.h8,
                              ),
                              child: Text(
                                context.l10n.signUp,
                                style: context.textTheme.headlineLarge
                                    ?.copyWith(fontSize: AppSize.sp24),
                              ),
                            ),
                            Text(
                              context.l10n.pleaseEnterDetails,
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w400,
                                color: AppColors.ff343A40,
                              ),
                            ),
                            Gap(AppSize.h32),

                            //* Name
                            AppTextFormField(
                              hintText: context.l10n.enterYourName,
                              fillColor: AppColors.ffF8F9FA,
                              textAction: TextInputAction.next,
                              title: context.l10n.name,
                              controller: signupProvider.nameController,
                              validator: (p0) => nameValidator().call(p0),
                            ),

                            //* Email
                            AppPadding.symmetric(
                              vertical: AppSize.h24,
                              child: AppTextFormField(
                                hintText: context.l10n.enterYourEmail,
                                textAction: TextInputAction.next,
                                keyboardType: TextInputType.emailAddress,
                                fillColor: AppColors.ffF8F9FA,
                                title: context.l10n.email,
                                controller: signupProvider.emailController,
                                validator: (p0) => emailValidator().call(p0),
                              ),
                            ),

                            //* Password
                            ValueListenableBuilder(
                              valueListenable: signupProvider.isPasswordShow,
                              builder: (context, isPasswordShow, _) {
                                return AppTextFormField(
                                  hintText: context.l10n.enterYourPassword,
                                  fillColor: AppColors.ffF8F9FA,
                                  obscureText:
                                      !signupProvider.isPasswordShow.value,
                                  textAction: TextInputAction.next,
                                  title: context.l10n.password,
                                  controller: signupProvider.passwordController,
                                  validator: (p0) =>
                                      passwordValidator().call(p0),
                                  suffixIcon: GestureDetector(
                                    onTap: () {
                                      signupProvider.isPasswordShow.value =
                                          !signupProvider.isPasswordShow.value;
                                    },
                                    child: Icon(
                                      isPasswordShow
                                          ? Icons.visibility_outlined
                                          : Icons.visibility_off_outlined,
                                      color: AppColors.ff6C757D,
                                    ),
                                  ),
                                );
                              },
                            ),

                            //* Confirm Password
                            AppPadding.symmetric(
                              vertical: AppSize.sp24,
                              child: ValueListenableBuilder(
                                valueListenable:
                                    signupProvider.isConfirmPasswordShow,
                                builder: (context, isConfirmPasswordShow, _) {
                                  return AppTextFormField(
                                    hintText:
                                        context.l10n.enterYourConfirmPassword,
                                    fillColor: AppColors.ffF8F9FA,
                                    obscureText: !signupProvider
                                        .isConfirmPasswordShow
                                        .value,
                                    title: context.l10n.confirmPassword,
                                    controller: signupProvider
                                        .confirmPasswordController,
                                    validator: (value) =>
                                        ConfirmPasswordValidator(
                                          errorText: context
                                              .l10n
                                              .pleaseEnterConfirmPassword,
                                          password: signupProvider
                                              .passwordController
                                              .text
                                              .trim(),
                                        ).call(value),
                                    suffixIcon: GestureDetector(
                                      onTap: () {
                                        signupProvider
                                            .isConfirmPasswordShow
                                            .value = !signupProvider
                                            .isConfirmPasswordShow
                                            .value;
                                      },
                                      child: Icon(
                                        isConfirmPasswordShow
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                        color: AppColors.ff6C757D,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),

                            //* Create an account
                            AppPadding(
                              top: AppSize.h16,
                              bottom: AppSize.h16,
                              child: AppButton(
                                text: context.l10n.createAccount,
                                isBottomBtn: false,
                                onPressed: () {
                                  if (signupProvider.formKeySignup.currentState!
                                      .validate()) {
                                    signupProvider.registerAPICall(
                                      context: context,
                                    );
                                  }
                                },
                              ),
                            ),

                            //* Navigate Login screen
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  context.l10n.alreadyHaveAccount,
                                  textAlign: TextAlign.center,
                                  style: context.textTheme.titleSmall,
                                ),
                                InkWell(
                                  onTap: () {
                                    if (context.canPop()) {
                                      AppNavigationService.pop(context);
                                    } else {
                                      AppNavigationService.pushAndRemoveAllPreviousRoute(
                                        context,
                                        AppRoutes.authBase,
                                        isBaseRoute: true,
                                      );
                                    }
                                  },
                                  child: Text(
                                    ' ${context.l10n.logIn}',
                                    textAlign: TextAlign.center,
                                    style: context.textTheme.titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.w700,
                                          color: AppColors.primaryColor,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                            Gap(AppSize.h10),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
