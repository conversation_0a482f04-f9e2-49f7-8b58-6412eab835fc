import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/check_otp_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

/// Signup Provider
class SignupProvider extends ChangeNotifier {
  final isShowLoader = ValueNotifier<bool>(false);
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final formKeySignup = GlobalKey<FormState>();

  final isPasswordShow = ValueNotifier<bool>(false);
  final isConfirmPasswordShow = ValueNotifier<bool>(false);
  CancelToken? registerCancelToken;
  bool _isClosed = false;

  Future<void> registerAPICall({required BuildContext context}) async {
    if (_isClosed) return;
    try {
      isShowLoader.value = true;
      registerCancelToken?.cancel();
      registerCancelToken = CancelToken();

      final deviceId = await AppCommonFunctions.getDeviceId();

      if (_isClosed) return;
      final registrationId = await AppCommonFunctions.getFcmToken();

      if (_isClosed) return;
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: emailController.text.trim().toLowerCase(),
        ApiKeys.firstName: nameController.text.trim(),
        ApiKeys.password: passwordController.text.trim(),
        ApiKeys.deviceId: deviceId,
        ApiKeys.deviceType: Platform.isAndroid
            ? DeviceType.ANDROID.name.toUpperCase()
            : Platform.isIOS
                ? DeviceType.IOS.name.toUpperCase()
                : 'UNKNOWN',
        ApiKeys.registrationId: registrationId,
        ApiKeys.role: 'CUSTOMER',
      };
      final request = ApiRequest(
        path: EndPoints.signup,
        data: data,
        cancelToken: registerCancelToken,
      );

      if (_isClosed) return;
      final res = await Injector.instance<AccountRepository>().signUp(request);
      await res.when(
        success: (data) async {
          if (_isClosed || (registerCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          context.l10n.accountRegisterSuccessfully.showSuccessAlert();
          await AppNavigationService.pushNamed(
            context,
            AppRoutes.authCheckOtpScreen,
            extra: CheckOtpParams(
              email: emailController.text.trim().toLowerCase(),
            ),
          );
        },
        error: (exception) {
          if (_isClosed || (registerCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (registerCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'registerAPICall error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    registerCancelToken?.cancel();
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    isPasswordShow.dispose();
    isConfirmPasswordShow.dispose();
    isShowLoader.dispose();
    super.dispose();
  }
}
