import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class ResetPasswordProvider extends ChangeNotifier {
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final formKeyResetPassword = GlobalKey<FormState>();

  final isPasswordShow = ValueNotifier(false);
  final isConfirmPasswordShow = ValueNotifier(false);
  final isShowLoader = ValueNotifier(false);
  CancelToken? resetPasswordCancelToken;
  bool _isClosed = false;

  Future<void> resetPasswordAPIcall({required BuildContext context}) async {
    if (_isClosed) return;
    try {
      isShowLoader.value = true;
      resetPasswordCancelToken?.cancel();
      resetPasswordCancelToken = CancelToken();

      Map<String, dynamic> data;
      data = {
        ApiKeys.resetType: PasswordType.FORGOT_PASSWORD.name,
        ApiKeys.password: passwordController.text,
      };
      final request = ApiRequest(
        path: EndPoints.resetPassword,
        data: data,
        cancelToken: resetPasswordCancelToken,
      );

      if (_isClosed) return;
      final res =
          await Injector.instance<AccountRepository>().restPassword(request);
      await res.when(
        success: (data) async {
          if (_isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          context.l10n.passChanged.showSuccessAlert();
          Injector.instance<AppDB>().refreshToken = '';
          Injector.instance<AppDB>().token = '';

          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            context,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        },
        error: (exception) {
          if (_isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'resetPasswordAPIcall error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    resetPasswordCancelToken?.cancel();
    passwordController.dispose();
    confirmPasswordController.dispose();
    isPasswordShow.dispose();
    isConfirmPasswordShow.dispose();
    isShowLoader.dispose();
    super.dispose();
  }
}
