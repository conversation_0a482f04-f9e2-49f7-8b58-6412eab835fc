import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/reset_password_page/provider/reset_password_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/utils/validators/password_match_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class ResetPasswordScreen extends StatelessWidget {
  const ResetPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: '', backgroundColor: AppColors.white),
      body: ChangeNotifierProvider(
        create: (context) => ResetPasswordProvider(),
        child: Consumer<ResetPasswordProvider>(
          builder: (context, resetPasswordProvider, _) {
            return GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: resetPasswordProvider.isShowLoader,
                builder: (context, isLoading, _) {
                  return AppLoader(
                    isShowLoader: isLoading,
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSize.appPadding,
                      ),
                      child: Form(
                        key: resetPasswordProvider.formKeyResetPassword,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: AppSize.h30,
                                bottom: AppSize.h8,
                              ),
                              child: Text(
                                context.l10n.setPassword,
                                style: context.textTheme.headlineLarge
                                    ?.copyWith(fontSize: AppSize.sp24),
                              ),
                            ),
                            Text(
                              context.l10n.setNewPassword,
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w400,
                                color: AppColors.ff343A40,
                              ),
                            ),
                            AppPadding(
                              top: AppSize.h32,
                              bottom: AppSize.h24,
                              child: ValueListenableBuilder(
                                valueListenable:
                                    resetPasswordProvider.isPasswordShow,
                                builder: (context, isPasswordShow, _) {
                                  return AppTextFormField(
                                    hintText: context.l10n.enterYourPassword,
                                    fillColor: AppColors.ffF8F9FA,
                                    title: context.l10n.password,
                                    textAction: TextInputAction.next,
                                    obscureText: !resetPasswordProvider
                                        .isPasswordShow
                                        .value,
                                    controller: resetPasswordProvider
                                        .passwordController,
                                    validator: (p0) =>
                                        passwordValidator().call(p0),
                                    suffixIcon: GestureDetector(
                                      onTap: () {
                                        resetPasswordProvider
                                            .isPasswordShow
                                            .value = !resetPasswordProvider
                                            .isPasswordShow
                                            .value;
                                      },
                                      child: isPasswordShow
                                          ? const Icon(
                                              Icons.visibility_outlined,
                                              color: AppColors.ff6C757D,
                                            )
                                          : const Icon(
                                              Icons.visibility_off_outlined,
                                              color: AppColors.ff6C757D,
                                            ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            ValueListenableBuilder(
                              valueListenable:
                                  resetPasswordProvider.isConfirmPasswordShow,
                              builder: (context, isConfirmPasswordShow, _) {
                                return AppTextFormField(
                                  hintText:
                                      context.l10n.enterYourConfirmPassword,
                                  title: context.l10n.confirmPassword,
                                  fillColor: AppColors.ffF8F9FA,
                                  obscureText: !resetPasswordProvider
                                      .isConfirmPasswordShow
                                      .value,
                                  controller: resetPasswordProvider
                                      .confirmPasswordController,
                                  validator: (value) =>
                                      ConfirmPasswordValidator(
                                        errorText: context
                                            .l10n
                                            .pleaseEnterConfirmPassword,
                                        password: resetPasswordProvider
                                            .passwordController
                                            .text
                                            .trim(),
                                      ).call(value),
                                  suffixIcon: GestureDetector(
                                    onTap: () {
                                      resetPasswordProvider
                                          .isConfirmPasswordShow
                                          .value = !resetPasswordProvider
                                          .isConfirmPasswordShow
                                          .value;
                                    },
                                    child: Icon(
                                      isConfirmPasswordShow
                                          ? Icons.visibility_outlined
                                          : Icons.visibility_off_outlined,
                                      color: AppColors.ff6C757D,
                                    ),
                                  ),
                                );
                              },
                            ),
                            Gap(AppSize.h40),
                            AppButton(
                              text: context.l10n.resetPassword,
                              isBottomBtn: false,
                              onPressed: () {
                                if (resetPasswordProvider
                                    .formKeyResetPassword
                                    .currentState!
                                    .validate()) {
                                  resetPasswordProvider.resetPasswordAPIcall(
                                    context: context,
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
