import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/forgot_password_page/provider/forgot_password_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class ForgotPasswordScreen extends StatelessWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: '', backgroundColor: AppColors.white),
      body: ChangeNotifierProvider(
        create: (context) => ForgotPasswordProvider(),
        child: Builder(
          builder: (context) {
            final forgetPasswordProvider = context
                .read<ForgotPasswordProvider>();
            return GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: forgetPasswordProvider.isShowLoader,
                builder: (context, isLoader, _) {
                  return AppLoader(
                    isShowLoader: isLoader,
                    child: AppPadding.symmetric(
                      horizontal: AppSize.appPadding,
                      child: Form(
                        key: forgetPasswordProvider.formKeyForgotPassword,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: AppSize.h30,
                                bottom: AppSize.h8,
                              ),
                              child: Text(
                                context.l10n.forgotPassword,
                                style: context.textTheme.headlineLarge
                                    ?.copyWith(fontSize: AppSize.sp24),
                              ),
                            ),
                            Text(
                              context.l10n.resetPasswordInstructions,
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w400,
                                color: AppColors.ff343A40,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                top: AppSize.h32,
                                bottom: AppSize.h40,
                              ),
                              child: AppTextFormField(
                                hintText: context.l10n.enterYourEmail,
                                title: context.l10n.email,
                                keyboardType: TextInputType.emailAddress,
                                fillColor: AppColors.ffF8F9FA,
                                controller:
                                    forgetPasswordProvider.emailController,
                                validator: (p0) => emailValidator().call(p0),
                              ),
                            ),
                            AppButton(
                              text: context.l10n.continues,
                              isBottomBtn: false,
                              onPressed: () {
                                if (forgetPasswordProvider
                                    .formKeyForgotPassword
                                    .currentState!
                                    .validate()) {
                                  forgetPasswordProvider.forgotPasswordAPIcall(
                                    context: context,
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
