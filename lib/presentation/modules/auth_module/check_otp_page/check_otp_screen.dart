import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/check_otp_params.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/provider/check_otp_provider.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/widgets/otp_pinput_widget.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class CheckOtpScreen extends StatelessWidget {
  const CheckOtpScreen({required this.checkOtpParams, super.key});
  final CheckOtpParams checkOtpParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: '', backgroundColor: AppColors.white),
      body: ChangeNotifierProvider(
        create: (context) => CheckOtpProvider(),
        child: Consumer<CheckOtpProvider>(
          builder: (context, checkOtpProvider, _) {
            return GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: checkOtpProvider.isShowLoader,
                builder: (context, isLoading, _) {
                  return AppLoader(
                    isShowLoader: isLoading,
                    child: AppPadding.symmetric(
                      horizontal: AppSize.appPadding,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                              top: AppSize.h30,
                              bottom: AppSize.h8,
                            ),
                            child: Text(
                              context.l10n.checkYourEmail,
                              style: context.textTheme.headlineLarge?.copyWith(
                                fontSize: AppSize.sp24,
                              ),
                            ),
                          ),
                          RichText(
                            strutStyle: const StrutStyle(height: 1.8),
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: context.l10n.sentVerificationCode,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    fontSize: AppSize.sp16,
                                    color: AppColors.ff343A40,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                TextSpan(
                                  text: checkOtpParams.email,
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: AppColors.ff67509C,
                                    fontWeight: FontWeight.w700,
                                    fontSize: AppSize.sp16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          AppPadding.symmetric(
                            vertical: AppSize.h32,
                            child: OtpPinPutWidget(
                              otpTextController:
                                  checkOtpProvider.otpTextController,
                            ),
                          ),
                          Gap(AppSize.h8),
                          AppButton(
                            text: context.l10n.verifyOtp,
                            isBottomBtn: false,
                            onPressed: () => checkOtpProvider.verifyOTPApiCall(
                              email: checkOtpParams.email,
                              context: context,
                              isFromForgotPassword:
                                  checkOtpParams.isFromForgotPassword,
                            ),
                          ),

                          AppPadding.symmetric(
                                vertical: AppSize.h16,
                                child: ValueListenableBuilder(
                                  valueListenable: checkOtpProvider.otpTimerValue,
                                  builder: (context, otpTimerValueV, _) {
                                    return RichText(
                                      text: TextSpan(
                                        text: context.l10n.didNtReceiveEmail,
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                              fontSize: AppSize.sp14,
                                              fontWeight: FontWeight.w400,
                                              color: AppColors.black,
                                            ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text:
                                                (checkOtpProvider
                                                        .otpTimer
                                                        ?.isActive ??
                                                    false)
                                                ? '00:${(checkOtpProvider.otpTimeout - (checkOtpProvider.otpTimerValue.value)).toString().padLeft(2, '0')}'
                                                : context.l10n.clickToResend,
                                            style: context.textTheme.bodySmall
                                                ?.copyWith(
                                                  color: AppColors.primaryColor,
                                                  fontWeight: FontWeight.w700,
                                                  fontSize: AppSize.sp14,
                                                ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap =
                                                  (checkOtpProvider
                                                          .otpTimer
                                                          ?.isActive ??
                                                      false)
                                                  ? null
                                                  : () async => checkOtpProvider
                                                        .resendOtpApiCall(
                                                          context: context,
                                                          email:
                                                              checkOtpParams.email,
                                                          isFromForgotPassword:
                                                              checkOtpParams
                                                                  .isFromForgotPassword,
                                                        ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),

                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
