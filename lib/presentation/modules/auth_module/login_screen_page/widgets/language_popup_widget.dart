import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/presentation/provider/app_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class LanguagePopupWidget extends StatelessWidget {
  const LanguagePopupWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<Locale>(
      onSelected: (value) {
        Injector.instance<AppProvider>().changeLocale(value);
      },
      borderRadius: BorderRadius.circular(AppSize.r10),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: const Locale('en'),
          child: Row(
            children: [
              const Text('English'),
              if (Injector.instance<AppDB>().languageCode == 'en')
                const Spacer(),
              if (Injector.instance<AppDB>().languageCode == 'en')
                Icon(
                  Icons.check_circle,
                  color: AppColors.successColor,
                  size: AppSize.sp20,
                ),
            ],
          ),
        ),
        PopupMenuItem(
          value: const Locale('es'),
          child: Row(
            children: [
              const Text('Spanish'),
              if (Injector.instance<AppDB>().languageCode == 'es')
                const Spacer(),
              if (Injector.instance<AppDB>().languageCode == 'es')
                Icon(
                  Icons.check_circle,
                  color: AppColors.successColor,
                  size: AppSize.sp20,
                ),
            ],
          ),
        ),
      ],
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w12,
          vertical: AppSize.h4,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: AppColors.ff0087C7.withValues(alpha: .1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              Localizations.localeOf(context).languageCode == 'en'
                  ? 'English'
                  : 'Spanish',
              style: const TextStyle(
                color: AppColors.ff0087C7,
                fontWeight: FontWeight.w600,
              ),
            ),
            Gap(AppSize.w4),
            Icon(
              Icons.keyboard_arrow_down,
              size: AppSize.sp20,
              color: AppColors.ff0087C7,
            ),
          ],
        ),
      ),
    );
  }
}
