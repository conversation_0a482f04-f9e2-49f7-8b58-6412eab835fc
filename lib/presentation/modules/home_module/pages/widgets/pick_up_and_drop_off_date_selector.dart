import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_textfield.dart';

/// pickup and drop off date selector
class PickUpAndDropOffDateSelector extends StatefulWidget {
  /// Constructor
  const PickUpAndDropOffDateSelector({
    required this.onPickupDateChanged,
    required this.onDropOffDateChanged,
    this.pickupDate,
    this.deliveryDate,
    this.readOnly = false,
    this.isExclusive = false,
    super.key,
  });

  /// On pick up change
  final void Function(DateTime date) onPickupDateChanged;

  /// On drop off change
  final void Function(DateTime date) onDropOffDateChanged;
  final DateTime? pickupDate;
  final DateTime? deliveryDate;
  final bool readOnly;
  final bool isExclusive;

  @override
  State<PickUpAndDropOffDateSelector> createState() =>
      _PickUpAndDropOffDateSelectorState();
}

class _PickUpAndDropOffDateSelectorState
    extends State<PickUpAndDropOffDateSelector> {
  TextEditingController pickupDateController = TextEditingController();
  TextEditingController dropOffDateController = TextEditingController();
  DateTime? _pickupDate;
  DateTime? _deliveryDate;

  Future<void> _selectDate(BuildContext context, bool isPickup) async {
    final extraDay = widget.isExclusive ? 1 : 2;
    final nextDate = DateTime.now().add(Duration(days: extraDay));
    final picked = await showDatePicker(
      context: context,
      initialDate:
          (isPickup
              ? widget.pickupDate
              : (widget.deliveryDate ??
                    widget.pickupDate?.add(Duration(days: extraDay)))) ??
          nextDate,
      firstDate: isPickup
          ? nextDate
          : (widget.pickupDate?.add(Duration(days: extraDay)) ?? nextDate),
      lastDate: DateTime.now().add(Duration(days: isPickup ? 60 : 62)),
    );
    if (picked != null) {
      if (mounted) {
        setState(() {
          if (isPickup) {
            _pickupDate = picked;
            pickupDateController.text = _pickupDate != null
                ? _pickupDate!.dateFormate
                : pickupDateController.text;
            if (_pickupDate != null) widget.onPickupDateChanged(_pickupDate!);
          } else {
            _deliveryDate = picked;
            dropOffDateController.text = _deliveryDate != null
                ? _deliveryDate!.dateFormate
                : pickupDateController.text;
            if (_deliveryDate != null) {
              widget.onDropOffDateChanged(_deliveryDate!);
            }
          }
        });
      }
    }
  }

  @override
  void didUpdateWidget(covariant PickUpAndDropOffDateSelector oldWidget) {
    _pickupDate = widget.pickupDate;
    pickupDateController = TextEditingController(
      text: widget.pickupDate?.dateFormate ?? '',
    );
    dropOffDateController = TextEditingController(
      text: widget.deliveryDate?.dateFormate ?? '',
    );
    _deliveryDate = widget.deliveryDate;
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    pickupDateController.dispose();
    dropOffDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.h16),
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSize.h16,
        children: [
          Text(
            context.l10n.pickupAndDeliveryDates,
            style: context.textTheme.titleLarge,
          ),
          AppTextFormField(
            readOnly: true,
            onTap: () => widget.readOnly ? null : _selectDate(context, true),
            hintText: 'DD/MM/YYYY',
            title: context.l10n.pickupDate,
            validator: (p0) => commonValidator(
              inputValue: p0,
              errorMessage: context.l10n.pleaseSelectPickupDate,
            ),
            suffixIcon: AppAssets.iconsCalendar.image(
              height: AppSize.h20,
              width: AppSize.w20,
              color: AppColors.ff495057,
              colorBlendMode: BlendMode.srcIn,
            ),
            controller: pickupDateController,
          ),
          AppTextFormField(
            readOnly: true,
            onTap: () => widget.readOnly ? null : _selectDate(context, false),
            hintText: 'DD/MM/YYYY',
            title: context.l10n.deliveryDate,
            validator: (p0) => commonValidator(
              inputValue: p0,
              errorMessage: context.l10n.pleaseSelectDropDate,
            ),
            suffixIcon: AppAssets.iconsCalendar.image(
              height: AppSize.h20,
              width: AppSize.w20,
              color: AppColors.ff495057,
              colorBlendMode: BlendMode.srcIn,
            ),
            controller: dropOffDateController,
          ),
        ],
      ),
    );
  }
}
