import 'package:flutter/material.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/widgets/equipment_type_radio_widget.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_padding.dart';

/// Vehicle type view
class VehicleTypeWidget extends StatelessWidget {
  /// Constructor
  const VehicleTypeWidget({
    super.key,
    required this.type,
    required this.onChange,
  });
  final String type;
  final void Function(String? value) onChange;

  @override
  Widget build(BuildContext context) {
    return AppPadding(
      bottom: AppSize.h10,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          //* Car Hauler
          EquipmentTypeRadioWidget(
            groupValue: type,
            value: AppStrings.carHauler,
            onChanged: onChange,
            equipmentImage: AppAssets.iconsCarHauler.image(),
            // equipmentTypeName: 'Car Hauler',
          ),

          //* Flat Bed
          EquipmentTypeRadioWidget(
            groupValue: type,
            value: AppStrings.flatBed,
            onChanged: onChange,
            equipmentImage: AppAssets.iconsFlatBed.image(),
            // equipmentTypeName: 'Flat Bed',
          ),

          //* Tow Truck
          EquipmentTypeRadioWidget(
            groupValue: type,
            value: AppStrings.towTruck,
            onChanged: onChange,
            equipmentImage: AppAssets.iconsTowTruck.image(),
            // equipmentTypeName: 'Tow Truck',
          ),
        ],
      ),
    );
  }
}
