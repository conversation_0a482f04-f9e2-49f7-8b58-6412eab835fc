import 'package:flutter/material.dart';
import 'package:transport_match/presentation/modules/home_module/models/car_creation_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/pickup_cost_model.dart';

/// Selected info of vehicle
class SelectedVehicleInfoModel {
  /// Constructor
  SelectedVehicleInfoModel({
    this.selectedBrand,
    this.selectedCarYear,
    this.selectedCarModel,
    this.carBrandString,
    this.carModelString,
    this.carYearString,
    this.carIssueString,
    this.carSerialString,
    this.showManualInputs = false,
    this.isWinchRequired = false,
    this.assignIndex,
    this.carType,
    this.isCarShouldBePickedUpFromMyLocation = false,
    this.costId,
    this.pickupCostModel,
    this.carYearList,
    this.carModels,
    this.isCarAssigned,
  });

  /// Selected car brand
  CarCreationModel? selectedBrand;

  /// Selected car creation
  CarYearModel? selectedCarYear;

  /// Selected car model
  CarModel? selectedCarModel;

  /// Car information
  List<CarYearModel>? carYearList;

  /// Vehicle model
  List<CarModel>? carModels;

  /// car brand string current value
  String? carBrandString;

  /// car issue string current value
  String? carIssueString;

  /// car model string current value
  String? carModelString;

  /// car year string current value
  String? carYearString;

  // /// car creation string current value
  // String? carConditionString;

  /// car serial string current value
  String? carSerialString;

  /// Show manual inputs
  bool showManualInputs;

  /// used only for assign vehicle to slot sheet
  int? assignIndex;

  /// used for winch required
  bool isWinchRequired;

  /// car sizing for new car
  ValueNotifier<String?>? carType;

  /// used for winch required
  bool isCarShouldBePickedUpFromMyLocation;

  /// cost id as per pick up option
  ValueNotifier<int?>? costId;

  /// pickup cost model for the vehicle
  ValueNotifier<PickupCostModel?>? pickupCostModel;

  /// variable is to check if the car is assigned or not
  ValueNotifier<bool>? isCarAssigned;
}
