import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/selected_vehicle_info.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/models/address_search_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/pick_up_and_drop_off_date_selector.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/vehicle_selection_list_widget.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_textfield.dart';

/// Shared trips tab UI
class ExclusiveTripsTab extends StatefulWidget {
  const ExclusiveTripsTab({super.key});

  @override
  State<ExclusiveTripsTab> createState() => _ExclusiveTripsTabState();
}

class _ExclusiveTripsTabState extends State<ExclusiveTripsTab> {
  final TextEditingController originController = TextEditingController();
  final TextEditingController dropController = TextEditingController();

  @override
  void initState() {
    final homeProvider = context.read<HomeProvider>();
    homeProvider.selectedVehicleInfo
      ..clear()
      ..add(SelectedVehicleInfoModel());
    homeProvider
      ..exclusivePickLocation.value = null
      ..exclusiveDropLocation.value = null
      ..pickupDate.value = null
      ..deliveryDate.value = null;
    super.initState();
  }

  @override
  void dispose() {
    originController.dispose();
    dropController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final homeProvider = context.read<HomeProvider>();
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
      child: Form(
        key: homeProvider.formKeyExclusiveTrip,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: AppSize.h15, bottom: AppSize.h24),
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '${context.l10n.exclusiveTrip}: ',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    TextSpan(
                      text: context.l10n.aPersonalized,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Text(
              context.l10n.userLocation,
              style: context.textTheme.titleLarge,
              textAlign: TextAlign.left,
            ),
            Padding(
              padding: EdgeInsets.only(top: AppSize.h8, bottom: AppSize.h16),
              child: AppTextFormField(
                controller: originController,
                hintText: context.l10n.userPickUpLocation,
                readOnly: true,
                validator: (p0) => commonValidator(
                  inputValue: p0,
                  errorMessage: context.l10n.chooseOriginStockLocation,
                ),
                onTap: () => AppNavigationService.pushNamed(
                  context,
                  AppRoutes.homeAddressSearchScreen,
                  extra: AddressSearchParams(
                    hintText: context.l10n.userPickUpLocation,
                    homeProvider: homeProvider,
                    isExclusive: true,
                    isOther: true,
                    onTap: (address) {
                      homeProvider.exclusivePickLocation.value = address;
                      originController.text = address.address ?? '';
                    },
                  ),

                  // AddressSearchScreen(
                  //   homeProvider: homeProvider,
                  //   hintText: context.l10n.userPickUpLocation,
                  //   isOther: true,
                  //   isExclusive: true,
                  //   onTap: (address) =>
                  //       homeProvider.exclusivePickLocation.value = address,
                  // ),
                ),
              ),
            ),
            AppTextFormField(
              controller: dropController,
              hintText: context.l10n.userDeliveryLocation,
              readOnly: true,
              validator: (p0) => commonValidator(
                inputValue: p0,
                errorMessage: context.l10n.chooseDropStockLocation,
              ),
              onTap: () => AppNavigationService.pushNamed(
                context,
                AppRoutes.homeAddressSearchScreen,
                extra: AddressSearchParams(
                  hintText: context.l10n.userDeliveryLocation,
                  homeProvider: homeProvider,
                  isExclusive: true,
                  isOther: true,
                  isDrop: true,
                  onTap: (address) {
                    homeProvider.exclusiveDropLocation.value = address;
                    dropController.text = address.address ?? '';
                  },
                ),
                //  AddressSearchScreen(
                //   hintText: context.l10n.userDeliveryLocation,
                //   homeProvider: homeProvider,
                //   isOther: true,
                //   isExclusive: true,
                //   onTap: (address) =>
                //       homeProvider.exclusiveDropLocation.value = address,
                // ),
              ),
            ),
            Gap(AppSize.h24),
            const VehicleSelectionListWidget(isExclusiveTrip: true),
            Consumer<HomeProvider>(
              builder: (context, homeProvider, child) {
                return PickUpAndDropOffDateSelector(
                  pickupDate: homeProvider.pickupDate.value,
                  deliveryDate: homeProvider.deliveryDate.value,
                  isExclusive: true,
                  onPickupDateChanged: (date) {
                    homeProvider
                      ..pickupDate.value = date
                      ..deliveryDate.value = null
                      ..notify();
                  },
                  onDropOffDateChanged: (date) {
                    homeProvider
                      ..deliveryDate.value = date
                      ..notify();
                  },
                );
              },
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h24),
              child: Consumer<HomeProvider>(
                builder: (context, homeProvider, child) {
                  return AppButton(
                    isBottomBtn: false,
                    text:
                        homeProvider.selectedVehicleInfo.any(
                          (e) => e.showManualInputs,
                        )
                        ? context.l10n.submitForApproval
                        : context.l10n.sendRequest,
                    onPressed: () async =>
                        (homeProvider.formKeyExclusiveTrip.currentState
                                ?.validate() ??
                            false)
                        ? await homeProvider.bookExclusive(
                            context,
                            homeProvider,
                          )
                        : null,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
