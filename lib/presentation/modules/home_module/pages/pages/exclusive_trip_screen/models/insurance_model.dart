class InsuranceModel {
  const InsuranceModel({
    this.id,
    this.name,
    this.insuranceProviderName,
    this.amount,
    this.coverage,
  });

  factory InsuranceModel.fromJson(Map<String, dynamic> json) {
    return InsuranceModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      insuranceProviderName: json['insurance_provider_name'] as String?,
      amount: json['amount'] as double?,
      coverage: json['coverage'] as String?,
    );
  }
  final int? id;
  final String? name;
  final String? insuranceProviderName;
  final double? amount;
  final String? coverage;
}
