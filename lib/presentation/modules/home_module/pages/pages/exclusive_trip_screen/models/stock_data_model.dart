import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/utils/app_common_functions.dart';

class StockLocationModel {
  const StockLocationModel({required this.stockLocation});

  factory StockLocationModel.fromJson(List<dynamic> json) {
    return StockLocationModel(
      stockLocation: json
          .map(
            (brand) => StopLocationData.fromJson(brand as Map<String, dynamic>),
          )
          .toList(),
    );
  }
  final List<StopLocationData> stockLocation;

  List<Map<String, dynamic>> toJson() {
    return stockLocation.map((brand) => brand.toJson()).toList();
  }
}

class AddressModel {
  AddressModel({
    this.street,
    // this.neighborhood,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.countryCode,
    this.latitude,
    this.longitude,
    this.address,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => AddressModel(
    street: json['street'] as String?,
    // neighborhood: json['neighborhood'] as String?,
    city: json['city'] as String?,
    state: json['state'] as String?,
    postalCode: json['postal_code'] as String?,
    country: json['country'] as String?,
    countryCode: json['country_code'] as String?,
    latitude: json['latitude']?.toString(),
    longitude: json['longitude']?.toString(),
  );
  String? address;
  final String? street;
  // final String? neighborhood;
  final String? city;
  final String? state;
  final String? postalCode;
  final String? country;
  final String? countryCode;
  final String? latitude;
  final String? longitude;

  Map<String, dynamic> toJson() => {
    'street': street.isNotEmptyAndNotNull ? street : '-',
    // if (neighborhood != null) 'neighborhood': neighborhood,
    'city': city.isNotEmptyAndNotNull ? city : '-',
    'state': state.isNotEmptyAndNotNull ? state : '-',
    'postal_code': postalCode.isNotEmptyAndNotNull ? postalCode : '-',
    'country': country.isNotEmptyAndNotNull ? country : '-',
    'country_code': countryCode.isNotEmptyAndNotNull ? countryCode : '-',
    'latitude': latitude?.doubletToShort(),
    'longitude': longitude?.doubletToShort(),
  };
}

class StopLocationData {
  StopLocationData({
    this.id,
    this.name,
    this.latitude,
    this.longitude,
    this.date,
    this.time,
    this.stopLocationIndex,
    this.preDistance,
    this.nextDistance,
    this.address,
    this.stopLocationCharge,
  });

  factory StopLocationData.fromJson(Map<String, dynamic> json) =>
      StopLocationData(
        id: json['id'] as num?,
        name: json['name'] as String?,
        latitude: json['latitude']?.toString(),
        longitude: json['longitud']?.toString(),
        address: json['address'] == null
            ? null
            : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
        stopLocationCharge: json['stop_location_charge'] as num?,
      );
  final num? id;
  final String? name;
  final String? latitude;
  final String? longitude;
  final AddressModel? address;
  DateTime? date;
  TimeOfDay? time;
  int? stopLocationIndex;
  double? preDistance;
  double? nextDistance;
  final num? stopLocationCharge;

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'address': address?.toJson(),
    'latitude': latitude,
    'longitud': longitude,
    'stop_location_charge': stopLocationCharge,
  };
}

extension Add on StopLocationData {
  String? get fullAddress {
    final addressVal = AppCommonFunctions.cleanUpAddress(
      '$name${AppCommonFunctions.addComma(address?.street)}'
      '${AppCommonFunctions.addComma(address?.city)}'
      '${AppCommonFunctions.addComma(address?.state)}',
    );
    return addressVal;
  }
}
