import 'package:flutter/material.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class EquipmentTypeRadioWidget extends StatelessWidget {
  const EquipmentTypeRadioWidget({
    required this.groupValue,
    required this.value,
    this.onChanged,
    this.equipmentImage,
    super.key,
  });

  final String groupValue;
  final String value;
  final void Function(String?)? onChanged;
  final Widget? equipmentImage;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Radio<String>(
          value: value,
          groupValue: groupValue,
          onChanged: onChanged,
          fillColor: WidgetStateProperty.resolveWith((states) {
            // active
            if (states.contains(WidgetState.selected)) {
              return AppColors.primaryColor;
            }
            // inactive
            return AppColors.ffDEE2E6;
          }),
        ),
        SizedBox(
          height: AppSize.h30,
          width: AppSize.h30,
          child: equipmentImage,
        ),
      ],
    );
  }
}
