// ignore_for_file: use_build_context_synchronously

import 'package:custom_info_window/custom_info_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/models/address_search_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/provider/google_map_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class AddressSearchScreen extends StatefulWidget {
  const AddressSearchScreen({super.key, required this.addressSearchParams});
  final AddressSearchParams addressSearchParams;

  @override
  State<AddressSearchScreen> createState() => _AddressSearchScreenState();
}

class _AddressSearchScreenState extends State<AddressSearchScreen> {
  FocusNode focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => GoogleMapProvider(),
      child: Consumer<GoogleMapProvider>(
        builder: (context, googleMapProvider, child) {
          return Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: CustomAppBar(
              title: widget.addressSearchParams.hintText,
              backgroundColor: AppColors.white,
            ),
            body: ValueListenableBuilder(
              valueListenable: widget
                  .addressSearchParams
                  .homeProvider
                  .isSearchAddressShowLoader,
              builder: (context, value, child) {
                return AppLoader(
                  isShowLoader: value || googleMapProvider.isShowLoader,
                  child: Stack(
                    children: [
                      /// google map widget here
                      GoogleMap(
                        initialCameraPosition: CameraPosition(
                          target: googleMapProvider.center,
                          zoom: 14,
                        ),
                        zoomControlsEnabled: false,
                        myLocationEnabled: true,
                        myLocationButtonEnabled: false,
                        markers:
                            (googleMapProvider.markers.toList() +
                                    googleMapProvider.stopMarkers.toList())
                                .toSet(),
                        onMapCreated: googleMapProvider.onMapCreated,
                        onCameraMove: googleMapProvider.onCameraMove,
                        onTap: (argument) async => googleMapProvider.onMapTap(
                          argument,
                          isSearch:
                              !widget.addressSearchParams.isExclusive &&
                              widget.addressSearchParams.isSearchStockLocation,
                          isDrop: widget.addressSearchParams.isDrop,
                          homeProvider: widget.addressSearchParams.homeProvider,
                          context: context,
                        ),
                      ),
                      CustomInfoWindow(
                        controller:
                            googleMapProvider.customInfoWindowController,
                        height: 80,
                        width: 230,
                      ),

                      /// search address text filed widget here
                      Positioned(
                        top: AppSize.h10,
                        left: AppSize.appPadding,
                        right: AppSize.appPadding,
                        child: SizedBox(
                          width: MediaQuery.sizeOf(context).width,
                          height: AppSize.h60,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Flexible(
                                child: TypeAheadField<String>(
                                  focusNode: focusNode,
                                  builder: (context, controller, focusNode) =>
                                      AppTextFormField(
                                        controller: controller,
                                        focusNode: focusNode,
                                        hintText: context.l10n.searchAddress,
                                      ),
                                  itemBuilder: (context, value) => Padding(
                                    padding: EdgeInsets.all(AppSize.sp10),
                                    child: Text(value),
                                  ),
                                  // offset: const Offset(0, -10),
                                  decorationBuilder: (context, child) =>
                                      DecoratedBox(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                          color: AppColors.pageBGColor,
                                        ),
                                        child: child,
                                      ),
                                  onSelected: (value) async =>
                                      googleMapProvider.onAddressTap(
                                        value,
                                        context,
                                        isExclusive: widget
                                            .addressSearchParams
                                            .isExclusive,
                                        isSearchStockLocation: widget
                                            .addressSearchParams
                                            .isSearchStockLocation,
                                        isDrop:
                                            widget.addressSearchParams.isDrop,
                                        homeProvider: widget
                                            .addressSearchParams
                                            .homeProvider,
                                      ),
                                  emptyBuilder: (context) => const SizedBox(),
                                  suggestionsCallback: (search) async {
                                    googleMapProvider.addressList =
                                        await googleMapProvider.getAddress(
                                          search,
                                        );
                                    return googleMapProvider.addressList
                                        .map((e) => e.description)
                                        .toList();
                                  },
                                ),
                              ),
                              Gap(AppSize.w10),
                              AppPadding(
                                bottom: AppSize.h18,
                                child: CircleAvatar(
                                  radius: 20,
                                  backgroundColor: AppColors.pageBGColor,
                                  child: IconButton(
                                    onPressed: () => googleMapProvider
                                        .moveToCurrentLocation(context),
                                    icon: const Icon(Icons.my_location),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      /// select location button widget here
                      if (!(!widget.addressSearchParams.isExclusive &&
                          widget.addressSearchParams.isSearchStockLocation))
                        Positioned(
                          bottom: MediaQuery.paddingOf(context).bottom / 2 + 10,
                          left: 20,
                          right: 20,
                          child: AppButton(
                            text: context.l10n.selectLocation,
                            isBottomBtn: false,
                            onPressed: () => googleMapProvider.onBtnTap(
                              context,
                              onTap: widget.addressSearchParams.onTap,
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  @override
  void initState() {
    focusNode.requestFocus();
    super.initState();
  }
}
