import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';

/// Parameter model for address search screen.
class AddressSearchParams {
  /// Constructor
  const AddressSearchParams({
    required this.homeProvider,
    required this.hintText,
    required this.onTap,
    this.isDrop = false,
    this.isOther = false,
    this.isBack = true,
    this.isExclusive = false,
    this.isSearchStockLocation = true,
  });

  /// Home provider instance
  final HomeProvider homeProvider;

  /// Flag to indicate if this is a drop location search
  final bool isDrop;

  /// Hint text for the search field
  final String hintText;

  /// Flag to indicate if this is for "other" location
  final bool isOther;

  /// Flag to indicate if back navigation is enabled
  final bool isBack;

  /// Flag to indicate if this is for exclusive trip
  final bool isExclusive;

  /// Callback function when an address is selected
  final void Function(AddressModel address) onTap;

  /// Flag to indicate if searching for stock locations
  final bool isSearchStockLocation;
}
