// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/selected_vehicle_info.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/pages/transporter_list_screen/provider/transporter_list_provider.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/add_to_car_widget.dart';
import 'package:transport_match/widgets/app_loader.dart';

/// Open add to cart sheet
Future<void> openAddToCartSheet(
  BuildContext context,
  ProviderListData providerListData,
  HomeProvider homeProvider,
  TransporterListProvider transporterListProvider, {
  bool isTrip = false,
}) async {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppSize.r20),
        topRight: Radius.circular(AppSize.r20),
      ),
    ),
    builder: (context) {
      return _AddToCarSheet(
        homeProvider: homeProvider,
        providerListData: providerListData,
        transporterListProvider: transporterListProvider,
        isTrip: isTrip,
      );
    },
  );
}

class _AddToCarSheet extends StatefulWidget {
  const _AddToCarSheet({
    required this.homeProvider,
    required this.providerListData,
    required this.transporterListProvider,
    required this.isTrip,
  });
  final HomeProvider homeProvider;
  final TransporterListProvider transporterListProvider;
  final ProviderListData providerListData;
  final bool isTrip;

  @override
  State<_AddToCarSheet> createState() => _AddToCarSheetState();
}

class _AddToCarSheetState extends State<_AddToCarSheet> {
  final carList = <CarDetailModel>[];
  final slotList = <String>[];
  final dummyVehicle = <SelectedVehicleInfoModel>[];

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      widget.transporterListProvider.isShowLoader.value = false;
      for (var i = 0; i < widget.homeProvider.selectedVehicleInfo.length; i++) {
        final e = widget.homeProvider.selectedVehicleInfo[i];
        final index = widget.transporterListProvider.transporterDetailList.value
            .indexWhere(
              (e) =>
                  e.carDetails.any((v) => v.selectedIndex == i) &&
                  e.trip != widget.providerListData.id,
            );

        e.assignIndex = null;
        if (index == -1) {
          dummyVehicle.add(e);
        }
      }
      if (widget
          .transporterListProvider
          .transporterDetailList
          .value
          .isNotEmpty) {
        final index = widget.transporterListProvider.transporterDetailList.value
            .indexWhere((e) => e.trip == widget.providerListData.id);
        if (index != -1) {
          carList.addAll(
            widget
                .transporterListProvider
                .transporterDetailList
                .value[index]
                .carDetails,
          );
        }
      }
      if (carList.isEmpty) {
        slotList.addAll(
          List.generate(
            widget.homeProvider.assignCarValue.value,
            (index) => '',
          ),
        );
      } else {
        for (final element in carList) {
          slotList.add(element.carName ?? '');
        }
      }
      Future.delayed(Durations.short1, () => setState(() {}));
      setState(() {});
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final list = dummyVehicle.where(
      (e) =>
          (e.selectedCarModel?.sizes?.size ?? 0) <=
              (widget.providerListData.availableSlot ?? 0) &&
          (widget.providerListData.isWinch ||
              e.isWinchRequired == widget.providerListData.isWinch),
    );
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        widget.transporterListProvider.createBookingSessionToken?.cancel();
        widget.transporterListProvider.registerTempSlotSingleToken?.cancel();
      },
      child: ValueListenableBuilder(
        valueListenable: widget.transporterListProvider.isShowLoader,
        builder: (context, loading, child) {
          final transporterProvider = widget.transporterListProvider;
          return AppLoader(
            isShowLoader: loading,
            // isFullScreen: false,
            child: AddToCarWidget(
              providerData: widget.providerListData,
              slotList: slotList,
              items: (index) => List.generate(list.length, (i) {
                final e = list.toList()[i];
                return GestureDetector(
                  onTap: () {
                    if (carList.any((e) => e.selectedIndex == i)) {
                      context.l10n.uAlreadyAssign.showErrorAlert();
                    } else {
                      final vehicle = e;
                      if (carList.length >= index + 1) {
                        carList.removeAt(index);
                      }

                      slotList[index] = vehicle.selectedCarModel?.model ?? '';

                      final carVehicle = vehicle.costId?.value != null
                          ? CarDetailModel(
                              selectedIndex: i,
                              serialNumber: vehicle.carSerialString?.nullCheck,
                              car: vehicle.selectedCarModel?.sizes?.id ?? 0,
                              carName: vehicle.selectedCarModel?.model ?? '',
                              brand:
                                  vehicle.carBrandString ??
                                  vehicle.selectedBrand?.id?.toString(),
                              year:
                                  vehicle.carYearString ??
                                  vehicle.selectedCarYear?.id?.toString(),
                              carSize:
                                  vehicle.selectedCarModel?.sizes?.size ?? 1,
                              isCarPickedUp:
                                  vehicle.isCarShouldBePickedUpFromMyLocation,
                              pickUpServiceAndDropOffService:
                                  vehicle.costId?.value,
                              fromCarToBePickedUpLocation:
                                  vehicle.isCarShouldBePickedUpFromMyLocation
                                  ? widget.homeProvider.pickUpAddress.value
                                  : null,
                              totalDistanceFromCustomerLocationToStopLocation:
                                  vehicle.isCarShouldBePickedUpFromMyLocation
                                  ? widget.homeProvider.distance
                                  : null,
                              isWinchRequired: vehicle.isWinchRequired,
                              carDescription: vehicle.carIssueString?.nullCheck,
                            )
                          : CarDetailModel(
                              selectedIndex: i,
                              serialNumber: vehicle.carSerialString?.nullCheck,
                              car: vehicle.selectedCarModel?.sizes?.id ?? 0,
                              carName: vehicle.selectedCarModel?.model ?? '',
                              brand:
                                  vehicle.carBrandString ??
                                  vehicle.selectedBrand?.id?.toString(),
                              year:
                                  vehicle.carYearString ??
                                  vehicle.selectedCarYear?.id?.toString(),
                              carSize:
                                  vehicle.selectedCarModel?.sizes?.size ?? 1,
                            );
                      carList.insert(index, carVehicle);

                      setState(() {});
                      AppNavigationService.pop(context);
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(bottom: AppSize.h10),
                    padding: EdgeInsets.all(AppSize.sp10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppSize.r8),
                      border: Border.all(
                        color: AppColors.ffDEE2E6,
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          "${e.selectedCarModel?.model ?? ''}"
                              " (${e.carYearString ?? e.selectedCarYear?.year.toString() ?? ''})",
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
              isInAllVehicle: widget.homeProvider.isInAllVehicle.value,
              isAddMoreBtn: slotList.length == list.length || list.isEmpty,
              onRemoveAll: () {
                setState(() {
                  carList.clear();
                  for (var i = 0; i < slotList.length; i++) {
                    slotList[i] = '';
                  }
                });
              },
              onSlotRemove: (index) {
                setState(() {
                  if (index < carList.length) {
                    dummyVehicle
                            .firstWhere(
                              (e) =>
                                  e.selectedCarModel?.model == slotList[index],
                            )
                            .assignIndex =
                        null;
                    if (!widget.homeProvider.isInAllVehicle.value) {
                      carList.removeAt(index);
                    } else {
                      carList[index].selectedIndex = null;
                    }
                  }
                  if (slotList.length == 1 ||
                      widget.homeProvider.isInAllVehicle.value) {
                    slotList[index] = '';
                  } else {
                    slotList.removeAt(index);
                  }
                });
                if (slotList.length == 1) {
                  widget.homeProvider
                    ..assignCarValue.value -= 1
                    ..notify();
                }
              },
              onAddMoreCar: () {
                if (carList.length == slotList.length) {
                  num totalSize = 0;
                  for (final e in carList) {
                    totalSize += e.carSize ?? 0;
                  }
                  var isSlotExist = true;
                  final dummyList = dummyVehicle.where(
                    (e) => e.assignIndex == null,
                  );

                  isSlotExist = dummyList.any((e) {
                    return totalSize + (e.selectedCarModel?.sizes?.size ?? 0) <=
                        (widget.providerListData.availableSlot ?? 0);
                  });
                  if (isSlotExist) {
                    widget.homeProvider
                      ..assignCarValue.value += 1
                      ..notify();
                    setState(() {
                      slotList.add('');
                    });
                  } else {
                    context.l10n.noSlotAvailable.showErrorAlert();
                  }
                } else {
                  '${context.l10n.pleaseAssignCar} ${slotList.length}'
                      .showErrorAlert();
                }
              },
              onSaveBtn: () {
                final temptTransporterDetailList = [
                  ...transporterProvider.transporterDetailList.value,
                ];
                if (carList.length == slotList.length) {
                  if (slotList.any((e) => e.isEmpty) &&
                      slotList.any((e) => e.isNotEmpty)) {
                    context.l10n.pleaseChooseCar.showErrorAlert();
                  } else {
                    final index = temptTransporterDetailList.indexWhere(
                      (e) => e.trip == widget.providerListData.id,
                    );
                    final data = TransporterDetailModel(
                      trip: widget.providerListData.id ?? 0,
                      carDetails: carList,
                      providerPickUpLocation:
                          widget
                              .providerListData
                              .customerStartStopLocation
                              ?.estimatedArrivalDate ??
                          DateTime.now(),
                    );

                    transporterProvider
                        .registerTempSlotSingle(
                          context,
                          data,
                          homeProvider: widget.homeProvider,
                        )
                        .then((value) {
                          if (value) {
                            if (carList.isEmpty) {
                              if (index != -1) {
                                transporterProvider.transporterDetailList.value
                                    .removeAt(index);
                              }
                            } else {
                              if (index != -1) {
                                transporterProvider.transporterDetailList.value
                                    .removeAt(index);
                                transporterProvider.transporterDetailList.value
                                    .insert(index, data);
                              } else {
                                transporterProvider.transporterDetailList.value
                                    .add(data);
                              }
                            }

                            widget.homeProvider
                              ..totalAssignCar.value = 0
                              ..clearSelectedVehicle();
                            for (final e
                                in transporterProvider
                                    .transporterDetailList
                                    .value) {
                              widget.homeProvider
                                ..totalAssignCar.value += e.carDetails.length
                                ..assignCar(e.carDetails);
                            }
                            widget.homeProvider.notify();
                            transporterProvider.notify();
                            AppNavigationService.pop(context);
                          }
                        });
                  }
                } else {
                  if (slotList.length == 1 ||
                      widget.homeProvider.isInAllVehicle.value) {
                    if (slotList.any((e) => e.isEmpty) &&
                        slotList.any((e) => e.isNotEmpty)) {
                      context.l10n.pleaseChooseCar.showErrorAlert();
                    } else {
                      final index = transporterProvider
                          .transporterDetailList
                          .value
                          .indexWhere(
                            (e) => e.trip == widget.providerListData.id,
                          );
                      if (index != -1) {
                        transporterProvider
                            .registerTempSlotSingle(
                              context,
                              transporterProvider
                                  .transporterDetailList
                                  .value[index],
                              isRemove: true,
                              homeProvider: widget.homeProvider,
                            )
                            .then((value) {
                              if (value) {
                                transporterProvider.transporterDetailList.value
                                    .removeAt(index);
                                widget.homeProvider
                                  ..totalAssignCar.value = 0
                                  ..clearSelectedVehicle();
                                for (final e
                                    in transporterProvider
                                        .transporterDetailList
                                        .value) {
                                  widget.homeProvider
                                    ..totalAssignCar.value +=
                                        e.carDetails.length
                                    ..assignCar(e.carDetails);
                                }
                                widget.homeProvider.notify();
                                transporterProvider.notify();
                                AppNavigationService.pop(context);
                              }
                            });
                      }
                    }
                  } else {
                    context.l10n.pleaseChooseCar.showErrorAlert();
                  }
                }
              },
            ),
          );
        },
      ),
    );
  }
}
