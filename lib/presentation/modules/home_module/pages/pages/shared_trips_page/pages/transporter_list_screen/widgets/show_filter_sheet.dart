import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';

/// Show filter bottom sheet
Future<T?> showFilterBottomSheet<T>(
  BuildContext context,
  HomeProvider homeProvider,
) {
  return showModalBottomSheet<T>(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(AppSize.r12)),
    ),
    backgroundColor: AppColors.pageBGColor,
    isScrollControlled: true,
    builder: (context) {
      return FilterScreen(
        onSave:
            (
              rate, {
              required isFromLow,
              required isIn2Day,
              team,
              required isWinch,
              required isException,
            }) {
              homeProvider.changeFilterRate(rate);
              if (homeProvider.isLowest.value != isFromLow) {
                homeProvider
                  ..isLowest.value = isFromLow
                  ..notify();
              }
              if (homeProvider.is2Days.value != isIn2Day) {
                homeProvider
                  ..is2Days.value = isIn2Day
                  ..notify();
              }
              if (homeProvider.isWinch.value != isWinch) {
                homeProvider
                  ..isWinch.value = isWinch
                  ..notify();
              }
              if (homeProvider.isException.value != isException) {
                homeProvider
                  ..isException.value = isException
                  ..notify();
              }
              return homeProvider.findTransporter(
                context,
                homeProvider,
                isFilter: true,
                isBack: true,
              );
            },
        isWinch: homeProvider.isWinch.value,
        onClear: () => homeProvider
          ..clearFilter()
          ..findTransporter(
            context,
            homeProvider,
            isFilter: true,
            isBack: true,
          ),
        is2Day: homeProvider.is2Days.value,
        isLow: homeProvider.isLowest.value,
        rate: homeProvider.rate.value,
        isWinchFixed: homeProvider.isWinchFix.value,
        isExceptionFixed: homeProvider.isExceptionFix.value,
        isException: homeProvider.isException.value,
      );
    },
  );
}

class FilterScreen extends StatefulWidget {
  const FilterScreen({
    super.key,
    required this.onSave,
    required this.onClear,
    required this.rate,
    this.team,
    required this.isLow,
    required this.is2Day,
    this.isTripFiler = false,
    required this.isWinch,
    required this.isWinchFixed,
    required this.isException,
    required this.isExceptionFixed,
    this.requiredSlot,
  });
  final Function(
    String rate, {
    required bool isFromLow,
    required bool isIn2Day,
    required bool isWinch,
    required bool isException,
    int? team,
  })
  onSave;
  final Function() onClear;
  final String rate;
  final int? team;
  final double? requiredSlot;
  final bool isLow;
  final bool is2Day;
  final bool isWinch;
  final bool isTripFiler;
  final bool isWinchFixed;
  final bool isException;
  final bool isExceptionFixed;
  @override
  State<FilterScreen> createState() => __FilterStateScreen();
}

class __FilterStateScreen extends State<FilterScreen> {
  String rate = '';
  int team = 0;
  bool isFromLow = false;
  bool isInDay = false;
  bool isWinch = false;
  bool isException = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        rate = widget.rate;
        team = widget.team ?? 0;
        isInDay = widget.is2Day;
        isFromLow = widget.isLow;
        isWinch = widget.isWinch;
        isException = widget.isException;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(AppSize.bottomSheetPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.filterBy,
                style: context.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.ff212529,
                ),
              ),
              TextButton(
                onPressed: () => widget.onClear(),
                // controller
                //   ..clearFilter()
                //   ..findTransporter(context, controller, isFilter: true),
                child: Text(
                  context.l10n.clearAll,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.theme.primaryColor,
                    fontSize: AppSize.sp14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          Gap(AppSize.h16),
          if (widget.isTripFiler)
            Text(
              context.l10n.teamCapacity,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: AppSize.sp14,
                color: AppColors.ff6C757D,
              ),
            ),
          if (widget.isTripFiler)
            Slider(
              value: team.toDouble(),
              divisions: widget.requiredSlot?.toInt() ?? 1,
              max: widget.requiredSlot ?? 1.0,
              label: team.toString(),
              secondaryActiveColor: Colors.white,
              onChanged: (value) {
                setState(() {
                  team = value.toInt();
                });
              },
            ),
          if (widget.isTripFiler)
            // Wrap(
            //   children: [
            //     _FilterOption(
            //       label: '3',
            //       isSelected: team == '3',
            //       onTap: (val) => setState(() => team = val ? '' : '3'),
            //     ),
            //     _FilterOption(
            //       label: '5',
            //       isSelected: team == '5',
            //       onTap: (val) => setState(() => team = val ? '' : '5'),
            //     ),
            //     _FilterOption(
            //       label: '9',
            //       isSelected: team == '9',
            //       onTap: (val) => setState(() => team = val ? '' : '9'),
            //     ),
            //   ],
            // ),
            if (widget.isTripFiler) Gap(AppSize.h5),
          Text(
            context.l10n.rating,
            style: context.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: AppSize.sp14,
              color: AppColors.ff6C757D,
            ),
          ),
          Gap(AppSize.h4),
          Wrap(
            runSpacing: AppSize.h10,
            children: [
              _FilterOption(
                label: '${context.l10n.rated} 3.5+',
                isSelected: rate == '${context.l10n.rated} 3.5+',
                onTap: (val) => setState(
                  () => rate = val ? '' : '${context.l10n.rated} 3.5+',
                ),
              ),
              _FilterOption(
                label: '${context.l10n.rated} 4+',
                isSelected: rate == '${context.l10n.rated} 4+',
                onTap: (val) => setState(
                  () => rate = val ? '' : '${context.l10n.rated} 4+',
                ),
              ),
              _FilterOption(
                label: '${context.l10n.rated} 4.5+',
                isSelected: rate == '${context.l10n.rated} 4.5+',
                onTap: (val) => setState(
                  () => rate = val ? '' : '${context.l10n.rated} 4.5+',
                ),
              ),
            ],
          ),
          Gap(AppSize.h16),
          Text(
            context.l10n.pricing,
            style: context.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: AppSize.sp14,
              color: AppColors.ff6C757D,
            ),
          ),
          Gap(AppSize.h4),
          _FilterOption(
            label: context.l10n.lowestPerKM,
            isSelected: isFromLow,
            onTap: (val) => setState(() => isFromLow = !isFromLow),
          ),
          // Gap(AppSize.h16),
          // Text(
          //   context.l10n.sooner,
          //   style: context.textTheme.bodyMedium?.copyWith(
          //     fontWeight: FontWeight.w500,
          //     fontSize: AppSize.sp14,
          //     color: AppColors.ff6C757D,
          //   ),
          // ),
          // Gap(AppSize.h4),
          // _FilterOption(
          //   label: context.l10n.in2Days,
          //   isSelected: isInDay,
          //   onTap: (val) => setState(() => isInDay = !isInDay),
          // ),
          Gap(AppSize.h16),
          AppConfirmCheckBox(
            description: context.l10n.winchRequired,
            value: isWinch,
            isDisabledValueChange: widget.isWinchFixed,
            onSelectionChanged: ({required bool value}) {
              setState(() => isWinch = value);
            },
          ),
          Gap(AppSize.h16),
          AppConfirmCheckBox(
            description: context.l10n.exception,
            value: isException,
            isDisabledValueChange: widget.isExceptionFixed,
            onSelectionChanged: ({required bool value}) {
              setState(() => isException = value);
            },
          ),
          Gap(AppSize.h24),
          AppButton(
            text: context.l10n.save,
            isBottomBtn: false,
            onPressed: () => widget.onSave(
              rate,
              isFromLow: isFromLow,
              isIn2Day: isInDay,
              team: team,
              isWinch: isWinch,
              isException: isException,
            ),
          ),
        ],
      ),
    );
  }
}

class _FilterOption extends StatelessWidget {
  const _FilterOption({
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  final String label;
  final bool isSelected;
  // ignore: avoid_positional_boolean_parameters
  final Function(bool isSelected) onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(isSelected),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w16,
          vertical: AppSize.w4,
        ),
        margin: EdgeInsets.only(right: AppSize.h8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryColor : AppColors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : AppColors.ffCED4DA,
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? AppColors.white : AppColors.ff495057,
            fontWeight: FontWeight.w500,
            fontSize: AppSize.sp12,
          ),
        ),
      ),
    );
  }
}
