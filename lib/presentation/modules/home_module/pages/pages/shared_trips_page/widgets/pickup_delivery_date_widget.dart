import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/pick_up_and_drop_off_date_selector.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';

class PickupDeliveryDateWidget extends StatelessWidget {
  const PickupDeliveryDateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<HomeProvider>(
      builder: (context, homeProvider, child) {
        return PickUpAndDropOffDateSelector(
          deliveryDate: homeProvider.deliveryDate.value,
          pickupDate: homeProvider.pickupDate.value,
          onPickupDateChanged: (date) {
            homeProvider
              ..pickupDate.value = date
              ..deliveryDate.value = null
              ..notify();
          },
          onDropOffDateChanged: (date) {
            homeProvider
              ..deliveryDate.value = date
              ..notify();
          },
        );
      },
    );
  }
}
