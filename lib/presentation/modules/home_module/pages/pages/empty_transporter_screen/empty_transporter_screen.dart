import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/empty_transporter_screen/models/empty_transporter_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/empty_transporter_screen/widgets/user_location_sheet.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Empty transporter widget
class EmptyTransporterScreen extends StatelessWidget {
  /// Constructor
  const EmptyTransporterScreen({
    super.key,
    required this.emptyTransporterParams,
  });
  final EmptyTransporterParams emptyTransporterParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: context.l10n.noTransportersFound),
      backgroundColor: AppColors.ffF8F9FA,
      body: ValueListenableBuilder(
        valueListenable: emptyTransporterParams.homeProvider.isShowLoader,
        builder: (context, isLoading, child) =>
            AppLoader(isShowLoader: isLoading, child: child!),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Icon(Icons.error, color: AppColors.ffFFC107, size: AppSize.w80),
              Gap(AppSize.h10),
              Text(
                context.l10n.noProvider,
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                /// change this text with below text when
                /// waiting list feature is added
                context.l10n.thereIsNoProvider,

                /// DO NOT REMOVE THIS TEXT
                // context.l10n.thereIsNoProviderWaitList,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.ff6C757D,
                  fontSize: AppSize.h16,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              Gap(AppSize.h16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: AppSize.w16,
                children: [
                  Expanded(
                    child: AppButton(
                      isBottomBtn: false,
                      onPressed: () {
                        emptyTransporterParams.homeProvider
                          ..exclusivePickLocation.value = null
                          ..exclusiveDropLocation.value = null;
                        showLocationBottomSheet<void>(
                          context,
                          emptyTransporterParams.homeProvider,
                        );
                      },
                      text: context.l10n.exclusiveTrip,
                    ),
                  ),
                  // Expanded(
                  //   child: AppButton(
                  //     isBottomBtn: false,
                  //     text: context.l10n.joinWaitlist,
                  //     onPressed: () {},
                  //     isFillButton: false,
                  //   ),
                  // ),
                ],
              ),
              Gap(AppSize.h100),
            ],
          ),
        ),
      ),
    );
  }
}
