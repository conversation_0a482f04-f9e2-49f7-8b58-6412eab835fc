class CarCreationModel {
  const CarCreationModel({
    required this.carBrand,
    this.id,
    this.years,
  });

  factory CarCreationModel.fromJson(Map<String, dynamic> json) {
    return CarCreationModel(
      id: json['id'] as int?,
      carBrand: json['name'] as String?,
      years: (json['years'] as List?)
          ?.map(
            (year) =>
                CarYearModel.fromJson(Map<String, dynamic>.from(year as Map)),
          )
          .toList(),
    );
  }

  static List<CarCreationModel> fromList(List<dynamic> data) {
    return data
        .map(
          (item) =>
              CarCreationModel.fromJson(Map<String, dynamic>.from(item as Map)),
        )
        .toList();
  }

  final int? id;
  final String? carBrand; // `name` is required
  final List<CarYearModel>? years;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': carBrand,
      'years': years?.map((year) => year.toJson()).toList(),
    };
  }

  @override
  // ignore: hash_and_equals, avoid_equals_and_hash_code_on_mutable_classes
  bool operator ==(Object other) => other is CarCreationModel && other.id == id;
}

class CarYearModel {
  CarYearModel({
    required this.year,
    this.id,
    this.models,
  });

  factory CarYearModel.fromJson(Map<String, dynamic> json) {
    return CarYearModel(
      id: json['id'] as int?,
      year: json['year'] as int,
      models: (json['models'] as List?)
          ?.map(
            (model) =>
                CarModel.fromJson(Map<String, dynamic>.from(model as Map)),
          )
          .toList(),
    );
  }
  final int? id;
  final int year; // `year` is required
  final List<CarModel>? models;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'year': year,
      'models': models?.map((model) => model.toJson()).toList(),
    };
  }

  @override
  // ignore: avoid_equals_and_hash_code_on_mutable_classes, hash_and_equals
  bool operator ==(Object other) => other is CarYearModel && other.id == id;
}

class CarModel {
  CarModel({
    required this.model,
    this.id,
    this.sizes,
  });

  factory CarModel.fromJson(Map<String, dynamic> json) {
    return CarModel(
      id: json['id'] as int?,
      model: json['model'] as String,
      sizes: json['size'] == null
          ? null
          : CarSizeModel.fromJson(json['size'] as Map<String, dynamic>),
    );
  }
  final int? id;
  final String model; // `model` is required
  final CarSizeModel? sizes;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'model': model,
      'size': sizes?.toJson(),
    };
  }

  @override
  // ignore: avoid_equals_and_hash_code_on_mutable_classes, hash_and_equals
  bool operator ==(Object other) => other is CarModel && other.id == id;
}

class CarSizeModel {
  // `size` is required

  CarSizeModel({
    required this.size,
    required this.length,
    required this.exceptions,
    this.id,
  });

  factory CarSizeModel.fromJson(Map<String, dynamic> json) {
    return CarSizeModel(
      id: json['id'] as int?,
      size: json['size'] as num?,
      length: json['length'] as num?,
      exceptions: (json['exceptions'] as bool?) ?? false,
    );
  }
  final int? id;
  final num? size;
  final num? length;
  final bool exceptions;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'size': size,
      'length': length,
      'exceptions': exceptions,
    };
  }

  @override
  // ignore: hash_and_equals, avoid_equals_and_hash_code_on_mutable_classes
  bool operator ==(Object other) => other is CarModel && other.id == id;
}
