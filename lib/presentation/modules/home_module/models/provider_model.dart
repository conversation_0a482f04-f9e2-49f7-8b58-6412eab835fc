import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';

class ProviderDataModel {
  const ProviderDataModel({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory ProviderDataModel.fromJson(Map<String, dynamic> json) =>
      ProviderDataModel(
        count: json['count'] as num?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<ProviderListData>.from(
                (json['results'] as List?)?.map(
                      (x) =>
                          ProviderListData.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
      );
  final num? count;
  final String? previous;
  final String? next;
  final List<ProviderListData>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? <dynamic>[]
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

class ProviderListData {
  const ProviderListData({
    this.id,
    this.startStopLocation,
    this.endStopLocation,
    this.companyName,
    this.tripStartDate,
    this.isWinch = false,
    this.isExceptionsSupported = false,
    this.tripEndDate,
    this.costPerKilometer,
    this.customerStartStopLocation,
    this.customerEndStopLocation,
    this.distance,
    this.availableSlot,
    this.ratings,
    this.totalCost,
    this.defaultStopLocationCarDropDate,
    this.tripId,
  });

  factory ProviderListData.fromJson(Map<String, dynamic> json) =>
      ProviderListData(
        id: json['id'] as int?,
        startStopLocation: json['start_stop_location'] == null
            ? null
            : StopLocationData.fromJson(
                json['start_stop_location'] as Map<String, dynamic>,
              ),
        endStopLocation: json['end_stop_location'] == null
            ? null
            : StopLocationData.fromJson(
                json['end_stop_location'] as Map<String, dynamic>,
              ),
        companyName: json['company_name'] as String?,
        tripStartDate: json['trip_start_date'] == null
            ? null
            : DateTime.parse(json['trip_start_date'] as String).toLocal(),
        tripEndDate: json['trip_end_date'] == null
            ? null
            : DateTime.parse(json['trip_end_date'] as String).toLocal(),
        costPerKilometer: json['cost_per_kilometer'] as num?,
        customerStartStopLocation: json['customer_start_stop_location'] == null
            ? null
            : CustomerStopLocationModel.fromJson(
                json['customer_start_stop_location'] as Map<String, dynamic>,
              ),
        customerEndStopLocation: json['customer_end_stop_location'] == null
            ? null
            : CustomerStopLocationModel.fromJson(
                json['customer_end_stop_location'] as Map<String, dynamic>,
              ),
        distance: json['distance'] as num?,
        availableSlot: json['available_slot'] as num? ??
            json['spot_available_for_reservation'] as num?,
        ratings: json['ratings'] as num?,
        totalCost: json['total_cost'] as num?,
        isWinch: (json['winch'] as bool?) ?? false,
        isExceptionsSupported:
            (json['is_exceptions_supported'] as bool?) ?? false,
        defaultStopLocationCarDropDate:
            json['default_stop_location_car_drop_date'] == null
                ? null
                : DateTime.parse(
                    json['default_stop_location_car_drop_date'] as String,
                  ),
        tripId: json['trip_id'] as String?,
      );
  final int? id;
  final StopLocationData? startStopLocation;
  final StopLocationData? endStopLocation;
  final String? companyName;
  final DateTime? tripStartDate;
  final DateTime? tripEndDate;
  final num? costPerKilometer;
  final CustomerStopLocationModel? customerStartStopLocation;
  final CustomerStopLocationModel? customerEndStopLocation;
  final bool isWinch;
  final bool isExceptionsSupported;
  final num? distance;
  final num? availableSlot;
  final num? ratings;
  final num? totalCost;
  final DateTime? defaultStopLocationCarDropDate;
  final String? tripId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'start_stop_location': startStopLocation?.toJson(),
        'end_stop_location': endStopLocation?.toJson(),
        'company_name': companyName,
        'is_exceptions_supported': isExceptionsSupported,
        'trip_start_date': tripStartDate?.toUtc().toIso8601String(),
        'trip_end_date': tripEndDate?.toUtc().toIso8601String(),
        'cost_per_kilometer': costPerKilometer,
        'customer_start_stop_location': customerStartStopLocation?.toJson(),
        'customer_end_stop_location': customerEndStopLocation?.toJson(),
        'distance': distance,
        'available_slot': availableSlot,
        'ratings': ratings,
        'total_cost': totalCost,
        'default_stop_location_car_drop_date':
            defaultStopLocationCarDropDate?.toUtc().toIso8601String(),
        'trip_id': tripId,
        'winch': isWinch,
      };
}

class CustomerStopLocationModel {
  const CustomerStopLocationModel({
    this.id,
    this.name,
    this.address,
    this.estimatedArrivalDate,
  });

  factory CustomerStopLocationModel.fromJson(Map<String, dynamic> json) =>
      CustomerStopLocationModel(
        id: json['id'] as int?,
        name: json['name'] as String?,
        address: json['address'] == null
            ? null
            : CustomerEndStopLocationAddressModel.fromJson(
                json['address'] as Map<String, dynamic>,
              ),
        estimatedArrivalDate: json['estimated_arrival_date'] == null
            ? null
            : DateTime.parse(json['estimated_arrival_date'] as String)
                .toLocal(),
      );
  final int? id;
  final String? name;
  final CustomerEndStopLocationAddressModel? address;
  final DateTime? estimatedArrivalDate;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'address': address?.toJson(),
        'estimated_arrival_date':
            estimatedArrivalDate?.toUtc().toIso8601String(),
      };
}

class CustomerEndStopLocationAddressModel {
  const CustomerEndStopLocationAddressModel({
    this.city,
    this.state,
    this.street,
    this.country,
    this.latitude,
    this.longitude,
    this.postalCode,
    this.countryCode,
    this.neighborhood,
  });

  factory CustomerEndStopLocationAddressModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      CustomerEndStopLocationAddressModel(
        city: json['city'] as String?,
        state: json['state'] as String?,
        street: json['street'] as String?,
        country: json['country'] as String?,
        latitude: json['latitude'] as num?,
        longitude: json['longitude'] as num?,
        postalCode: json['postal_code'] as String?,
        countryCode: json['country_code'] as String?,
        neighborhood: json['neighborhood'] as String?,
      );
  final String? city;
  final String? state;
  final String? street;
  final String? country;
  final num? latitude;
  final num? longitude;
  final String? postalCode;
  final String? countryCode;
  final String? neighborhood;

  Map<String, dynamic> toJson() => {
        'city': city,
        'state': state,
        'street': street,
        'country': country,
        'latitude': latitude,
        'longitude': longitude,
        'postal_code': postalCode,
        'country_code': countryCode,
        'neighborhood': neighborhood,
      };
}
