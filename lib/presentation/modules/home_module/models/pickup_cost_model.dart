class PickupCostModel {
  PickupCostModel({
    required this.count,
    this.next,
    this.previous,
    required this.results,
  });

  factory PickupCostModel.fromJson(Map<String, dynamic> json) {
    return PickupCostModel(
      count: json['count'] as int,
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: List<ServiceModel>.from(
        (json['results'] as List<dynamic>)
            .map((x) => ServiceModel.fromJson(x as Map<String, dynamic>)),
      ),
    );
  }
  final int count;
  final String? next;
  final String? previous;
  final List<ServiceModel> results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results.map((x) => x.toJson()).toList(),
      };
}

class ServiceModel {
  ServiceModel({
    required this.id,
    required this.serviceType,
    required this.serviceFee,
    required this.minimumFee,
  });

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    return ServiceModel(
      id: json['id'] as int,
      serviceType: json['service_type'] as String,
      serviceFee: (json['service_fee'] as num).toDouble(),
      minimumFee: (json['minimum_fee'] as num).toDouble(),
    );
  }
  final int id;
  final String serviceType;
  final double serviceFee;
  final double minimumFee;

  Map<String, dynamic> toJson() => {
        'id': id,
        'service_type': serviceType,
        'service_fee': serviceFee,
        'minimum_fee': minimumFee,
      };
}
