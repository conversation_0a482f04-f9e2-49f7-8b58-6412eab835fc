import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/exclusive_trip_tab.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/shared_trips_tab.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/common_tabbar.dart';

/// Homepage
class HomeScreen extends StatefulWidget {
  /// Homepage Constructor
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late final TabController tabController;

  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    super.initState();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => HomeProvider(),
      child: Builder(
        builder: (context) {
          final homeProvider = context.read<HomeProvider>();
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            appBar: AppBar(
              automaticallyImplyLeading: false,
              toolbarHeight: AppSize.h46,
              flexibleSpace: AppPadding(
                top: MediaQuery.paddingOf(context).top,
                child: TabBar(
                  controller: tabController,
                  dividerColor: AppColors.transparent,
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorWeight: 3,
                  tabs: [
                    CommonTabBar(
                      title: context.l10n.sharedTrip,
                      index: 0,
                      animation: tabController.animation!,
                    ),
                    CommonTabBar(
                      title: context.l10n.exclusiveTrip,
                      index: 1,
                      animation: tabController.animation!,
                    ),
                  ],
                ),
              ),
            ),
            body: ValueListenableBuilder(
              valueListenable: homeProvider.isShowLoader,
              builder: (context, isLoading, child) =>
                  AppLoader(isShowLoader: isLoading, child: child!),
              child: TabBarView(
                controller: tabController,
                children: const [SharedTripsTab(), ExclusiveTripsTab()],
              ),
            ),
          );
        },
      ),
    );
  }
}
