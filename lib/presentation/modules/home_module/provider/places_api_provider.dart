import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:geocoding/geocoding.dart' as geo;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:transport_match/env/env.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/suggestion_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/utils/logger.dart';

class PlaceApiProvider {
  /// HTTP client for API requests
  final client = http.Client();

  /// Google Maps API key based on platform
  final String apiKey = Platform.isAndroid
      ? AppEnv().googleMapAndroidKey
      : AppEnv().googleMapIosKey;

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Fetch address suggestions from Google Places API
  /// [input] is the search text
  /// [lang] is the language code
  Future<List<SuggestionModel>> fetchSuggestions({
    required String input,
    required String lang,
  }) async {
    if (_isClosed) return [];

    try {
      final request =
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$input&language=$lang&key=$apiKey';
      final response = await client.get(Uri.parse(request));

      if (response.statusCode == 200) {
        final result = json.decode(response.body) as Map<String, dynamic>;
        if (result['status'] == 'OK') {
          return (result['predictions'] as List)
              .map<SuggestionModel>(
                (p) => SuggestionModel(
                  p['place_id'].toString(),
                  p['description'].toString(),
                ),
              )
              .toList();
        }
        if (result['status'] == 'ZERO_RESULTS') {
          return [];
        }
        throw Exception(result['error_message']);
      } else {
        throw Exception('Failed to fetch suggestion');
      }
    } catch (e) {
      'fetchSuggestions error: $e'.logE;
      return [];
    }
  }

  /// Fetch place details from Google Places API
  /// [description] is the place description
  /// [addressList] is the list of suggestions
  Future<LatLng?> fetchPlaceDetails({
    required String description,
    required List<SuggestionModel> addressList,
  }) async {
    if (_isClosed) return null;

    try {
      final placeId = addressList
          .firstWhere((element) => element.description == description)
          .placeId;

      final request =
          'https://maps.googleapis.com/maps/api/place/details/json?placeid=$placeId&key=$apiKey';
      final response = await client.get(Uri.parse(request));

      if (response.statusCode == 200) {
        final result = json.decode(response.body) as Map<String, dynamic>;
        if (result['status'] == 'OK') {
          return LatLng(
            result['result']['geometry']['location']['lat'] as double,
            result['result']['geometry']['location']['lng'] as double,
          );
        }
        if (result['status'] == 'ZERO_RESULTS') {
          return null;
        }
        throw Exception(result['error_message']);
      } else {
        throw Exception('Failed to fetch suggestion');
      }
    } catch (e) {
      'fetchPlaceDetails error: $e'.logE;
      return null;
    }
  }

  /// Fetch place details
  // Future<LatLng?> getAddress(LatLng latLng) async {
  //   final request = 'https://maps.googleapis.com/maps/api/geocode/json?'
  //       'latlng=${latLng.latitude},${latLng.longitude}&key=$apiKey';
  //   final response = await client.get(Uri.parse(request));

  //   if (response.statusCode == 200) {
  //     final result = json.decode(response.body) as Map<String, dynamic>;
  //     log('===>>> get google address result = $result');
  //     if (result['status'] == 'OK') {
  //       return LatLng(
  //         result['results'][0]['geometry']['location']['lat'] as double,
  //         result['results'][0]['geometry']['location']['lng'] as double,
  //       );
  //     }
  //     if (result['status'] == 'ZERO_RESULTS') {
  //       return null;
  //     }
  //     throw Exception(result['error_message']);
  //   } else {
  //     throw Exception('Failed to fetch suggestion');
  //   }
  // }

  /// Get route list between two locations
  /// [origin] is the starting point
  /// [destination] is the ending point
  Future<(List<LatLng>, double)> getRouteList({
    required LatLng origin,
    required LatLng destination,
  }) async {
    if (_isClosed) return (<LatLng>[], 0.0);

    final routeList = <LatLng>[];
    double distance = 0;

    try {
      final request =
          'https://maps.googleapis.com/maps/api/directions/json?origin='
          '${origin.latitude},${origin.longitude}&destination=${destination.latitude}'
          ',${destination.longitude}&mode=DRIVING&key=$apiKey';

      final response = await client.get(Uri.parse(request));

      if (response.statusCode == 200) {
        /// Extract route coordinates
        final routeCoordinates = _extractRouteCoordinates(
          json.decode(response.body) as Map<String, dynamic>,
        );
        if (!_isClosed) {
          await getBoundingBox(routeCoordinates);
        }
        for (final coordinate in routeCoordinates) {
          routeList.add(LatLng(coordinate.latitude, coordinate.longitude));
        }

        /// Extract distance
        final result = json.decode(response.body) as Map<String, dynamic>;
        final list = result['routes'];
        if (list is List) {
          if (list.isNotEmpty) {
            if (list.first['legs'] is List) {
              final legs = list.first['legs'] as List;
              if (legs.isNotEmpty) {
                if (legs.first['steps'] is List) {
                  final steps = legs.first['steps'] as List;
                  if (steps.isNotEmpty) {
                    for (final step in steps) {
                      if (step['start_location'] is Map) {
                        final startLocation =
                            step['start_location'] as Map<String, dynamic>;
                        if (startLocation['lat'] is double &&
                            startLocation['lng'] is double) {
                          if (step['distance']['value'] is num) {
                            distance += step['distance']['value'] as num;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    } catch (e) {
      'getRouteList error: $e'.logE;
    }

    return (routeList, distance);
  }

  Future<AddressModel?> getAccurateAddress({
    required double latitude,
    required double longitude,
    required bool isClosed,
    String? selectedAddress,
    required String googleApiKey,
  }) async {
    if (isClosed) return null;

    try {
      final googleUrl = Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=$latitude,$longitude&key=$googleApiKey',
      );

      final response = await http.get(googleUrl);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200 &&
          data['results'] != null &&
          (data['results'] as List? ?? []).isNotEmpty) {
        final result = data['results'][0];
        final components = result['address_components'] as List<dynamic>;

        String? getComponent(List<String> types) {
          for (final c in components) {
            final t = List<String>.from(c['types'] as List<dynamic>);
            if (types.any(t.contains)) return c['long_name'] as String?;
          }
          return null;
        }

        final formattedAddress = selectedAddress ?? result['formatted_address'] as String?;

        final city =
            getComponent(['locality']) ??
                getComponent(['administrative_area_level_3']);
        final postalCode = getComponent(['postal_code']);
        final state = getComponent(['administrative_area_level_1']);
        final country = getComponent(['country']);
        final countryCode = getComponent([
          'country',
        ])?.substring(0, 2).toUpperCase();
        final street = getComponent(['route']);
        final number = getComponent(['street_number']);
        final fullStreet = [number, street].where((e) => e != null).join(', ');

        return AddressModel(
          latitude: latitude.toString(),
          longitude: longitude.toString(),
          address: formattedAddress ?? '',
          city: city ?? '',
          country: country ?? '',
          postalCode: postalCode ?? '',
          state: state ?? '',
          countryCode: countryCode ?? '',
          street: fullStreet.isNotEmpty ? fullStreet : formattedAddress ?? '',
        );
      }
    } catch (e) {
      ('Google geocoding failed: $e').logE;
    }

    // Fallback to Flutter geocoding
    try {
      final placemarks = await geo.placemarkFromCoordinates(
        latitude,
        longitude,
      );
      if (isClosed) return null;

      if (placemarks.isNotEmpty) {
        final p = placemarks.first;
        final fallbackAddress =
            selectedAddress ??
                '${p.name ?? ''}, ${p.locality ?? ''}, ${p.administrativeArea ?? ''}, ${p.country ?? ''}';

        return AddressModel(
          latitude: latitude.toString(),
          longitude: longitude.toString(),
          address: fallbackAddress,
          city: p.locality ?? '',
          country: p.country ?? '',
          postalCode: p.postalCode ?? '',
          state: p.administrativeArea ?? '',
          countryCode: p.isoCountryCode ?? '',
          street: fallbackAddress,
        );
      }
    } catch (e) {
      ('Fallback placemark failed: $e').logE;
    }

    return null;
  }

  /// Get bounding box of the route
  /// [routePoints] is the list of route points
  Future<void> getBoundingBox(List<LatLng> routePoints) async {
    if (_isClosed || routePoints.isEmpty) return;

    try {
      // Compute min/max lat/lng
      var minLat = routePoints.first.latitude;
      var maxLat = routePoints.first.latitude;
      var minLng = routePoints.first.longitude;
      var maxLng = routePoints.first.longitude;

      for (final point in routePoints) {
        if (point.latitude < minLat) minLat = point.latitude;
        if (point.latitude > maxLat) maxLat = point.latitude;
        if (point.longitude < minLng) minLng = point.longitude;
        if (point.longitude > maxLng) maxLng = point.longitude;
      }

      // Define bounding box corners
      final topLeft = LatLng(maxLat, minLng);
      final topRight = LatLng(maxLat, maxLng);
      final bottomLeft = LatLng(minLat, minLng);
      final bottomRight = LatLng(minLat, maxLng);

      log('Bounding Box Coordinates:');
      log('Top-Left (NW): $topLeft');
      log('Top-Right (NE): $topRight');
      log('Bottom-Left (SW): $bottomLeft');
      log('Bottom-Right (SE): $bottomRight');
    } catch (e) {
      'getBoundingBox error: $e'.logE;
    }
  }

  /// Decode polyline string to list of coordinates
  /// [encoded] is the encoded polyline string
  List<LatLng> _decodePolyline(String encoded) {
    if (_isClosed) return [];

    try {
      final points = <LatLng>[];
      var index = 0;
      final len = encoded.length;
      var lat = 0;
      var lng = 0;

      while (index < len) {
        var shift = 0;
        var result = 0;
        int b;
        do {
          b = encoded.codeUnitAt(index++) - 63;
          result |= (b & 0x1F) << shift;
          shift += 5;
        } while (b >= 0x20);
        final deltaLat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
        lat += deltaLat;

        shift = 0;
        result = 0;
        do {
          b = encoded.codeUnitAt(index++) - 63;
          result |= (b & 0x1F) << shift;
          shift += 5;
        } while (b >= 0x20);
        final deltaLng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
        lng += deltaLng;

        points.add(LatLng(lat / 1E5, lng / 1E5));
      }
      return points;
    } catch (e) {
      '_decodePolyline error: $e'.logE;
      return [];
    }
  }

  /// Extract route coordinates from API response
  /// [route] is the route data from API response
  List<LatLng> _extractRouteCoordinates(
    Map<String, dynamic> route,
  ) {
    if (_isClosed) return [];

    try {
      final coordinates = <LatLng>[];

      if (route.containsKey('routes') && (route['routes'] as List).isNotEmpty) {
        for (final leg in route['routes'][0]['legs'] as List) {
          for (final step in leg['steps'] as List) {
            coordinates
                .addAll(_decodePolyline(step['polyline']['points'] as String));
          }
        }
      }

      return coordinates;
    } catch (e) {
      '_extractRouteCoordinates error: $e'.logE;
      return [];
    }
  }



  /// Dispose method to clean up resources
  void dispose() {
    _isClosed = true;
    client.close();
  }
}
