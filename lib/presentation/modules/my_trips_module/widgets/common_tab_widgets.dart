import 'package:flutter/material.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// common Tab Widgets
class CommonTabWidgets extends StatelessWidget {
  /// Constructor
  const CommonTabWidgets({
    required this.text,
    required this.tabIndex,
    super.key,
    required this.selectedIndex,
    required this.onTap,
  });

  /// for tab text
  final String text;

  /// for tab value change
  final int tabIndex;

  /// for selected value
  final int selectedIndex;

  /// on tap handler
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    final isSelected = selectedIndex == tabIndex;
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryColor : AppColors.white,
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            border: Border.all(
              color: isSelected
                  ? AppColors.transparent
                  : AppColors.unSelectedColor,
            ),
          ),
          padding: EdgeInsets.symmetric(vertical: AppSize.h8),
          alignment: Alignment.center,
          child: Text(
            text,
            style: TextStyle(
              color: isSelected ? AppColors.white : AppColors.unSelectedColor,
              fontSize: AppSize.sp12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
