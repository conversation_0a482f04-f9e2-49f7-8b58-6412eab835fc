import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';

class NoTripWidget extends StatelessWidget {
  const NoTripWidget({super.key, this.text});
  final String? text;

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: context.height * 0.7,
          ),
          child: Center(
            child: Text(text ?? context.l10n.noTripFound),
          ),
        ),
      ],
    );
  }
}
