import 'package:flutter/material.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/accepted_trips_tab.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/requested_trips_tab.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/common_tabbar.dart';
import 'package:transport_match/widgets/keep_alive_wrapper.dart';

/// MyTrips page
class MyTripScreen extends StatefulWidget {
  const MyTripScreen({super.key});

  @override
  State<MyTripScreen> createState() => _MyTripScreenState();
}

class _MyTripScreenState extends State<MyTripScreen>
    with SingleTickerProviderStateMixin {
  late final TabController tabController;

  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    super.initState();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.pageBGColor,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        // toolbarHeight: AppSize.h40,
        flexibleSpace: AppPadding(
          top: MediaQuery.paddingOf(context).top,
          child: TabBar(
            controller: tabController,
            dividerColor: AppColors.transparent,
            indicatorSize: TabBarIndicatorSize.tab,
            indicatorWeight: 3,
            tabs: [
              CommonTabBar(
                title: context.l10n.requestedTrips,
                index: 0,
                animation: tabController.animation!,
              ),
              CommonTabBar(
                title: context.l10n.acceptedTrips,
                index: 1,
                animation: tabController.animation!,
              ),
            ],
          ),
        ),
      ),
      body: TabBarView(
        controller: tabController,
        children: const [
          RequestedTripsTab(),
          KeepAliveWrapper(child: AcceptedTripsTab()),
        ],
      ),
    );
  }
}
