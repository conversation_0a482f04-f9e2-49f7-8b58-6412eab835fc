import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class AcceptedTripProvider extends ChangeNotifier {
  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Page controller for tab view
  final pageController = PageController();

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  /// data list
  final upcomingTripList = ValueNotifier<List<TripModel>>([]);
  final ongoingTripList = ValueNotifier<List<TripModel>>([]);
  final completedTripList = ValueNotifier<List<TripModel>>([]);

  /// loaders
  final upcomingTripLoader = ValueNotifier<bool>(false);
  final ongoingTripLoader = ValueNotifier<bool>(false);
  final completedTripLoader = ValueNotifier<bool>(false);

  /// cancel tokens
  CancelToken? upcomingTripToken;
  CancelToken? ongoingTripToken;
  CancelToken? completeTripToken;

  /// next api urls
  final nextUpcomingUrl = ValueNotifier<String?>(null);
  final nextOngoingUrl = ValueNotifier<String?>(null);
  final nextCompleteUrl = ValueNotifier<String?>(null);

  final tripRepo = Injector.instance<TripRepository>();

  /// acceptedTabValue for changing tab
  int acceptedTabValue = 0;

  /// Update AcceptedTabValue Value
  /// [tabIndex] is the new tab index
  void acceptedTabValueChange(int tabIndex) {
    if (_isClosed) return;

    try {
      if (pageController.page != tabIndex) {
        acceptedTabValue = tabIndex;
        pageController.animateToPage(
          tabIndex,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
        notify();
      }
    } catch (e) {
      'acceptedTabValueChange error: $e'.logE;
    }
  }

  /// Get accepted trip list
  /// [acceptedTabValue] is the tab index
  /// [isPagination] flag for pagination
  /// [isWantShowLoader] flag to show loader
  Future<void> getAcceptedTripList(
    int acceptedTabValue, {
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (_isClosed) return;

    /// assign new value and remove old value
    switch (acceptedTabValue) {
      case 0:
        upcomingTripToken?.cancel();
        upcomingTripToken = CancelToken();
        if (!isPagination) nextUpcomingUrl.value = null;
      case 1:
        ongoingTripToken?.cancel();
        ongoingTripToken = CancelToken();
        if (!isPagination) nextOngoingUrl.value = null;
      case 2:
        completeTripToken?.cancel();
        completeTripToken = CancelToken();
        if (!isPagination) nextCompleteUrl.value = null;
      default:
    }

    /// call api
    await _commonFuncGetAcceptedTripList(
      isWantShowLoader: isWantShowLoader,
      isPagination: isPagination,
      loadingFunction: ({required bool isLoad}) {
        if (acceptedTabValue == 0) {
          upcomingTripLoader.value = isLoad;
        } else if (acceptedTabValue == 1) {
          ongoingTripLoader.value = isLoad;
        } else {
          completedTripLoader.value = isLoad;
        }
      },

      /// below function give me next url if there were paginated data
      nextUrl: switch (acceptedTabValue) {
        0 => nextUpcomingUrl,
        1 => nextOngoingUrl,
        _ => nextCompleteUrl,
      }
          .value,

      /// below function give me final response get from api
      successFunction: ({required List<TripModel> dataList, String? nextUrl}) {
        switch (acceptedTabValue) {
          case 0:
            upcomingTripList.value = dataList;
            nextUpcomingUrl.value = nextUrl;
          case 1:
            ongoingTripList.value = dataList;
            nextOngoingUrl.value = nextUrl;
          case 2:
            completedTripList.value = dataList;
            nextCompleteUrl.value = nextUrl;
          default:
        }
        notify();
      },
      acceptedTabValue: acceptedTabValue,
    );
  }

  /// get cancel token based upon accepted tab value
  CancelToken? getCancelToken(int acceptedTabValue) {
    switch (acceptedTabValue) {
      case 0:
        return upcomingTripToken;
      case 1:
        return ongoingTripToken;
      case 2:
        return completeTripToken;
      default:
        return null;
    }
  }

  /// common function for getting accepted trip lists
  Future<void> _commonFuncGetAcceptedTripList({
    required void Function({required bool isLoad}) loadingFunction,
    required void Function({
      required List<TripModel> dataList,
      String? nextUrl,
    }) successFunction,
    required String? nextUrl,
    bool isPagination = false,
    bool isWantShowLoader = false,
    required int acceptedTabValue,
  }) async {
    if (_isClosed) return;
    try {
      final url = '${EndPoints.getAcceptedTrip}?booking_status='
          '${switch (acceptedTabValue) {
        0 => AcceptedTripType.CONFIRMED.name,
        1 => AcceptedTripType.ONGOING.name,
        _ => AcceptedTripType.COMPLETED.name,
      }}';
      if (isWantShowLoader) loadingFunction(isLoad: true);
      final response = await tripRepo.getRequestedTripList(
        ApiRequest(
          path: isPagination ? nextUrl ?? url : url,
          cancelToken: getCancelToken(acceptedTabValue),
        ),
      );
      final dummyList = [
        ...switch (acceptedTabValue) {
          0 => upcomingTripList.value,
          1 => ongoingTripList.value,
          _ => completedTripList.value,
        },
      ];
      response.when(
        success: (data) {
          if (_isClosed ||
              (getCancelToken(acceptedTabValue)?.isCancelled ?? true)) {
            return;
          }
          loadingFunction(isLoad: false);
          if (!isPagination) dummyList.clear();
          dummyList.addAll(data.results ?? []);
          successFunction(
            dataList: dummyList,
            nextUrl: data.next,
          );
          notify();
        },
        error: (error) {
          if (_isClosed ||
              (getCancelToken(acceptedTabValue)?.isCancelled ?? true)) {
            return;
          }
          loadingFunction(isLoad: false);

          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed ||
          (getCancelToken(acceptedTabValue)?.isCancelled ?? true)) {
        return;
      }
      loadingFunction(isLoad: false);
      '_commonFuncGetAcceptedTripList error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    upcomingTripToken?.cancel();
    ongoingTripToken?.cancel();
    completeTripToken?.cancel();

    super.dispose();
  }
}
