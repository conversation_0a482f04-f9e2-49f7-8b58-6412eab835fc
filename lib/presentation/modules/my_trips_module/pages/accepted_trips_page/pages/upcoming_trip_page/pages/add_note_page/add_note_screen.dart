import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/pages/add_note_page/model/add_note_param.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/pages/add_note_page/provider/add_note_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Add Note Page Ui
class AddNoteScreen extends StatelessWidget {
  /// Add Note Constructor
  const AddNoteScreen({super.key, required this.addNoteParam})
    : assert(addNoteParam != null, 'Please pass AddNoteParam');

  final AddNoteParam? addNoteParam;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AddNoteProvider(),
      builder: (context, child) {
        final addNoteProvider = Provider.of<AddNoteProvider>(context);
        return Form(
          key: addNoteProvider.formKey,
          child: Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(title: context.l10n.addNotes),
            bottomNavigationBar: Selector<AddNoteProvider, bool>(
              selector: (p0, addNoteProvider) => addNoteProvider.isAddNotesLoad,
              builder: (context, isAddNotesLoad, child) {
                return AppButton(
                  text: context.l10n.save,
                  onPressed: isAddNotesLoad
                      ? null
                      : () {
                          final form = addNoteProvider.formKey.currentState;
                          if (form?.validate() ?? false) {
                            addNoteProvider.addNotes(
                              addNoteParam?.tripId ?? '0',
                              context,
                              addNoteProvider.noteController.text,
                            );
                          }
                        },
                );
              },
            ),
            body: Selector<AddNoteProvider, bool>(
              selector: (p0, addNoteProvider) => addNoteProvider.isAddNotesLoad,
              builder: (context, isAddNotesLoad, child) {
                return AppLoader(isShowLoader: isAddNotesLoad, child: child!);
              },
              child: ListView(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSize.appPadding,
                  vertical: AppSize.h8,
                ),
                children: [
                  AppTextFormField(
                    controller: addNoteProvider.noteController,
                    keyboardType: TextInputType.multiline,
                    maxLine: 6,
                    minLine: 4,
                    title: context.l10n.writeNotes,
                    contentHeight: AppSize.r16,
                    hintText: context.l10n.writeUrMessage,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return context.l10n.pleaseAddNotes;
                      }
                      return null;
                    },
                    // contentWidth: AppSize.r26,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
