import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/pages/add_note_page/model/add_note_param.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/upcoming_tab_screen.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';

///Common Card Widgets for tab
class UpcomingCommonCard extends StatelessWidget {
  ///Common Card Constructor
  const UpcomingCommonCard({
    super.key,
    required this.tripData,
    required this.onBack,
  });

  final TripModel tripData;
  final Function(NoteModel? note, {bool isRemove}) onBack;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NoOfVehicleWidget(
                  noOfVehicle: tripData.noOfCars?.toString() ?? '-',
                  isTitleWidget: true,
                ),
                GestureDetector(
                  onTap: () => AppNavigationService.pushNamed(
                    context,
                    AppRoutes.tripsTripDetailsScreen,
                    extra: TripDetailsParams(
                      isCompleted: false,
                      id: tripData.id?.toString() ?? '',
                      onBack: () => onBack(null, isRemove: true),
                    ),
                  ),
                  child: Text(
                    context.l10n.details,
                    style: context.textTheme.bodyLarge?.copyWith(
                      color: AppColors.ff0087C7,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.sp16),
              child: LocationInfoWidget(
                /// latitude and longitude
                startLatitude:
                    tripData.startStopLocation?.address?.latitude ??
                    tripData.userStartLocation?.latitude,
                startLongitude:
                    tripData.startStopLocation?.address?.longitude ??
                    tripData.userStartLocation?.longitude,
                endLatitude:
                    tripData.endStopLocation?.address?.latitude ??
                    tripData.userEndLocation?.latitude,
                endLongitude:
                    tripData.endStopLocation?.address?.longitude ??
                    tripData.userEndLocation?.longitude,

                /// date and time
                startLocationTitle:
                    tripData.startStopLocation?.fullAddress ??
                    tripData.userStartLocation?.street ??
                    '',
                startLocationDate:
                    booking(tripData.bookingDetails ?? [], isStart: true)
                        ?.intermediateStartStopLocation
                        ?.estimatedArrivalDate
                        ?.monthDateFormate ??
                    tripData.customerStartDate?.monthDateFormate ??
                    '',
                endLocationTitle:
                    tripData.endStopLocation?.fullAddress ??
                    tripData.userEndLocation?.street ??
                    '',
                endLocationDate:
                    booking(tripData.bookingDetails ?? [])
                        ?.intermediateEndStopLocation
                        ?.estimatedArrivalDate
                        ?.monthDateFormate ??
                    tripData.customerEndDate?.monthDateFormate ??
                    '',
                centerWidget: (child) => Stack(
                  children: [
                    SizedBox(height: AppSize.h25, child: child),
                    Positioned(
                      right: AppSize.r5,
                      left: AppSize.r5,
                      bottom: AppSize.r5,
                      child: AppAssets.iconsCar.image(
                        height: AppSize.h18,
                        fit: BoxFit.fitHeight,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (tripData.bookingDetails?.firstOrNull?.notes?.firstOrNull ==
                null)
              GestureDetector(
                onTap: () => AppNavigationService.pushNamed(
                  context,
                  AppRoutes.tripsAddNoteScreen,
                  extra: AddNoteParam(
                    tripId:
                        tripData.bookingDetails?.firstOrNull?.id?.toString() ??
                        '',
                  ),
                  afterBack: (value) {
                    if (value is NoteModel) {
                      onBack(value);
                    }
                  },
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppAssets.iconsNotes.image(
                      height: AppSize.h16,
                      width: AppSize.h16,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: AppSize.h4),
                      child: Text(
                        context.l10n.addNotes,
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: AppColors.ff495057,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              Text(
                '${context.l10n.notes}: '
                '${tripData.bookingDetails?.firstOrNull?.notes?.firstOrNull?.description}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.ff6C757D,
                  fontSize: AppSize.sp12,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
