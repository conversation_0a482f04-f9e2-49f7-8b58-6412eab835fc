import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class AddNoteProvider extends ChangeNotifier {
  bool _isClosed = false;
  final tripRepo = Injector.instance<TripRepository>();
  final noteController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  /// this function is called to add notes
  CancelToken? addNotesToken;
  bool isAddNotesLoad = false;
  Future<dynamic> addNotes(
    String id,
    BuildContext context,
    String notes,
  ) async {
    addNotesToken?.cancel();
    addNotesToken = CancelToken();
    try {
      if (_isClosed) return;
      const url = EndPoints.createNotes;
      isAddNotesLoad = true;
      notify();
      final response = await tripRepo.addNotes(
        ApiRequest(
          path: url,
          cancelToken: addNotesToken,
          data: {
            'booking_detail': id,
            'description': notes,
          },
        ),
      );

      response.when(
        success: (data) {
          if (_isClosed || (addNotesToken?.isCancelled ?? true)) return;

          context.l10n.bookingNoteCreatedSuccess.showSuccessAlert();
          AppNavigationService.pop(
            context,
            NoteModel(bookingDetail: int.parse(id), description: notes),
          );
        },
        error: (error) {
          if (_isClosed || (addNotesToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isAddNotesLoad = false;
      notify();
    } catch (e) {
      if (_isClosed || (addNotesToken?.isCancelled ?? true)) return;
      isAddNotesLoad = false;
      notify();
      'Add notes error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    addNotesToken?.cancel();
    noteController.clear();
    super.dispose();
  }
}
