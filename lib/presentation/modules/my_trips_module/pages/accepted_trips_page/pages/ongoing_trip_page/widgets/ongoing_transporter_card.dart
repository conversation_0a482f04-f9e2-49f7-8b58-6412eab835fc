import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/dashed_divider.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';
import 'package:transport_match/widgets/title_info.dart';

///Ongoing Transporter Card Widgets
class OngoingTransporterCard extends StatelessWidget {
  /// Constructor
  const OngoingTransporterCard({super.key, required this.data});

  final TripModel data;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with "Your Shipment" and "Edit"
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NoOfVehicleWidget(
                  noOfVehicle: data.noOfCars?.toString() ?? '-',
                  isTitleWidget: true,
                ),
                Row(
                  spacing: AppSize.w16,
                  children: [
                    GestureDetector(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.tripsTripDetailsScreen,
                        extra: TripDetailsParams(
                          isCompleted: false,
                          id: data.id?.toString() ?? '',
                        ),
                        // TripDetailsScreen(
                        //   isCompleted: false,
                        //   id: data.id?.toString() ?? '',
                        // ),
                      ),
                      // onTap: () => context
                      //     .read<TripDetailProvider>()
                      //     .getTripDetails(data.id.toString(), context),
                      child: Text(
                        context.l10n.details,
                        style: context.textTheme.bodyLarge?.copyWith(
                          color: AppColors.ff0087C7,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    // GestureDetector(
                    //   onTap: () => AppNavigationService.pushNamed(
                    //     context,
                    //     ChatScreen(
                    //       title: title,
                    //       receiverId: receiverId,
                    //       bookingDetailId: bookingDetailId,
                    //       chatType: chatType,
                    //       customerChatRoomParameter: customerChatRoomParameter,
                    //     ),
                    //   ),
                    //   child: AppAssets.iconsChat.image(
                    //     height: AppSize.h24,
                    //     width: AppSize.h24,
                    //   ),
                    // ),
                  ],
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppAssets.iconsLocationOrigin.image(
                    height: AppSize.h16,
                    width: AppSize.h16,
                  ),
                  const Expanded(
                    child: Row(
                      children: [
                        Flexible(child: Divider()),
                        DashedDivider(),
                      ],
                    ),
                  ),
                  AppAssets.iconsLocation.image(
                    height: AppSize.h16,
                    width: AppSize.h16,
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: AppSize.h16),
              child: TitleInfo(
                title: context.l10n.transporter,
                subTitle: data.bookingDetails?.length == 1 ?data.bookingDetails?.first.tripData?.companyName??'-' : (data.bookingDetails??[]).isEmpty?'-': '${data.bookingDetails?.length} ${context.l10n.transporter}',
                subTitleFontWeight: FontWeight.w500,
              ),
            ),
            LocationInfoWidget(
              /// user start and end location
              startLatitude:
                  data.startStopLocation?.address?.latitude ??
                  data.userStartLocation?.latitude,
              startLongitude:
                  data.startStopLocation?.address?.longitude ??
                  data.userStartLocation?.longitude,
              endLatitude:
                  data.endStopLocation?.address?.latitude ??
                  data.userEndLocation?.latitude,
              endLongitude:
                  data.endStopLocation?.address?.longitude ??
                  data.userEndLocation?.longitude,

              /// date and time
              startLocationTitle:
                  data.startStopLocation?.fullAddress ??
                  data.userStartLocation?.street ??
                  '',
              startLocationDate: data.customerStartDate?.monthDateFormate ?? '',
              endLocationTitle:
                  data.endStopLocation?.fullAddress ??
                  data.userEndLocation?.street ??
                  '',
              endLocationDate: data.customerEndDate?.monthDateFormate ?? '',
            ),
          ],
        ),
      ),
    );
  }
}
