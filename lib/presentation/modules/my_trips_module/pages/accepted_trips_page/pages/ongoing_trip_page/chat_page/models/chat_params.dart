import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';

/// Parameter model for chat screen.
class ChatParams {
  /// Constructor
  const ChatParams({
    required this.title,
    required this.receiverId,
    required this.bookingDetailId,
    required this.customerChatRoomParameter,
    this.addDriverChatRoomData,
  });

  /// Chat title
  final String title;

  /// Receiver ID for the chat
  final int receiverId;

  /// Booking detail ID for the chat
  final int bookingDetailId;

  // /// Chat type
  // final ChatType chatType;

  /// Chat Room Object
  final ChatRoomDetailModel? customerChatRoomParameter;

  /// this function is used to add driver chant room data
  /// manually coz first time it will be null
  final void Function(int chatRoomId, bool isActive)? addDriverChatRoomData;
}
