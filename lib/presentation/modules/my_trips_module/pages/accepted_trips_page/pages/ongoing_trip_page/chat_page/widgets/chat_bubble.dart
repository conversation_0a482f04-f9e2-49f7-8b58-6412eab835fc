import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_messages_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_padding.dart';

class ChatBubble extends StatelessWidget {
  const ChatBubble({
    super.key,
    required this.message,
    required this.isUser,
    this.isNextMsgSameSender = false,
  });

  final Message message;
  final bool isUser;
  final bool isNextMsgSameSender;

  @override
  Widget build(BuildContext context) {
    return AppPadding(
      // right: isUser ? AppSize.w8 : AppSize.w26,
      // left: isUser ? AppSize.w26 : AppSize.w8,
      bottom: isNextMsgSameSender ? AppSize.h6 : AppSize.h10,
      child: Align(
        alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.75,
          ),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppSize.w10,
              vertical: AppSize.h4,
            ),
            decoration: BoxDecoration(
              gradient: isUser
                  ? const LinearGradient(
                      colors: [
                        AppColors.primaryColor,
                        AppColors.primaryColor,
                        Color.fromARGB(255, 4, 127, 180),
                      ],
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                    )
                  : null,
              color: isUser ? null : const Color.fromARGB(255, 232, 246, 252),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppSize.r8),
                topRight: Radius.circular(AppSize.r8),
                bottomLeft: Radius.circular(isUser ? AppSize.r8 : 0),
                bottomRight: Radius.circular(isUser ? 0 : AppSize.r8),
              ),
              boxShadow: [
                BoxShadow(
                  color: isUser
                      ? Colors.black12
                      : Colors.black12.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              spacing: AppSize.h4,
              children: [
                ReadMoreText(
                  message.message ?? '',
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w400,
                    color: isUser ? AppColors.white : AppColors.ff495057,
                  ),
                  trimLength: 150,
                  lessStyle: context.textTheme.titleMedium?.copyWith(
                    fontSize: AppSize.sp12,
                    fontWeight: FontWeight.w400,
                    color: isUser
                        ? AppColors.white.withValues(alpha: 0.95)
                        : AppColors.primaryColor,
                  ),
                  trimCollapsedText: context.l10n.readMore,
                  trimExpandedText: ' ${context.l10n.readLess}',
                  moreStyle: context.textTheme.titleMedium?.copyWith(
                    fontSize: AppSize.sp12,
                    fontWeight: FontWeight.w400,
                    color: isUser
                        ? AppColors.white.withValues(alpha: 0.95)
                        : AppColors.primaryColor,
                  ),
                ),
                Text(
                  message.createdAt.timeOnly,
                  style: context.textTheme.bodySmall!.copyWith(
                    color: isUser
                        ? AppColors.white.withAlpha(178)
                        : AppColors.ff6C757D,
                    fontSize: AppSize.sp10,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// class ChatBubble extends StatelessWidget {
//   const ChatBubble({
//     super.key,
//     required this.message,
//     required this.isUser,
//   });
//   final Message message;
//   final bool isUser;

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.only(
//         right: isUser ? AppSize.w8 : AppSize.w26,
//         bottom: AppSize.h16,
//       ),
//       child: Align(
//         alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
//         child: Container(
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.all(
//               Radius.circular(AppSize.r6),
//             ),
//             color: isUser ? AppColors.primaryColor : AppColors.white,
//           ),
//           padding: EdgeInsets.symmetric(
//             horizontal: AppSize.w16,
//             vertical: AppSize.h8,
//           ),
//           child: Column(
//             crossAxisAlignment:
//                 isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//             children: [
//               Text(
//                 message.message ?? '',
//                 style: Theme.of(context).textTheme.bodyLarge!.copyWith(
//                       fontWeight: FontWeight.w400,
//                       color: isUser ? AppColors.white : AppColors.ff495057,
//                     ),
//               ),
//               Gap(AppSize.h4),
//               Text(
//                 message.createdAt.timeOnly,
//                 style: Theme.of(context).textTheme.bodySmall!.copyWith(
//                       color: isUser
//                           ? AppColors.white.withAlpha(178)
//                           : AppColors.ff6C757D,
//                       fontSize: 10,
//                     ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
