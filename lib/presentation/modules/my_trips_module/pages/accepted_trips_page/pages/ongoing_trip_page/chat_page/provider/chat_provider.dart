import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_messages_model.dart';
import 'package:transport_match/services/socket_service/models/chat_room_model.dart';
import 'package:transport_match/services/socket_service/socket_service_impl.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

/// Chat Provider for real-time messaging
class ChatProvider extends ChangeNotifier {
  /// Constructor
  ChatProvider({
    required this.receiverId,
    required this.bookingDetailId,
    // required this.chatType,
    required this.customerChatRoomParameter,
  }) {
    Future.delayed(Duration.zero, () async {
      if (customerChatRoomParameter != null) {
        await getOldChatMessages(
          (customerChatRoomParameter?.id ?? -1).toString(),
        );
      }
      if (customerChatRoomParameter == null) {
        await _init();
      } else if (customerChatRoomParameter != null &&
          (customerChatRoomParameter?.isActive ?? false)) {
        await _init();
      }
      if (customerChatRoomParameter != null && messages.isNotEmpty) {
        await markAllMessagesRead(
          (customerChatRoomParameter?.id ?? -1).toString(),
        );
      }
    });
  }

  /// chat room model
  final ChatRoomDetailModel? customerChatRoomParameter;

  /// Receiver ID for the chat
  final int receiverId;

  /// Booking detail ID for the chat
  final int bookingDetailId;

  /// Chat type
  // final ChatType chatType;

  /// Flag to check if provider is closed
  bool __isClosed = false;

  /// Flag to check if loading
  bool _isLoading = true;

  /// Get loading status
  bool get isLoading => _isLoading;

  /// Socket service instance
  final SocketService _socketService = Injector.instance<SocketService>();

  /// Chat message controller
  final TextEditingController chatController = TextEditingController();

  /// List of chat messages
  final List<Message> _messages = [];

  /// Get messages
  List<Message> get messages => _messages;

  /// Connection status
  bool _isConnected = false;

  /// Get connection status
  bool get isConnected => _isConnected;

  /// Current chat room
  ChatRoom? _chatRoom;

  /// Get current chat room
  ChatRoom? get chatRoom => _chatRoom;

  /// User ID
  int? _userId;

  /// Get user ID
  int? get userId => _userId;

  /// Cancel token for get old chat messages
  CancelToken? _getOldChatMessagesCancelToken;

  /// Cancel token for mark all messages read
  CancelToken? _markAllMessagesReadCancelToken;

  /// Refresh Controller
  final EasyRefreshController refreshController = EasyRefreshController();

  /// Scroll Controller
  final ScrollController scrollController = ScrollController();

  /// Pagination Next URL
  String? nextUrl;

  /// Stream subscriptions
  StreamSubscription<Message>? _messageSubscription;
  StreamSubscription<bool>? _connectionSubscription;
  StreamSubscription<ChatRoom?>? _chatRoomSubscription;

  /// Initialize the provider
  Future<void> _init() async {
    if (__isClosed) return;

    try {
      // Get user ID
      _userId = Injector.instance<AppDB>().userModel?.user?.id;

      if (_userId == null) {
        'Cannot initialize chat: No user ID available'.logE;
        _setLoading(false);
        return;
      }

      // Initialize socket service
      await _socketService.initialize();

      // Listen for connection status
      _connectionSubscription = _socketService.connectionStatus.listen((
        status,
      ) {
        if (__isClosed) return;
        _isConnected = status;
        notifyListeners();
        // If connected, get chat connection
        if (status) {
          _getChatConnection();
        }
      });

      // Listen for new messages
      _messageSubscription = _socketService.onMessage.listen((message) {
        if (__isClosed) return;
        if (message.chatRoom == _chatRoom?.id) {
          _addMessage(message);
        } else if (message.chatRoom == null) {
          _chatRoom = null;
          _getChatConnection();
        }
      });

      // Listen for chat room connection
      _chatRoomSubscription = _socketService.onChatRoomConnection.listen((
        room,
      ) {
        if (__isClosed) return;
        _chatRoom = room;
        _setLoading(false);
        notifyListeners();
      });
    } catch (e) {
      'Error initializing chat provider: $e'.logE;
      _setLoading(false);
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    if (__isClosed) return;
    _isLoading = loading;
    notifyListeners();
  }

  /// Get chat connection
  Future<void> _getChatConnection() async {
    if (__isClosed) return;

    try {
      final room = await _socketService.getChatConnection(
        receiverId: receiverId,
        bookingDetailId: bookingDetailId,
      );

      _chatRoom = room;
      _setLoading(false);
    } catch (e) {
      'Error getting chat connection: $e'.logE;
      _setLoading(false);
    }
  }

  /// Add a message to the list
  void _addMessage(Message message) {
    if (__isClosed) return;

    _messages.insert(0, message);
    notifyListeners();
  }

  /// Send a message
  Future<bool> sendMessage() async {
    if (__isClosed || _chatRoom == null) return false;

    final message = chatController.text.trim();
    if (message.isEmpty) return false;

    try {
      final success = await _socketService.sendMessage(
        chatRoomId: _chatRoom!.id,
        message: message,
      );

      if (success) {
        '===>>>>>>> Message sent successfully <<<<<<<<<=== $_chatRoom'.logE;
        chatController.clear();
        if (scrollController.hasClients && scrollController.offset != 0) {
          // Give a slight delay so the ListView updates first
          WidgetsBinding.instance.addPostFrameCallback((_) {
            scrollController.animateTo(
              0,
              duration: Durations.short1,
              curve: Curves.linear,
            );
          });
        }
      }

      return success;
    } catch (e) {
      'Error sending message: $e'.logE;
      return false;
    }
  }

  Future<void> getOldChatMessages(String id) async {
    if (__isClosed) return;
    try {
      _getOldChatMessagesCancelToken?.cancel();
      _getOldChatMessagesCancelToken = CancelToken();
      final request = ApiRequest(
        path: nextUrl ?? EndPoints.getOldChatMessages(id),
        cancelToken: _getOldChatMessagesCancelToken,
      );
      if (nextUrl.isEmptyOrNull) {
        _setLoading(true);
        notify();
      }
      final response = await Injector.instance<TripRepository>()
          .getOldChatMessages(request);
      if (__isClosed) return;
      if (nextUrl.isEmptyOrNull) {
        _setLoading(false);
        notify();
      }
      response.when(
        success: (data) {
          if (__isClosed ||
              (_getOldChatMessagesCancelToken?.isCancelled ?? true)) {
            return;
          }
          if (nextUrl.isEmptyOrNull) messages.clear();
          nextUrl = data.next;
          messages.addAll(data.results);
          notify();
        },
        error: (error) {
          if (__isClosed ||
              (_getOldChatMessagesCancelToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      'Error getting old chat messages: $e'.logE;
    }
  }

  /// mark all messages as read
  Future<void> markAllMessagesRead(String chatRoomID) async {
    if (__isClosed) return;
    try {
      _markAllMessagesReadCancelToken?.cancel();
      _markAllMessagesReadCancelToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.markAllChatMessagesReaded(chatRoomID),
        cancelToken: _markAllMessagesReadCancelToken,
      );
      final response = await Injector.instance<TripRepository>()
          .markAllChatMessagesAsReaded(request);
      response.when(
        success: (data) {
          if (__isClosed ||
              (_markAllMessagesReadCancelToken?.isCancelled ?? true)) {
            return;
          }
        },
        error: (error) {
          if (__isClosed ||
              (_markAllMessagesReadCancelToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      'Error marking all messages read: $e'.logE;
    }
  }

  /// Grouped messages by day
  Map<String, List<Message>> get groupedMessages {
    final grouped = <String, List<Message>>{};

    for (final msg in _messages) {
      final date = msg.createdAt.separatorDate;

      if (grouped.containsKey(date)) {
        grouped[date]!.add(msg);
      } else {
        grouped[date] = [msg];
      }
    }

    return grouped;
  }

  /// Notify listeners if not closed
  void notify() {
    if (__isClosed) return;
    notifyListeners();
  }

  @override
  void dispose() {
    __isClosed = true;
    _markAllMessagesReadCancelToken?.cancel();
    _getOldChatMessagesCancelToken?.cancel();

    // Cancel subscriptions
    _messageSubscription?.cancel();
    _connectionSubscription?.cancel();
    _chatRoomSubscription?.cancel();

    // Mark chat room as inactive
    if (_chatRoom != null) {
      _socketService
        ..markChatRoomInactive(_chatRoom!.id)
        ..disconnect();
    }

    // Dispose controllers
    chatController.dispose();
    scrollController.dispose();
    refreshController.dispose();
    _messages.clear();
    nextUrl = null;
    super.dispose();
  }
}
