class ChatMessagesModel {
  ChatMessagesModel({
    required this.count,
    required this.next,
    required this.previous,
    required this.results,
  });

  factory ChatMessagesModel.fromJson(Map<String, dynamic> json) {
    return ChatMessagesModel(
      count: json['count'] as int,
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: List<Message>.from(
        (json['results'] as List)
            .map((x) => Message.fromJson(x as Map<String, dynamic>)),
      ),
    );
  }
  int count;
  String? next;
  String? previous;
  List<Message> results;

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'next': next,
      'previous': previous,
      'results': results.map((x) => x.toJson()).toList(),
    };
  }
}

class Message {
  Message({
    required this.id,
    required this.chatRoom,
    required this.sender,
    required this.message,
    required this.messageType,
    required this.fileUrl,
    required this.createdAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as int?,
      chatRoom: json['chat_room'] as int?,
      sender: json['sender'] == null
          ? null
          : SenderModel.fromJson(json['sender'] as Map<String, dynamic>),
      message: json['message'] as String?,
      messageType: json['message_type'] as String?,
      fileUrl: json['file_url'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'].toString())
          : DateTime.now(),
    );
  }
  int? id;
  int? chatRoom;
  SenderModel? sender;
  String? message;
  String? messageType;
  String? fileUrl;
  DateTime createdAt;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chat_room': chatRoom,
      'sender': sender?.toJson(),
      'message': message,
      'message_type': messageType,
      'file_url': fileUrl,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class SenderModel {
  SenderModel({
    required this.id,
    required this.firstName,
  });

  factory SenderModel.fromJson(Map<String, dynamic> json) {
    return SenderModel(
      id: json['id'] as int?,
      firstName: json['first_name'] as String?,
    );
  }
  int? id;
  String? firstName;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
    };
  }
}
