/// Parameter model for rate screen.
class RateParams {
  /// Constructor
  const RateParams({
    required this.tripId,
    required this.providerId,
    required this.bookingDetail,
    required this.onRatingDone,
  });

  /// Trip ID
  final String tripId;

  /// Provider ID
  final String providerId;

  /// Booking detail ID
  final String bookingDetail;

  /// called after ratings is done
  final Function(int rating, String? description) onRatingDone;
}
