import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/rate_page/models/rate_page_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/rate_page/provider/rate_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Rate Provider Page ui
class RatingScreen extends StatelessWidget {
  /// Constructor
  const RatingScreen({super.key, required this.rateParams});
  final RateParams rateParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => RateProvider(),
      child: Consumer<RateProvider>(
        builder: (context, rateProvider, _) {
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(title: context.l10n.rateProvider),
            body: ValueListenableBuilder(
              valueListenable: rateProvider.isShowLoader,
              builder: (context, isShowLoader, _) {
                return AppLoader(
                  isShowLoader: isShowLoader,
                  child: ListView(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSize.appPadding,
                    ),
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: AppSize.h8),
                        child: Text(
                          context.l10n.chooseWhatUFeel,
                          style: TextStyle(
                            fontSize: AppSize.h14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.ff2B2829,
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children:
                            [
                                  (
                                    1,
                                    AppAssets.iconsVaryUnhappyColor,
                                    AppAssets.iconsVaryUnhappy,
                                  ),
                                  (
                                    2,
                                    AppAssets.iconsUnhappyColor,
                                    AppAssets.iconsUnhappy,
                                  ),
                                  (
                                    3,
                                    AppAssets.iconsNeutralColor,
                                    AppAssets.iconsNeutral,
                                  ),
                                  (
                                    4,
                                    AppAssets.iconsHappyColor,
                                    AppAssets.iconsHappy,
                                  ),
                                  (
                                    5,
                                    AppAssets.iconsVaryHappyColor,
                                    AppAssets.iconsVaryHappy,
                                  ),
                                ]
                                .map(
                                  (e) => GestureDetector(
                                    onTap: () =>
                                        rateProvider.ratingUpdate(e.$1),
                                    child: rateProvider.rating == e.$1
                                        ? e.$2.image(
                                            height: AppSize.h48,
                                            width: AppSize.w48,
                                          )
                                        : e.$3.image(
                                            height: AppSize.h40,
                                            width: AppSize.w40,
                                          ),
                                  ),
                                )
                                .toList(),
                      ),
                      Gap(AppSize.h16),
                      AppTextFormField(
                        keyboardType: TextInputType.multiline,
                        controller: rateProvider.descriptionController,
                        maxLine: 6,
                        title: context.l10n.writeSuggestion,
                        contentHeight: AppSize.r10,
                        hintText: context.l10n.writeUrSuggestion,
                        contentWidth: AppSize.r16,
                      ),
                    ],
                  ),
                );
              },
            ),
            bottomNavigationBar: AppButton(
              text: context.l10n.submitReview,
              onPressed: () {
                rateProvider.rateProvider(
                  context,
                  tripId: rateParams.tripId,
                  providerId: rateParams.providerId,
                  bookingDetail: rateParams.bookingDetail,
                  onRateDone: rateParams.onRatingDone,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
