import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

/// Rate Module Data Handling
class RateProvider extends ChangeNotifier {
  bool _isClosed = false;

  /// for display rating
  int rating = 3;
  final isShowLoader = ValueNotifier<bool>(false);
  final descriptionController = TextEditingController();
  CancelToken? rateProviderCancelToken;

  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  ///rating update
  void ratingUpdate(int index) {
    rating = index;
    notify();
  }

  Future<void> rateProvider(
    BuildContext context, {
    required String tripId,
    required String providerId,
    required String bookingDetail,
    required Function(int rating, String? description) onRateDone,
  }) async {
    try {
      if (_isClosed) return;
      isShowLoader.value = true;
      rateProviderCancelToken?.cancel();
      rateProviderCancelToken = CancelToken();
      final data = {
        ApiKeys.rating: rating,
        ApiKeys.trip: tripId,
        ApiKeys.provider: providerId,
        ApiKeys.bookingDetail: bookingDetail,
        ApiKeys.suggestion: descriptionController.text,
      };
      final response = await Injector.instance<TripRepository>().rateProvider(
        ApiRequest(
          path: EndPoints.rateProvider,
          cancelToken: rateProviderCancelToken,
          data: data,
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed || (rateProviderCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          AppNavigationService.pop(context);
          onRateDone(rating, descriptionController.text);
          context.l10n.thanksUForFeedback.showSuccessAlert();
        },
        error: (error) {
          if (_isClosed || (rateProviderCancelToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (rateProviderCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'rateProvider error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    rateProviderCancelToken?.cancel();
    descriptionController.dispose();
    isShowLoader.dispose();
    super.dispose();
  }
}
