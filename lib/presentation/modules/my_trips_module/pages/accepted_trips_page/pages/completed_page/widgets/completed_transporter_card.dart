import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';
import 'package:transport_match/widgets/title_info.dart';

class CompletedTransporterCard extends StatelessWidget {
  const CompletedTransporterCard({
    super.key,
    this.onPayPressed,
    required this.data,
  });

  final VoidCallback? onPayPressed;
  final TripModel data;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: AppSize.h16,
          children: [
            /// Header row with "Your Shipment" and "Edit"
            NoOfVehicleWidget(
              noOfVehicle: data.noOfCars?.toString() ?? '-',
              isTitleWidget: true,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: AppSize.w4,
              children: [
                AppAssets.iconsLocationOrigin.image(
                  height: AppSize.h14,
                  width: AppSize.h14,
                ),
                const Expanded(
                  child: Divider(
                    color: AppColors.unSelectedColor,
                    thickness: 2,
                  ),
                ),
                AppAssets.iconsLocation.image(
                  height: AppSize.h14,
                  width: AppSize.h14,
                ),
              ],
            ),
            TitleInfo(
              title: context.l10n.transporter,
              subTitle: data.bookingDetails?.length == 1 ?data.bookingDetails?.first.tripData?.companyName??'-' : (data.bookingDetails??[]).isEmpty?'-': '${data.bookingDetails?.length} ${context.l10n.transporter}',
              subTitleFontWeight: FontWeight.w500,
            ),
            LocationInfoWidget(
              /// user start and end location
              startLatitude:
                  data.startStopLocation?.address?.latitude ??
                  data.userStartLocation?.latitude,
              startLongitude:
                  data.startStopLocation?.address?.longitude ??
                  data.userStartLocation?.longitude,
              endLatitude:
                  data.endStopLocation?.address?.latitude ??
                  data.userEndLocation?.latitude,
              endLongitude:
                  data.endStopLocation?.address?.longitude ??
                  data.userEndLocation?.longitude,

              /// date and place
              startLocationTitle:
                  data.startStopLocation?.name ??
                  data.userStartLocation?.street ??
                  '',
              startLocationDate: data.customerStartDate?.monthDateFormate ?? '',
              endLocationTitle:
                  data.endStopLocation?.name ??
                  data.userEndLocation?.street ??
                  '',
              endLocationDate: data.customerEndDate?.monthDateFormate ?? '',
            ),
          ],
        ),
      ),
    );
  }
}
