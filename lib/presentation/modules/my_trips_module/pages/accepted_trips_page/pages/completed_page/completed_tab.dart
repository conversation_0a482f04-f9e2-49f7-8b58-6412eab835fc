import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/widgets/completed_transporter_card.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/provider/accepted_trip_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';

/// Completed Tab ui
class CompletedTab extends StatefulWidget {
  /// Constructor
  const CompletedTab({super.key, required this.acceptedTripProvider});
  final AcceptedTripProvider acceptedTripProvider;

  @override
  State<CompletedTab> createState() => _CompletedTabState();
}

class _CompletedTabState extends State<CompletedTab> {
  final refreshController = EasyRefreshController();
  @override
  void initState() {
    widget.acceptedTripProvider.getAcceptedTripList(2, isWantShowLoader: true);
    super.initState();
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.acceptedTripProvider.completedTripList,
      builder: (context, list, child) {
        return ValueListenableBuilder(
          valueListenable: widget.acceptedTripProvider.nextCompleteUrl,
          builder: (context, nextUrl, nextUrlChild) {
            return ValueListenableBuilder(
              valueListenable: widget.acceptedTripProvider.completedTripLoader,
              builder: (context, isShowLoader, child) {
                return AppLoader(
                  isShowLoader: isShowLoader,
                  child: EasyRefresh(
                    triggerAxis: Axis.vertical,
                    header: AppCommonFunctions.getLoadingHeader(),
                    footer: AppCommonFunctions.getLoadingFooter(),
                    controller: refreshController,
                    onRefresh: () =>
                        widget.acceptedTripProvider.getAcceptedTripList(2),
                    onLoad: () => nextUrl.isNotEmptyAndNotNull
                        ? widget.acceptedTripProvider.getAcceptedTripList(
                            2,
                            isPagination: true,
                          )
                        : null,
                    child: !isShowLoader && list.isEmpty
                        ? const NoTripWidget()
                        : nextUrlChild!,
                  ),
                );
              },
            );
          },
          child: SingleChildScrollView(
            child: Column(
              children: List.generate(list.length, (index) {
                final data = list[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: AppSize.h16),
                  child: GestureDetector(
                    onTap: () => AppNavigationService.pushNamed(
                      context,
                      AppRoutes.tripsTripDetailsScreen,
                      extra: TripDetailsParams(
                        isCompleted: true,
                        id: data.id?.toString() ?? '',
                      ),
                      // TripDetailsScreen(
                      //   isCompleted: true,
                      //   id: data.id?.toString() ?? '',
                      // ),
                    ),
                    child: CompletedTransporterCard(data: data),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}
