import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Payment summary row
class PaymentSummaryRow extends StatelessWidget {
  /// Constructor
  const PaymentSummaryRow({
    required this.title,
    required this.amount,
    super.key,
  });

  /// Title
  final String title;

  /// Amount
  final String amount;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: AppSize.w10,
        children: [
          Flexible(
            child: Text(
              title,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: AppSize.sp14,
                color: AppColors.black,
              ),
            ),
          ),
          Text(
            amount,
            maxLines: 1,
            style: context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.ff495057,
            ),
          ),
        ],
      ),
    );
  }
}

/// Payment total section
class TotalRow extends StatelessWidget {
  /// Constructor
  const TotalRow({
    super.key,
    required this.firstTitle,
    required this.lastTitle,
    this.padding,
    this.isLast = false,
  });

  /// Payment FirstTitle
  final String firstTitle;

  /// Payment FirstTitle
  final String lastTitle;

  /// Payment LastTitle FontWeight
  final double? padding;

  final bool isLast;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.h16,
        vertical: padding ?? AppSize.h8,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r4),
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            firstTitle,
            style: isLast
                ? context.textTheme.titleLarge?.copyWith(
                    color: AppColors.ff212529,
                  )
                : context.textTheme.titleLarge?.copyWith(
                    fontSize: AppSize.sp18,
                    color: AppColors.ff212529,
                    fontWeight: FontWeight.w500,
                  ),
          ),
          Flexible(
            child: MarqueeWidget(
              child: Text(
                lastTitle,
                maxLines: 1,
                style: isLast
                    ? context.textTheme.titleLarge
                    : context.textTheme.titleLarge?.copyWith(
                        fontSize: AppSize.sp18,
                        color: AppColors.black,
                        fontWeight: FontWeight.w500,
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
