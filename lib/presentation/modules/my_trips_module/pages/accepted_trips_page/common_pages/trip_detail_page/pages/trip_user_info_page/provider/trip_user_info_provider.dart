import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/trip_user_info_page/models/trip_user_info_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class TripUserInfoProvider extends ChangeNotifier {
  TripUserInfoProvider(TripUserInfoParams tripUserInfoParams) {
    dropUserName.text = tripUserInfoParams.dropUserName ?? '';
    dropUserImage = tripUserInfoParams.dropUserImage ?? '';
    dropUserDocType = tripUserInfoParams.dropUserDocType ?? '';
    pickUserName.text = tripUserInfoParams.pickUserName ?? '';
    pickUserImage = tripUserInfoParams.pickUserImage ?? '';
    pickUserDocType = tripUserInfoParams.pickUserDocType ?? '';
    tripId = tripUserInfoParams.tripId ?? '';
  }

  String tripId = '';
  final dropUserName = TextEditingController();
  String dropUserImage = '';
  String dropUserDocType = '';
  final pickUserName = TextEditingController();
  String pickUserImage = '';
  String pickUserDocType = '';

  bool _isClosed = false;
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  /// for register temporary slot
  final imageLoading = ValueNotifier<bool>(false);
  final isAssigneeShowLoader = ValueNotifier<bool>(false);
  CancelToken? generateImgToken;
  CancelToken? createBookToken;
  Future<void> createBookingAssignee(
    BuildContext context,
    Function(List<AssigneeModel>? assigneeModel)? onSuccess,
  ) async {
    try {
      if (_isClosed) return;
      if (dropUserName.text.isEmpty) {
        context.l10n.pleaseEnterDropUserName.showErrorAlert();
      } else if (dropUserDocType.isEmpty) {
        context.l10n.pleaseEnterDropUserDocType.showErrorAlert();
      } else if (dropUserImage.isEmpty) {
        context.l10n.pleaseEnterDropUserDoc.showErrorAlert();
      } else if (pickUserName.text.isEmpty) {
        context.l10n.pleaseEnterPickUserName.showErrorAlert();
      } else if (pickUserDocType.isEmpty) {
        context.l10n.pleaseEnterPickUserDocType.showErrorAlert();
      } else if (pickUserImage.isEmpty) {
        context.l10n.pleaseEnterPickUserDoc.showErrorAlert();
      } else {
        createBookToken?.cancel();
        createBookToken = CancelToken();
        var dataList = <Map<String, dynamic>>[];
        if (_isClosed) return;

        isAssigneeShowLoader.value = true;
        generateImgToken?.cancel();
        generateImgToken = CancelToken();
        await AppCommonFunctions.generateEmptyListImages(
          generateImgToken: generateImgToken,
          isClosed: _isClosed,
          whenLoaderChange: (loader) => imageLoading.value = loader,
          imgList: [File(dropUserImage), File(pickUserImage)],
        )?.then((value) async {
          if (value.length == 2) {
            dataList = [
              {
                ApiKeys.name: dropUserName.text,
                ApiKeys.idProofType: dropUserDocType,
                ApiKeys.awsImageKey: value.first,
                ApiKeys.assigneeType: AssigneeType.DROP.name,
              },
              {
                ApiKeys.name: pickUserName.text,
                ApiKeys.idProofType: pickUserDocType,
                ApiKeys.awsImageKey: value.last,
                ApiKeys.assigneeType: AssigneeType.PICKUP.name,
              },
            ];
            final response =
                await Injector.instance<HomeRepository>().createBookingAssign(
              ApiRequest(
                path: EndPoints.createBookingAssign,
                data: {
                  ApiKeys.booking: tripId,
                  ApiKeys.assignees: dataList,
                },
                cancelToken: createBookToken,
              ),
            );
            response.when(
              success: (data) {
                if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
                if (data.isNotEmpty) {
                  onSuccess?.call(data.map(AssigneeModel.fromJson).toList());
                }
                AppNavigationService.pop(context);
                notify();
              },
              error: (error) {
                if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
                error.message.showErrorAlert();
              },
            );
          } else {
            if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
            if (context.mounted) {
              context.l10n.enableToUploadImages.showErrorAlert();
            }
          }
        });
        isAssigneeShowLoader.value = false;
      }
    } catch (e) {
      if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
      '==>>> create user assign api $e'.logFatal;
    }
  }

  // Future<bool> _uploadFile({
  //   required String url,
  //   required Uint8List byteImages,
  //   required String mimeType,
  //   void Function(double)? onSendProgress,
  //   CancelToken? cancelToken,
  // }) {
  //   final baseOptions = BaseOptions(
  //     connectTimeout: const Duration(minutes: 10),
  //     sendTimeout: const Duration(minutes: 10),
  //     receiveTimeout: const Duration(minutes: 10),
  //     // contentType: mimeType,
  //     headers: {
  //       ApiKeys.contentType: '',
  //     },
  //   );

  //   final dio = Dio(baseOptions);

  //   return dio.put<Map<String, dynamic>>(
  //     url,
  //     data: byteImages,
  //     cancelToken: cancelToken,
  //     onSendProgress: (count, total) {
  //       final progressPercent = count / total;
  //       onSendProgress?.call(progressPercent);
  //     },
  //   ).then(
  //     (value) {
  //       return true;
  //     },
  //     onError: (Object error) {
  //       if (error is DioException) {
  //         if (error.type == DioExceptionType.cancel) {
  //           return false;
  //         }
  //       }
  //       return false;
  //     },
  //   ).onError((error, stackTrace) {
  //     return false;
  //   });
  // }

  // /// to upload image we need to first generate empty list of image link then we have
  // /// put image data into that generated link
  // Future<List<String>>? _generateEmptyListImages({
  //   required List<File> imgList,
  // }) async {
  //   try {
  //     if (_isClosed) return [];
  //     generateImgToken?.cancel();
  //     generateImgToken = CancelToken();
  //     imageLoading.value = true;
  //     final imagesList = <String>[];
  //     final completer = Completer<List<String>>();

  //     final data = FormData();
  //     data.fields.addAll([
  //       const MapEntry(ApiKeys.fileExtension, 'jpg'),
  //       MapEntry(ApiKeys.folderName, ImageTypes.BOOKING_ASSIGNEE_DOCUMENT.name),
  //       MapEntry(ApiKeys.numberOfUrl, imgList.length.toString()),
  //     ]);
  //     final request = ApiRequest(
  //       path: EndPoints.generateUrl,
  //       data: data,
  //       cancelToken: generateImgToken,
  //     );

  //     final res =
  //         await Injector.instance<AccountRepository>().generateUrl(request);

  //     res.when(
  //       success: (data) async {
  //         if (_isClosed || (generateImgToken?.isCancelled ?? true)) return [];

  //         // Convert data['keys'] to List<String> and add to imagesList
  //         imagesList.addAll(
  //           data.map((e) => e[ApiKeys.putUrl].toString()),
  //         );
  //         final uploadFutures = <Future<void>>[];
  //         if (imagesList.length == imgList.length) {
  //           // Upload images

  //           for (var i = 0; i < imgList.length; i++) {
  //             uploadFutures.add(
  //               _uploadFile(
  //                 byteImages: imgList[i].readAsBytesSync(),
  //                 url: data[i][ApiKeys.putUrl].toString(),
  //                 mimeType: 'image/jpeg',
  //               ),
  //             );
  //           }
  //         } else {
  //           return [];
  //         }
  //         // Wait for all uploads to complete
  //         if (_isClosed || (generateImgToken?.isCancelled ?? true)) return [];
  //         await Future.wait(uploadFutures);
  //         completer.complete(
  //           data.map((e) => e[ApiKeys.keyName].toString()).toList(),
  //         );
  //       },
  //       error: (exception) {
  //         if (generateImgToken?.isCancelled ?? true) return [];
  //       },
  //     );
  //     imageLoading.value = false;
  //     return completer.future;
  //   } catch (e) {
  //     if (_isClosed || (generateImgToken?.isCancelled ?? true)) return [];
  //     imageLoading.value = false;
  //     '===>>>> here from catch part '.logE;
  //     return [];
  //   }
  // }

  @override
  void dispose() {
    createBookToken?.cancel();
    generateImgToken?.cancel();
    _isClosed = true;
    super.dispose();
  }
}
