import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart' show AppSize;
import 'package:transport_match/utils/gen/assets.gen.dart';

class ChatIconWidget extends StatelessWidget {
  const ChatIconWidget({
    super.key,
    required this.isComplete,
    required this.isExclusive,
    required this.isOngoing,
    required this.tripData,
    required this.data,
    required this.addDriverChatRoomData,
  });
  final bool isComplete;
  final bool isExclusive;
  final bool isOngoing;
  final TripModel? tripData;
  final BookingDetail? data;
  final void Function(int chatRoomId, bool isActive, int index, bool isCount)?
      addDriverChatRoomData;

  @override
  Widget build(BuildContext context) {
    var count = data?.driverChatRoomDetail?.unreadMessageCount ?? 0;
    if (!isExclusive) {
      count += (data?.sharedBookings?.firstOrNull?.endLocationChatRoomDetail
                  ?.unreadMessageCount ??
              0) +
          (data?.sharedBookings?.firstOrNull?.startLocationChatRoomDetail
                  ?.unreadMessageCount ??
              0);
    }
    return isComplete
        ? const SizedBox.shrink()
        : !isExclusive
            ? PopupMenuButton(
                menuPadding: EdgeInsets.zero,
                icon: Badge.count(
                  count: count,
                  isLabelVisible: count > 0,
                  textColor: AppColors.white,
                  child: AppAssets.iconsChat.image(
                    height: AppSize.h24,
                    width: AppSize.h24,
                  ),
                ),
                position: PopupMenuPosition.under,
                itemBuilder: (context) {
                  return List.generate(
                    isOngoing ? 3 : 2,
                    (index) {
                      final trip = tripData;
                      final count = switch (index) {
                        0 => data
                                ?.sharedBookings
                                ?.firstOrNull
                                ?.startLocationChatRoomDetail
                                ?.unreadMessageCount ??
                            0,
                        1 => data
                                ?.sharedBookings
                                ?.firstOrNull
                                ?.endLocationChatRoomDetail
                                ?.unreadMessageCount ??
                            0,
                        _ =>
                          data?.driverChatRoomDetail?.unreadMessageCount ?? 0,
                      };
                      return PopupMenuItem(
                        padding: EdgeInsets.symmetric(horizontal: AppSize.w8),
                        onTap: () {
                          addDriverChatRoomData?.call(
                            0,
                            true,
                            index,
                            true,
                          );
                          AppNavigationService.pushNamed(
                            context,
                            AppRoutes.tripsChatScreen,
                            extra: ChatParams(
                              title: switch (index) {
                                0 => trip?.startStopLocation?.name ?? '',
                                1 => trip?.endStopLocation?.name ?? '',
                                _ => data?.tripData?.companyName ?? '',
                              },
                              receiverId: switch (index) {
                                0 => trip?.startStopLocation?.adminUserId ?? 0,
                                1 => trip?.endStopLocation?.adminUserId ?? 0,
                                _ => data?.tripData?.driverUserId ?? 0,
                              },
                              bookingDetailId: data?.id ?? 0,
                              customerChatRoomParameter: switch (index) {
                                0 => data?.sharedBookings?.firstOrNull
                                    ?.startLocationChatRoomDetail,
                                1 => data?.sharedBookings?.firstOrNull
                                    ?.endLocationChatRoomDetail,
                                _ => data?.driverChatRoomDetail,
                              },
                              addDriverChatRoomData: (
                                chatRoomId,
                                isActive,
                              ) =>
                                  addDriverChatRoomData?.call(
                                chatRoomId,
                                isActive,
                                index,
                                false,
                              ),
                            ),
                            // ChatScreen(
                            //   title: switch (index) {
                            //     0 => trip?.startStopLocation?.name ?? '',
                            //     1 => trip?.endStopLocation?.name ?? '',
                            //     _ => data?.tripData?.companyName ?? '',
                            //   },
                            //   receiverId: switch (index) {
                            //     0 => trip?.startStopLocation?.adminUserId ?? 0,
                            //     1 => trip?.endStopLocation?.adminUserId ?? 0,
                            //     _ => data?.tripData?.driverUserId ?? 0,
                            //   },
                            //   bookingDetailId: data?.id ?? 0,
                            //   customerChatRoomParameter: switch (index) {
                            //     0 => data?.sharedBookings?.firstOrNull
                            //         ?.startLocationChatRoomDetail,
                            //     1 => data?.sharedBookings?.firstOrNull
                            //         ?.endLocationChatRoomDetail,
                            //     _ => data?.driverChatRoomDetail,
                            //   },
                            //   addDriverChatRoomData: (
                            //     chatRoomId,
                            //     isActive,
                            //   ) =>
                            //       addDriverChatRoomData?.call(
                            //     chatRoomId,
                            //     isActive,
                            //     index,
                            //     false,
                            //   ),
                            // ),
                          );
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          spacing: AppSize.w5,
                          children: [
                            Text(
                              switch (index) {
                                0 => context.l10n.originStockAdmin,
                                1 => context.l10n.dropStockAdmin,
                                _ => context.l10n.transporter,
                              },
                            ),
                            if (count > 0)
                              CircleAvatar(
                                radius: AppSize.r8,
                                backgroundColor: AppColors.errorColor,
                                child: Text(
                                  count.toString(),
                                  style: context.textTheme.bodySmall?.copyWith(
                                    color: AppColors.white,
                                    fontSize: AppSize.sp10,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ).toList();
                },
              )
            : isOngoing
                ? Padding(
                    padding: EdgeInsets.only(left: AppSize.w8),
                    child: Badge.count(
                      count:
                          data?.driverChatRoomDetail?.unreadMessageCount ?? 0,
                      isLabelVisible:
                          (data?.driverChatRoomDetail?.unreadMessageCount ??
                                  0) >
                              0,
                      textColor: AppColors.white,
                      child: GestureDetector(
                        onTap: () {
                          addDriverChatRoomData?.call(
                            0,
                            true,
                            2,
                            true,
                          );
                          AppNavigationService.pushNamed(
                            context, AppRoutes.tripsChatScreen,
                            extra: ChatParams(
                              title: data?.tripData?.companyName ?? '',
                              receiverId: data?.tripData?.driverUserId ?? 0,
                              bookingDetailId: data?.id ?? 0,
                              customerChatRoomParameter:
                                  data?.driverChatRoomDetail,
                              addDriverChatRoomData: (chatRoomId, isActive) =>
                                  addDriverChatRoomData?.call(
                                chatRoomId,
                                isActive,
                                2,
                                false,
                              ),
                            ),

                            // ChatScreen(
                            //   title: data?.tripData?.companyName ?? '',
                            //   receiverId: data?.tripData?.driverUserId ?? 0,
                            //   bookingDetailId: data?.id ?? 0,
                            //   customerChatRoomParameter:
                            //       data?.driverChatRoomDetail,
                            //   addDriverChatRoomData: (chatRoomId, isActive) =>
                            //       addDriverChatRoomData?.call(
                            //     chatRoomId,
                            //     isActive,
                            //     2,
                            //     false,
                            //   ),
                            // ),
                          );
                        },
                        child: AppAssets.iconsChat.image(
                          height: AppSize.h24,
                          width: AppSize.h24,
                        ),
                      ),
                    ),
                  )
                : const SizedBox.shrink();
  }
}
