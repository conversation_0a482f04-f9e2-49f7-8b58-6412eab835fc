import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';

/// Parameter model for trip user info screen.
class TripUserInfoParams {
  /// Constructor
  const TripUserInfoParams({
    required this.isEdit,
    this.dropUserName,
    this.dropUserImage,
    this.dropUserDocType,
    this.pickUserName,
    this.pickUserImage,
    this.pickUserDocType,
    this.tripId,
    this.onSuccess,
  });

  /// Flag to indicate if in edit mode
  final bool isEdit;
  final String? dropUserName;
  final String? dropUserImage;
  final String? dropUserDocType;
  final String? pickUserName;
  final String? pickUserImage;
  final String? pickUserDocType;
  final String? tripId;
  final Function(List<AssigneeModel>? assigneeModel)? onSuccess;
}
