import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/num_extension.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/provider/trip_detail_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/widgets/payment_summary_row.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class TripPaymentSummary extends StatelessWidget {
  const TripPaymentSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TripDetailProvider>(
      builder: (context, tripDetailProvider, child) {
        final data = tripDetailProvider.paymentSummaryData;
        return tripDetailProvider.isPaymentSummaryLoad
            ? const CircularProgressIndicator.adaptive()
            : data != null
            ? Column(
                spacing: AppSize.h16,
                children: [
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.all(
                        Radius.circular(AppSize.r4),
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(AppSize.sp16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.l10n.paymentSummary,
                            style: context.textTheme.titleLarge,
                          ),
                          Gap(AppSize.h8),
                          if (data.totalTransportationCharge.isNotNullNotZero)
                            PaymentSummaryRow(
                              title: context.l10n.transportationCost,
                              amount: '${data.totalTransportationCharge}'
                                  .smartFormat(),
                            ),
                          if (data
                              .totalEndLocationStorageCharge
                              .isNotNullNotZero)
                            PaymentSummaryRow(
                              title: context.l10n.dropOffStorageFee,
                              amount: '${data.totalEndLocationStorageCharge}'
                                  .smartFormat(),
                            ),
                          if (data
                              .totalStartLocationStorageCharge
                              .isNotNullNotZero)
                            PaymentSummaryRow(
                              title: context.l10n.pickupStorageFee,
                              amount: '${data.totalStartLocationStorageCharge}'
                                  .smartFormat(),
                            ),
                          if (data
                              .totalCustomerLocationToStartStopLocationServiceCharge
                              .isNotNullNotZero)
                            PaymentSummaryRow(
                              title: context.l10n.dropTransportation,
                              amount:
                                  '${data.totalCustomerLocationToStartStopLocationServiceCharge}'
                                      .smartFormat(),
                            ),
                          if (data.totalInsuranceCharge.isNotNullNotZero)
                            PaymentSummaryRow(
                              title: context.l10n.totalInsuranceCost,
                              amount: '${data.totalInsuranceCharge}'.smartFormat(),
                            ),
                          if (data.totalAppFee.isNotNullNotZero)
                            PaymentSummaryRow(
                              title: context.l10n.serviceFee,
                              amount: '${data.totalAppFee}'.smartFormat(),
                            ),
                        ],
                      ),
                    ),
                  ),
                  TotalRow(
                    firstTitle: context.l10n.paidAmount,
                    lastTitle: '${data.paidAmount}'.smartFormat(),
                  ),
                  TotalRow(
                    firstTitle: context.l10n.remainAmount,
                    lastTitle: '${data.remainingBookingAmount}'.smartFormat(),
                  ),
                  TotalRow(
                    firstTitle: context.l10n.total,
                    lastTitle: '${data.totalBookingCost}'.smartFormat(),
                    padding: AppSize.h16,
                    isLast: true,
                  ),
                ],
              )
            : const SizedBox();
      },
    );
  }
}
