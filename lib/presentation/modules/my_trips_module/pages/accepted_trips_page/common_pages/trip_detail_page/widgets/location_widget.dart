import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class TripLocationWidget extends StatelessWidget {
  const TripLocationWidget({
    super.key,
    required this.title,
    required this.value,
  });
  final String title;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(AppSize.r4),
        ),
      ),
      margin: EdgeInsets.only(bottom: AppSize.h16),
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w16,
        vertical: AppSize.h10,
      ),
      child: Row(
        children: [
          Flexible(
            child: Text.rich(
              TextSpan(
                text: '$title: ',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: AppColors.ffADB5BD,
                ),
                children: [
                  TextSpan(
                    text: value,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // GestureDetector(
          //   onTap: () => AppNavigationService.pushNamed(
          //     context,
          //     ChatScreen(
          //       title: name ?? '',
          //       receiverId: receiverId ?? 0,
          //       bookingDetailId: bookingDetailId ?? 0,
          //       chatType: chatType ?? ChatType.originStock,
          //       customerChatRoomParameter: customerChatRoomParameter,
          //     ),
          //   ),
          //   child: AppAssets.iconsChat.image(
          //     height: AppSize.h24,
          //     width: AppSize.h24,
          //   ),
          // ),
        ],
      ),
    );
  }
}
