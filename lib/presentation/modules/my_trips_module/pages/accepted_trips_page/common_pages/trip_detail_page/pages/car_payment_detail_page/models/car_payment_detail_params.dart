import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_summary_model.dart';

/// Parameter model for car payment detail screen.
class CarPaymentDetailParams {
  /// Constructor
  const CarPaymentDetailParams({
    required this.bookingDetail,
    required this.paymentData,
  });

  /// Booking detail
  final BookingDetail? bookingDetail;

  /// Payment data
  final TransporterModel? paymentData;
}
