import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';

class OriginDropLocationWidget extends StatelessWidget {
  const OriginDropLocationWidget({super.key, required this.data});
  final TripModel? data;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(AppSize.r4),
        ),
      ),
      padding: EdgeInsets.all(AppSize.r16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSize.h16,
        children: [
          Text(
            context.l10n.stockLocation,
            style: context.textTheme.titleLarge,
          ),
          ...[
            (
              AppAssets.iconsLocationOrigin,
              context.l10n.originStockLocation,
              data?.startStopLocation?.fullAddress,
              data?.startStopDetailLocation?.perDayCharge,
            ),
            (
              AppAssets.iconsLocation,
              context.l10n.dropStockLocation,
              data?.endStopLocation?.fullAddress,
              data?.endStopDetailLocation?.perDayCharge,
            ),
          ].map(
            (e) => Column(
              spacing: AppSize.h4,
              children: [
                Row(
                  spacing: AppSize.w4,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: AppSize.w8),
                      child: e.$1.image(
                        height: AppSize.h16,
                        width: AppSize.h16,
                      ),
                    ),
                    Text(
                      e.$2,
                      style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.black,
                        fontSize: AppSize.sp14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Container(
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    color: AppColors.ffF2EEF8,
                    borderRadius: BorderRadius.all(
                      Radius.circular(AppSize.r10),
                    ),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSize.h16,
                    vertical: AppSize.h10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        e.$3 ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: AppSize.sp14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.black,
                        ),
                      ),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: '\$${e.$4 ?? 0}/',
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: AppColors.ff67509C,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            TextSpan(
                              text: context.l10n.day,
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: AppColors.ff67509C,
                                fontSize: AppSize.sp12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
