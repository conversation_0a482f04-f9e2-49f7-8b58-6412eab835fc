import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/provider/trip_detail_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class NotesWidget extends StatelessWidget {
  const NotesWidget({super.key, required this.onBack});
  final Function() onBack;

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        final tripDetailProvider = context.read<TripDetailProvider>();
        final data = tripDetailProvider.selectedTrip;
        return Selector<TripDetailProvider, List<NoteModel>?>(
          selector: (p0, p1) => data?.bookingDetails?.firstOrNull?.notes,
          builder: (context, noteList, child) {
            if (noteList?.isNotEmpty ?? false) {
              return Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: AppSize.w10,
                  vertical: AppSize.h10,
                ),
                margin: EdgeInsets.only(bottom: AppSize.h16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: AppSize.h5,
                  children: [
                    Text(
                      context.l10n.note,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: noteList?.length ?? 0,
                      itemBuilder: (context, index) {
                        final note = noteList?[index];
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: AppSize.w5,
                          children: [
                            Gap(AppSize.w5),
                            Padding(
                              padding: EdgeInsets.only(top: 6.h),
                              child: CircleAvatar(
                                radius: AppSize.r4,
                                backgroundColor: AppColors.ff495057,
                              ),
                            ),
                            Flexible(
                              fit: FlexFit.tight,
                              child: ReadMoreText(
                                note?.description ?? '',
                                style: context.textTheme.titleMedium?.copyWith(
                                  fontSize: AppSize.sp14,
                                  color: AppColors.black,
                                ),
                                trimMode: TrimMode.Line,
                                trimCollapsedText: context.l10n.readMore,
                                trimExpandedText: ' ${context.l10n.readLess}',
                                lessStyle: context.textTheme.titleMedium
                                    ?.copyWith(
                                      fontSize: AppSize.sp12,
                                      color: AppColors.ff0087C7,
                                    ),
                                moreStyle: context.textTheme.titleMedium
                                    ?.copyWith(
                                      fontSize: AppSize.sp12,
                                      color: AppColors.ff0087C7,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: AppSize.w5),
                              child: GestureDetector(
                                onTap: () => tripDetailProvider.deleteNotes(
                                  note?.id.toString() ?? '',
                                  context,
                                  onSuccess: onBack,
                                ),
                                child: const Icon(Icons.delete),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              );
            } else {
              return const SizedBox();
            }
          },
        );
      },
    );
  }
}
