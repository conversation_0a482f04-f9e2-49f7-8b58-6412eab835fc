import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/trip_user_info_page/models/trip_user_info_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/trip_user_info_page/provider/trip_user_info_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/build_pickup_person_info_widget.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// User info page
class TripUserInfoPage extends StatelessWidget {
  /// User info
  const TripUserInfoPage({super.key, required this.tripUserInfoParams});

  final TripUserInfoParams tripUserInfoParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TripUserInfoProvider(tripUserInfoParams),
      builder: (context, child) {
        final tripUserInfoProvider = Provider.of<TripUserInfoProvider>(context);
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.userInfo),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: ValueListenableBuilder(
            valueListenable: tripUserInfoProvider.isAssigneeShowLoader,
            builder: (context, isShowLoader, _) {
              return tripUserInfoParams.isEdit
                  ? AppButton(
                      text: context.l10n.save,
                      onPressed: isShowLoader
                          ? null
                          : () => tripUserInfoProvider.createBookingAssignee(
                              context,
                              tripUserInfoParams.onSuccess,
                            ),
                    )
                  : const SizedBox.shrink();
            },
          ),
          body: ValueListenableBuilder(
            valueListenable: tripUserInfoProvider.isAssigneeShowLoader,
            builder: (context, isAssigneeShowLoader, child) =>
                AppLoader(isShowLoader: isAssigneeShowLoader, child: child!),
            child: SingleChildScrollView(
              padding: EdgeInsets.all(AppSize.appPadding),
              child: Column(
                spacing: AppSize.h24,
                children: [
                  Selector<TripUserInfoProvider, (String, String)>(
                    selector: (p0, tripUserInfoProvider) => (
                      tripUserInfoProvider.dropUserDocType,
                      tripUserInfoProvider.dropUserImage,
                    ),
                    builder: (context, value, child) {
                      return BuildPickupPersonInfoWidget(
                        isEdit: tripUserInfoParams.isEdit,
                        title: context.l10n.carDropPerson,
                        subtitle: context.l10n.thisIsDropInfo,
                        onImageChange: (value) => tripUserInfoProvider
                          ..dropUserImage = value ?? ''
                          ..notify(),
                        name: tripUserInfoProvider.dropUserName,
                        docType: value.$1,
                        onTypeChange: (value) =>
                            tripUserInfoProvider.dropUserDocType = value ?? '',
                        image: value.$2,
                      );
                    },
                  ),
                  Selector<TripUserInfoProvider, (String, String)>(
                    selector: (p0, tripUserInfoProvider) => (
                      tripUserInfoProvider.pickUserDocType,
                      tripUserInfoProvider.pickUserImage,
                    ),
                    builder: (context, value, child) {
                      return BuildPickupPersonInfoWidget(
                        isEdit: tripUserInfoParams.isEdit,
                        title: context.l10n.carPickupPerson,
                        subtitle: context.l10n.thisIsDeliveryInfo,
                        name: tripUserInfoProvider.pickUserName,
                        docType: value.$1,
                        onTypeChange: (value) =>
                            tripUserInfoProvider.pickUserDocType = value ?? '',
                        image: value.$2,
                        onImageChange: (value) => tripUserInfoProvider
                          ..pickUserImage = value ?? ''
                          ..notify(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
