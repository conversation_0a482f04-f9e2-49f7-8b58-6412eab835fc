// ignore_for_file: constant_identifier_names

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/show_checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/checklist_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class ChecklistProvider extends ChangeNotifier {
  bool _isClosed = false;
  String? fuelValue;
  final tripRepo = Injector.instance<TripRepository>();
  ChecklistType? performedDuring;
  ChecklistModel? checklistModel;
  EasyRefreshController refreshController = EasyRefreshController();
  final commentController = TextEditingController();

  bool isShowLoader = false;
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  final angleList = ['left', 'right', 'front', 'back'];

  CancelToken? getCheckListToken;
  bool isCheckListShowLoader = false;
  List<ChecklistModel> checkList = <ChecklistModel>[];
  String? checkListNextUrl;
  Future<dynamic> getCheckList(
    String id,
    BuildContext context, {
    required String clientName,
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    getCheckListToken?.cancel();
    getCheckListToken = CancelToken();
    try {
      final url = isPagination
          ? checkListNextUrl ?? EndPoints.getCheckList(id)
          : EndPoints.getCheckList(id);
      if (isWantShowLoader) isCheckListShowLoader = true;
      notify();
      final response = await tripRepo.getCheckList(
        ApiRequest(path: url, cancelToken: getCheckListToken),
      );
      final dummyList = [...checkList];
      response.when(
        success: (data) {
          if (_isClosed || (getCheckListToken?.isCancelled ?? true)) return;
          if (!isPagination) dummyList.clear();
          dummyList.addAll(data.results);
          checkList = dummyList;
          checkListNextUrl = data.next;
          notify();
        },
        error: (error) {
          if (_isClosed || (getCheckListToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      if (isWantShowLoader) isCheckListShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (getCheckListToken?.isCancelled ?? true)) return;
      if (isWantShowLoader) isCheckListShowLoader = false;
      notify();
      'getCheckList error: $e'.logE;
    }
  }

  /// check list static variables list
  /// +++>>>> DO NOT MODIFY <<<<+++
  List<Map<String, dynamic>> checkDataList = [];
  void assignCheckListData(
    BuildContext context, {
    ChecklistModel? data,
    required String clientName,
    required ChecklistProvider checkListProvider,
  }) {
    commentController.clear();
    try {
      if (_isClosed) return;
      checkDataList = [
        {
          'title': 'Exterior',
          'list': <CheckListModel>[
            CheckListModel(
              'Main Lights',
              ValueNotifier(false),
              value: data?.mainLights,
            ),
            CheckListModel(
              'Medium Lights',
              ValueNotifier(false),
              value: data?.mediumLights,
            ),
            CheckListModel(
              'Stop Light/Turn Signals',
              ValueNotifier(false),
              value: data?.stopLightOrTurnSignals,
            ),
            CheckListModel(
              'Radio Antenna',
              ValueNotifier(false),
              value: data?.radioAntenna,
            ),
            CheckListModel(
              'Pair of Windshield Wipers',
              ValueNotifier(false),
              value: data?.pairOfWindShieldsWipers,
            ),
            CheckListModel(
              'Right Side Mirror',
              ValueNotifier(false),
              value: data?.rightSideMirror,
            ),
            CheckListModel(
              'Side Windows',
              ValueNotifier(false),
              value: data?.sideWindows,
            ),
            CheckListModel(
              'Windshield',
              ValueNotifier(false),
              value: data?.windshield,
            ),
            CheckListModel(
              'Rear Windows',
              ValueNotifier(false),
              value: data?.rearWindow,
            ),
            CheckListModel(
              '4 Wheels Caps',
              ValueNotifier(false),
              value: data?.fourWheelCaps,
            ),
            CheckListModel(
              'Body Without Dents',
              ValueNotifier(false),
              value: data?.bodyWithoutDents,
            ),
            CheckListModel(
              'Front Bumper',
              ValueNotifier(false),
              value: data?.frontBumper,
            ),
            CheckListModel(
              'Rear Bumper',
              ValueNotifier(false),
              value: data?.rearBumper,
            ),
            CheckListModel(
              'Front License Plate',
              ValueNotifier(false),
              value: data?.frontLicensePlate,
            ),
            CheckListModel(
              'Rear License Plate',
              ValueNotifier(false),
              value: data?.rearLicensePlate,
            ),
          ],
        },
        {
          'title': 'Interior',
          'list': <CheckListModel>[
            CheckListModel(
              'Heating',
              ValueNotifier(false),
              value: data?.heating,
            ),
            CheckListModel('Radio', ValueNotifier(false), value: data?.radio),
            CheckListModel(
              'Speakers',
              ValueNotifier(false),
              value: data?.speakers,
            ),
            CheckListModel(
              'Lighter',
              ValueNotifier(false),
              value: data?.lighter,
            ),
            CheckListModel(
              'Rearview Mirror',
              ValueNotifier(false),
              value: data?.rearviewMirror,
            ),
            CheckListModel(
              'Ashtrays',
              ValueNotifier(false),
              value: data?.ashtrays,
            ),
            CheckListModel(
              'Seat Belt',
              ValueNotifier(false),
              value: data?.seatBelts,
            ),
            CheckListModel(
              'Window Handles',
              ValueNotifier(false),
              value: data?.windowHandles,
            ),
            CheckListModel(
              'Rubber Floors',
              ValueNotifier(false),
              value: data?.rubberFloors,
            ),
            CheckListModel(
              'Seat Covers',
              ValueNotifier(false),
              value: data?.seatCovers,
            ),
            CheckListModel(
              'Door Handles',
              ValueNotifier(false),
              value: data?.doorHandles,
            ),
            CheckListModel(
              'Holders',
              ValueNotifier(false),
              value: data?.holder,
            ),
            CheckListModel('Engine', ValueNotifier(false), value: data?.engine),
            CheckListModel(
              'Floor Mats',
              ValueNotifier(false),
              value: data?.floorMats,
            ),
          ],
        },
        {
          'title': 'Accessories',
          'list': <CheckListModel>[
            CheckListModel('Jack', ValueNotifier(false), value: data?.jack),
            CheckListModel(
              'Wheel Wrench',
              ValueNotifier(false),
              value: data?.wheelWrench,
            ),
            CheckListModel(
              'Tool kit',
              ValueNotifier(false),
              value: data?.toolKit,
            ),
            CheckListModel(
              'Triangle',
              ValueNotifier(false),
              value: data?.triangle,
            ),
            CheckListModel(
              'Spare tier',
              ValueNotifier(false),
              value: data?.spareTire,
            ),
            CheckListModel(
              'Fire Extinguisher',
              ValueNotifier(false),
              value: data?.fireExtinguisher,
            ),
          ],
        },
        {
          'title': 'Damage / Wear',
          'list': <CheckListModel>[
            CheckListModel(
              'Scratched Paint',
              ValueNotifier(false),
              value: data?.scratchedPaint,
            ),
            CheckListModel(
              'Broken Windows',
              ValueNotifier(false),
              value: data?.brokenWindows,
            ),
            CheckListModel('Dents', ValueNotifier(false), value: data?.dents),
            CheckListModel(
              'Suspension',
              ValueNotifier(false),
              value: data?.suspension,
            ),
          ],
        },
      ];

      AppNavigationService.pushNamed(
        context,
        AppRoutes.tripsShowChecklistScreen,
        extra: ShowChecklistParams(
          clientName: clientName,
          checkListProvider: checkListProvider,
          checklistModel: data,
        ),
        // extra:
        // ShowChecklistScreen(
        //   clientName: clientName,
        //   checkListProvider: checkListProvider,
        //   checklistModel: data,
        // ),
      );
    } catch (e) {
      if (_isClosed) return;
      'assignCheckListData error: $e'.logE;
    }
  }

  CancelToken? approvedCheckListToken;
  bool isApproveShowLoader = false;
  Future<dynamic> approvedCheckList(int id, BuildContext context) async {
    approvedCheckListToken?.cancel();
    approvedCheckListToken = CancelToken();
    try {
      isApproveShowLoader = true;
      notify();
      final response = await tripRepo.checklistComments(
        ApiRequest(
          path: EndPoints.checklistComments,
          cancelToken: approvedCheckListToken,
          data: {
            'checklist': id,
            if (commentController.text.isNotEmptyAndNotNull)
              'description': commentController.text,
            'status': ChecklistRequestDataType.ACCEPTED.name,
          },
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed || (approvedCheckListToken?.isCancelled ?? true)) {
            return;
          }
          final index = checkList.indexWhere((element) => element.id == id);
          checkList.removeAt(index);
          final dummyList = [...checkList]..insert(index, data);
          checkList = dummyList;
          notify();

          AppNavigationService.pop(context);
        },
        error: (error) {
          if (_isClosed || (approvedCheckListToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );
      isApproveShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (getCheckListToken?.isCancelled ?? true)) return;
      isApproveShowLoader = false;
      notify();
      'getCheckList error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    getCheckListToken?.cancel();
    refreshController.dispose();

    super.dispose();
  }
}

/// checklist status enum
enum ChecklistType {
  COLLECTING_FROM_CUSTOMER,
  HANDED_OVER_TO_CUSTOMER,
  COLLECTING_FROM_ORIGIN_STOP_ADMIN,
  HANDED_OVER_TO_DESTINATION_STOP_ADMIN,
}

class CheckListModel {
  CheckListModel(this.name, this.isCheck, {this.value});

  final String name;
  final bool? value;
  ValueNotifier<bool> isCheck = ValueNotifier(false);
}
