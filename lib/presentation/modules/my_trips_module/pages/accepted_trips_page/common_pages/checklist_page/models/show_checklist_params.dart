import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/provider/checklist_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/checklist_model.dart';

/// Parameter model for show checklist screen.
class ShowChecklistParams {
  /// Constructor
  const ShowChecklistParams({
    required this.clientName,
    required this.checkListProvider,
    required this.checklistModel,
  });

  /// Client name
  final String clientName;

  /// Checklist model
  final ChecklistModel? checklistModel;

  /// Checklist provider instance
  final ChecklistProvider checkListProvider;
}
