import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/provider/checklist_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/checklist_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class ChecklistWidget extends StatelessWidget {
  const ChecklistWidget({super.key, this.checklistModel});
  final ChecklistModel? checklistModel;

  @override
  Widget build(BuildContext context) {
    return Consumer<ChecklistProvider>(
      builder: (context, checklistProvider, child) {
        final check = checklistProvider.checkDataList;
        return Column(
          children: check.map((e) {
            final list = e['list'] as List<CheckListModel>;
            final mainIndex = check.indexWhere((l) => l['title'] == e['title']);
            return _CheckListWidget(
              list,
              e['title'] as String,
              (index) {
                (checklistProvider.checkDataList[mainIndex]['list'][index]
                            as CheckListModel)
                        .isCheck
                        .value =
                    !list[index].isCheck.value;
                checklistProvider.notify();
              },
              checklistModel: checklistModel,
              isEdit: checklistModel != null,
            );
          }).toList(),
        );
      },
    );
  }
}

/// Button Row
class ButtonRow extends StatelessWidget {
  /// Constructor
  const ButtonRow({
    required this.onPressed,
    required this.data,
    this.isEdit = false,
    this.value = false,
    super.key,
  });

  final VoidCallback onPressed;
  final CheckListModel data;
  final bool isEdit;
  final bool value;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Flexible(
          fit: FlexFit.tight,
          child: Text(data.name, style: context.textTheme.titleSmall),
        ),
        if (data.value == null && !isEdit)
          Checkbox(
            value: value,
            checkColor: Colors.white,
            onChanged: (_) => onPressed(),
          )
        else
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: CircleAvatar(
              radius: 11.5,
              backgroundColor: data.value!
                  ? AppColors.ff17BD8D
                  : AppColors.errorColor,
              child: Icon(
                data.value! ? Icons.check_rounded : Icons.close_rounded,
                color: AppColors.white,
                size: 15,
              ),
            ),
          ),
      ],
    );
  }
}

class _CheckListWidget extends StatefulWidget {
  const _CheckListWidget(
    this.list,
    this.title,
    this.onPress, {
    this.checklistModel,
    this.isEdit = false,
  });
  final List<CheckListModel> list;
  final String title;
  final ChecklistModel? checklistModel;
  final bool isEdit;
  final Function(int index) onPress;

  @override
  State<_CheckListWidget> createState() => __CheckListWidgetState();
}

class __CheckListWidgetState extends State<_CheckListWidget> {
  int length = 6;

  @override
  void initState() {
    length = widget.list.length > 6 ? 6 : widget.list.length;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: AppSize.h10),
          child: Text(
            widget.title,
            style: context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ...List.generate(
          length,
          (index) => ValueListenableBuilder(
            valueListenable: widget.list[index].isCheck,
            builder: (context, value, child) {
              return Padding(
                padding: EdgeInsets.only(
                  bottom: widget.checklistModel != null ? 10 : 0,
                ),
                child: ButtonRow(
                  data: widget.list[index],
                  isEdit: widget.isEdit,
                  value: value,
                  onPressed: () => widget.onPress(index),
                ),
              );
            },
          ),
        ),
        if (widget.list.length > 6)
          Align(
            child: TextButton(
              onPressed: () {
                setState(() {
                  if (length == widget.list.length) {
                    length = 6;
                  } else {
                    length = widget.list.length;
                  }
                });
              },
              child: Text(
                length == widget.list.length
                    ? context.l10n.viewLess
                    : context.l10n.viewMore,
                style: const TextStyle(fontSize: 15),
              ),
            ),
          ),
        const Gap(10),
      ],
    );
  }
}
