import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/completed_page/completed_tab.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/ongoing_tab.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/upcoming_tab_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/provider/accepted_trip_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/common_tab_widgets.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/keep_alive_wrapper.dart';

///AcceptedTripsTab Tab Ui
class AcceptedTripsTab extends StatelessWidget {
  /// AcceptedTripsTab Tab Constructor
  const AcceptedTripsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AcceptedTripProvider(),
      builder: (context, child) {
        final acceptedTripProvider = context.read<AcceptedTripProvider>();
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppSize.appPadding,
          ).add(EdgeInsets.only(top: AppSize.w16)),
          child: Column(
            spacing: AppSize.h16,
            children: [
              Row(
                spacing: AppSize.w8,
                children: List.generate(
                  3,
                  (index) => Selector<AcceptedTripProvider, int>(
                    selector: (p0, acceptedTripProvider) =>
                        acceptedTripProvider.acceptedTabValue,
                    builder: (context, acceptedTabIndex, child) {
                      return CommonTabWidgets(
                        text: switch (index) {
                          0 => context.l10n.upcoming,
                          1 => context.l10n.ongoing,
                          _ => context.l10n.completed,
                        },
                        tabIndex: index,
                        selectedIndex: acceptedTabIndex,
                        onTap: () =>
                            acceptedTripProvider.acceptedTabValueChange(index),
                      );
                    },
                  ),
                ),
              ),
              Expanded(
                child: PageView.builder(
                  controller: acceptedTripProvider.pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) => KeepAliveWrapper(
                    child: [
                      UpcomingTabScreen(
                        acceptedTripProvider: acceptedTripProvider,
                      ),
                      OngoingTab(acceptedTripProvider: acceptedTripProvider),
                      CompletedTab(acceptedTripProvider: acceptedTripProvider),
                    ][index],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
