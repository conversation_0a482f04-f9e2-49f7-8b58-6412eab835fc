import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

/// MyTrips Module Data Handling
class RequestedTripsProvider extends ChangeNotifier {
  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// selected trip data
  TripModel? tripData;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  final pageviewController = PageController();

  /// trip data list
  final exclusiveTripList = ValueNotifier<List<TripModel>>([]);
  final waitingTripList = ValueNotifier<List<TripModel>>([]);
  final restedTripList = ValueNotifier<List<TripModel>>([]);

  /// get trip data list loader
  final isShowExclusiveLoader = ValueNotifier<bool>(false);
  final isShowWaitingLoader = ValueNotifier<bool>(false);
  final isShowRestedLoader = ValueNotifier<bool>(false);

  /// cancel trip api token
  CancelToken? cancelExclusiveToken;
  CancelToken? cancelWaitingTripToken;
  CancelToken? cancelRestedTripToken;

  /// trip next api url
  final nextExclusiveUrl = ValueNotifier<String?>(null);
  final nextWaitingUrl = ValueNotifier<String?>(null);
  final nextRestedUrl = ValueNotifier<String?>(null);

  ValueNotifier<bool> isAddressLoading = ValueNotifier(false);

  ProviderListData? selectedProvider;

  /// requestedTab Value for changing tab
  int requestedTabIndex = 0;

  /// Update requestedTab Value
  /// [tabIndex] is the new tab index
  void requestedTabValueChange(int tabIndex) {
    if (_isClosed) return;

    try {
      if (pageviewController.page != tabIndex) {
        requestedTabIndex = tabIndex;
        pageviewController.animateToPage(
          tabIndex,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
        notify();
      }
    } catch (e) {
      'requestedTabValueChange error: $e'.logE;
    }
  }

  /// to access trip repo
  final tripRepo = Injector.instance<TripRepository>();

  /// loading variable for only if trip home screen api called
  bool isReqDataShowLoader = false;

  /// get requested trip api called
  CancelToken? reqTripToken;
  String? nextRequestedUrl;

  /// Get requested trip list
  /// [acceptedTabValue] is the tab index
  /// [isPagination] flag for pagination
  /// [isWantShowLoader] flag to show loader
  Future<void> getRequestedTripList(
    int acceptedTabValue, {
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (_isClosed) return;

    /// assign new value and remove old value
    switch (acceptedTabValue) {
      case 0:
        cancelExclusiveToken?.cancel();
        cancelExclusiveToken = CancelToken();
        if (!isPagination) nextExclusiveUrl.value = null;
      case 1:
        cancelWaitingTripToken?.cancel();
        cancelWaitingTripToken = CancelToken();
        if (!isPagination) nextWaitingUrl.value = null;
      case 2:
        cancelRestedTripToken?.cancel();
        cancelRestedTripToken = CancelToken();
        if (!isPagination) nextRestedUrl.value = null;
      default:
    }

    /// call api
    await commonFuncGetRequestedTripList(
      isWantShowLoader: isWantShowLoader,
      isPagination: isPagination,
      loadingFunction: ({required bool isLoad}) {
        if (acceptedTabValue == 0) {
          isShowExclusiveLoader.value = isLoad;
        } else if (acceptedTabValue == 1) {
          isShowWaitingLoader.value = isLoad;
        } else {
          isShowRestedLoader.value = isLoad;
        }
      },

      /// below function give me next url if there were paginated data
      nextUrl: switch (acceptedTabValue) {
        0 => nextExclusiveUrl,
        1 => nextWaitingUrl,
        _ => nextRestedUrl,
      }
          .value,

      /// below function give me final response get from api
      successFunction: ({required List<TripModel> dataList, String? nextUrl}) {
        switch (acceptedTabValue) {
          case 0:
            exclusiveTripList.value = dataList;
            nextExclusiveUrl.value = nextUrl;
          case 1:
            waitingTripList.value = dataList;
            nextWaitingUrl.value = nextUrl;
          case 2:
            restedTripList.value = dataList;
            nextRestedUrl.value = nextUrl;
          default:
        }
        notify();
      },
      requestedTripPageIndex: acceptedTabValue,
    );
  }

  /// get trip list based upon accepted tab value
  ValueNotifier<List<TripModel>> getTripList(int requestedTripPageIndex) {
    switch (requestedTripPageIndex) {
      case 0:
        return exclusiveTripList;
      case 1:
        return waitingTripList;
      default:
        return restedTripList;
    }
  }

  /// get cancel token based upon accepted tab value
  CancelToken? getCancelToken(int requestedTripPageIndex) {
    switch (requestedTripPageIndex) {
      case 0:
        return cancelExclusiveToken;
      case 1:
        return cancelWaitingTripToken;
      case 2:
        return cancelRestedTripToken;
      default:
        return null;
    }
  }

  /// common function for getting accepted trip lists
  Future<void> commonFuncGetRequestedTripList({
    required void Function({required bool isLoad}) loadingFunction,
    required void Function({
      required List<TripModel> dataList,
      String? nextUrl,
    }) successFunction,
    required String? nextUrl,
    bool isPagination = false,
    bool isWantShowLoader = false,
    required int requestedTripPageIndex,
  }) async {
    if (_isClosed) return;
    try {
      final url = '${EndPoints.getRequestedTrip}?booking_type='
          '${switch (requestedTripPageIndex) {
        0 => ExclusiveTripType.EXCLUSIVE.name,
        1 => ExclusiveTripType.WAITING_LIST.name,
        _ => ExclusiveTripType.RESTED.name,
      }}';
      if (isWantShowLoader) loadingFunction(isLoad: true);
      final response = await tripRepo.getRequestedTripList(
        ApiRequest(
          path: isPagination ? nextUrl ?? url : url,
          cancelToken: getCancelToken(requestedTripPageIndex),
        ),
      );
      final dummyList = [
        ...switch (requestedTripPageIndex) {
          0 => exclusiveTripList.value,
          1 => waitingTripList.value,
          _ => restedTripList.value,
        },
      ];
      response.when(
        success: (data) {
          if (_isClosed ||
              (getCancelToken(requestedTripPageIndex)?.isCancelled ?? true)) {
            return;
          }
          loadingFunction(isLoad: false);
          if (!isPagination) dummyList.clear();
          dummyList.addAll(data.results ?? []);
          successFunction(
            dataList: dummyList,
            nextUrl: data.next,
          );
          notify();
        },
        error: (error) {
          if (_isClosed ||
              (getCancelToken(requestedTripPageIndex)?.isCancelled ?? true)) {
            return;
          }
          loadingFunction(isLoad: false);
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed ||
          (getCancelToken(requestedTripPageIndex)?.isCancelled ?? true)) {
        return;
      }
      loadingFunction(isLoad: false);
      '_commonFuncGetRequestedTripList error: $e'.logE;
    }
  }

  /// Get requested trip detail api
  CancelToken? getTripDetailsToken;
  bool isShowTripDetailLoader = false;
  Future<dynamic> getTripDetails(
    String id,
    BuildContext context, {
    bool isCompleted = false,
    required int value,
    required RequestedTripsProvider requestedTripsProvider,
  }) async {
    if (_isClosed) return;
    tripData = null;

    getTripDetailsToken?.cancel();
    getTripDetailsToken = CancelToken();
    // unawaited(
    //   AppNavigationService.pushNamed(
    //     context,
    //     ChangeNotifierProvider.value(
    //       value: requestedTripsProvider,
    //       child: value == 2
    //           ? RestedRequestedTripsScreen(
    //               value: value,
    //               index: index,
    //               requestedTripsProvider: requestedTripsProvider,
    //             )
    //           : RequestedTripsScreen(
    //               value: value,
    //               requestedTripsProvider: requestedTripsProvider,
    //             ),
    //     ),
    //   ),
    // );
    try {
      final url = '${EndPoints.requestedTripDetail}/$id/';
      isShowTripDetailLoader = true;
      notify();
      final response = await tripRepo.getTripDetail(
        ApiRequest(
          path: url,
          cancelToken: getTripDetailsToken,
        ),
      );
      isShowTripDetailLoader = false;
      if (_isClosed) return;

      response.when(
        success: (data) {
          if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
          tripData = TripModel.fromJson(data);
        },
        error: (error) {
          if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );
      notify();
    } catch (e) {
      if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
      isShowTripDetailLoader = false;
      notify();
      'req trip getTripDetails error: $e'.logE;
    }
  }

  /// Cancel requested trip
  /// [index] is the trip index
  /// [context] is the BuildContext
  /// [isBack] flag to navigate back
  /// [requestedTripPageIndex] is the tab index
  CancelToken? canReqTripToken;
  Future<dynamic> removeReqTrip(
    int? index,
    String id,
    BuildContext context, {
    bool isBack = false,
    required int requestedTripPageIndex,
  }) async {
    if (_isClosed) return;

    canReqTripToken?.cancel();
    canReqTripToken = CancelToken();
    try {
      final url = EndPoints.cancelReqTrip(id);
      isReqDataShowLoader = true;
      notify();
      final response = await tripRepo.removeReqTrip(
        ApiRequest(
          path: url,
          cancelToken: canReqTripToken,
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed || (canReqTripToken?.isCancelled ?? true)) return;

          final dummyList = [
            ...getTripList(requestedTripPageIndex).value,
          ];
          if (index != null) dummyList.removeAt(index);

          /// remove trip data from trip list according to page index
          switch (requestedTripPageIndex) {
            case 0:
              exclusiveTripList.value = dummyList;
            case 1:
              waitingTripList.value = dummyList;
            case 2:
              restedTripList.value = dummyList;
            default:
          }
          if (isBack) AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (canReqTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isReqDataShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (canReqTripToken?.isCancelled ?? true)) return;
      isReqDataShowLoader = false;
      notify();
      'removeReqTrip error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    pageviewController.dispose();

    // Cancel all tokens
    cancelExclusiveToken?.cancel();
    cancelWaitingTripToken?.cancel();
    cancelRestedTripToken?.cancel();
    canReqTripToken?.cancel();

    // Dispose all ValueNotifiers
    exclusiveTripList.dispose();
    waitingTripList.dispose();
    restedTripList.dispose();
    isShowExclusiveLoader.dispose();
    isShowWaitingLoader.dispose();
    isShowRestedLoader.dispose();
    nextExclusiveUrl.dispose();
    nextWaitingUrl.dispose();
    nextRestedUrl.dispose();
    isAddressLoading.dispose();

    super.dispose();
  }
}
