import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class RequestedTripCommonProvider extends ChangeNotifier {
  RequestedTripCommonProvider(String tripId, BuildContext context) {
    getTripDetails(tripId, context);
  }

  final refreshController = EasyRefreshController();

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// selected trip data
  TripModel? tripData;

  /// to access trip repo
  final tripRepo = Injector.instance<TripRepository>();

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Get requested trip detail api
  CancelToken? getTripDetailsToken;
  bool isShowTripDetailLoader = false;

  Future<dynamic> getTripDetails(String tripId, BuildContext context) async {
    if (_isClosed) return;
    tripData = null;

    getTripDetailsToken?.cancel();
    getTripDetailsToken = CancelToken();

    try {
      final url = '${EndPoints.requestedTripDetail}/$tripId/';
      isShowTripDetailLoader = true;
      notify();
      final response = await tripRepo.getTripDetail(
        ApiRequest(path: url, cancelToken: getTripDetailsToken),
      );
      isShowTripDetailLoader = false;
      if (_isClosed) return;

      response.when(
        success: (data) {
          if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
          tripData = TripModel.fromJson(data);
        },
        error: (error) {
          if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
          // error.message.showErrorAlert();
        },
      );
      notify();
    } catch (e) {
      if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
      isShowTripDetailLoader = false;
      notify();
      'req trip common getTripDetails error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    getTripDetailsToken?.cancel();
    refreshController.dispose();

    super.dispose();
  }
}
