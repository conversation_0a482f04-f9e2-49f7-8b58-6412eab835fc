import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/pages/auction_page/widgets/congratulations_dialog_box.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_textfield.dart';

/// Show filter bottom sheet
Future<T?> showBidBottomSheet<T>(BuildContext context) {
  return showModalBottomSheet<T>(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(AppSize.r12)),
    ),
    backgroundColor: AppColors.pageBGColor,
    isScrollControlled: true,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Padding(
          padding: EdgeInsets.all(AppSize.bottomSheetPadding),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.l10n.changeBid,
                    style: context.textTheme.titleLarge?.copyWith(),
                  ),
                  TextButton(
                    onPressed: () {
                      // Clear all filters logic here
                    },
                    child: Text.rich(
                      TextSpan(
                        text: '${context.l10n.yourCurrentBid} ',
                        style: context.textTheme.titleMedium?.copyWith(
                          color: AppColors.ff67509C,
                        ),
                        children: [
                          TextSpan(
                            text: r'$1225',
                            style: context.textTheme.titleLarge?.copyWith(
                              color: AppColors.ff67509C,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Gap(AppSize.h10),
              Text.rich(
                TextSpan(
                  text: '${context.l10n.minimumBidRequired} ',
                  style: const TextStyle(
                    color: AppColors.ff6C757D,
                    fontWeight: FontWeight.normal,
                  ),
                  children: [
                    TextSpan(
                      text: r'$1250',
                      style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.ff67509C,
                      ),
                    ),
                  ],
                ),
              ),
              Gap(AppSize.h10),
              Text.rich(
                TextSpan(
                  text: '${context.l10n.note}: ',
                  style: const TextStyle(
                    color: AppColors.ff6C757D,
                    fontWeight: FontWeight.bold,
                  ),
                  children: [
                    TextSpan(
                      text: context.l10n.minimumRequirementFor,
                      style: const TextStyle(
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              Gap(AppSize.h10),
              Text(
                context.l10n.chooseYourBidIncrement,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Gap(AppSize.h5),
              AppTextFormField(
                hintText: r'$50',
                hintStyle: const TextStyle(color: AppColors.ff6C757D),
                suffixIcon: Icon(
                  Icons.keyboard_arrow_down_sharp,
                  size: AppSize.h26,
                  color: AppColors.ff6C757D,
                ),
              ),
              Gap(AppSize.h10),
              Text.rich(
                TextSpan(
                  text: '${context.l10n.yourCurrentBid}: ',
                  style: context.textTheme.titleMedium?.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                  children: [
                    TextSpan(
                      text: r'$1275',
                      style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.ff67509C,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Gap(AppSize.h10),
              Text.rich(
                TextSpan(
                  text: '${context.l10n.yourPositionInList}: ',
                  style: context.textTheme.titleMedium?.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                  children: [
                    TextSpan(
                      text: '3/9',
                      style: context.textTheme.titleMedium?.copyWith(
                        color: AppColors.ff67509C,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Gap(AppSize.h16),
              AppButton(
                text: 'Save',
                onPressed: () {
                  Navigator.pop(context);
                  congratulationsDialogBox(context);
                },
              ),
            ],
          ),
        ),
      );
    },
  );
}
