import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/pages/auction_page/provider/auction_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/pages/auction_page/widgets/bid_sheets.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/title_info.dart';

///Auction Page
class AuctionScreen extends StatefulWidget {
  ///Constructor
  const AuctionScreen({super.key});

  @override
  State<AuctionScreen> createState() => _AuctionScreenState();
}

class _AuctionScreenState extends State<AuctionScreen> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AuctionProvider(),
      child: Consumer<AuctionProvider>(
        builder: (context, auctionProvider, _) {
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              title: context.l10n.auction,
              actions: [
                Container(
                  decoration: const BoxDecoration(color: AppColors.ffF2EEF8),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSize.w14,
                      vertical: AppSize.h4,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Align(
                          child: Text(
                            context.l10n.closingIn,
                            style: TextStyle(
                              fontSize: AppSize.sp10,
                              color: AppColors.ff67509C,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            Row(
                              spacing: AppSize.w2,
                              children: [
                                AppAssets.iconsTimer.image(
                                  height: AppSize.h10,
                                  width: AppSize.w10,
                                  fit: BoxFit.cover,
                                ),
                                Text(
                                  '2 Days, 12hr',
                                  style: TextStyle(
                                    fontSize: AppSize.sp10,
                                    color: AppColors.ff67509C,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    color: AppColors.white,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSize.w16,
                        vertical: AppSize.h16,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: AppSize.h10),
                          Text(
                            context.l10n.opnTrackTransportation,
                            style: context.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.normal,
                              color: AppColors.black,
                            ),
                          ),
                          SizedBox(height: AppSize.h10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Column(
                                  spacing: AppSize.h10,
                                  children: [
                                    TitleInfo(
                                      title: '${context.l10n.availableSlot}:',
                                      subTitle: '2',
                                      subTitleFontWeight: FontWeight.bold,
                                      subTitleColor: AppColors.ff67509C,
                                    ),
                                    TitleInfo(
                                      title: '${context.l10n.yourCurrentBid}:',
                                      subTitle: r'$1175',
                                      subTitleFontWeight: FontWeight.bold,
                                      subTitleColor: AppColors.ff67509C,
                                    ),
                                  ],
                                ),
                              ),
                              Flexible(
                                child: Column(
                                  spacing: AppSize.h10,
                                  children: [
                                    TitleInfo(
                                      title: '${context.l10n.carsWanted}:',
                                      subTitle: '6',
                                      subTitleFontWeight: FontWeight.bold,
                                      subTitleColor: AppColors.ff67509C,
                                    ),
                                    TitleInfo(
                                      title: '${context.l10n.yourCurrentSpot}:',
                                      subTitle: context.l10n.uHaveNotBid,
                                      subTitleFontWeight: FontWeight.bold,
                                      subTitleColor: AppColors.ff67509C,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Gap(AppSize.h16),
                        ],
                      ),
                    ),
                  ),
                  Gap(AppSize.h10),
                  Text(
                    context.l10n.listOfCurrentBid,
                    style: context.textTheme.titleLarge,
                  ),
                  Gap(AppSize.h10),
                  Expanded(
                    child: ListView.builder(
                      itemCount: auctionProvider.bidsUserList.length,
                      itemBuilder: (context, index) {
                        return Container(
                          decoration: BoxDecoration(
                            color: index <= 1
                                ? AppColors.ffD1ECF1
                                : AppColors.transparent,
                            borderRadius: index == 0
                                ? BorderRadius.only(
                                    topLeft: Radius.circular(
                                      AppSize.borderRadius,
                                    ),
                                    topRight: Radius.circular(
                                      AppSize.borderRadius,
                                    ),
                                  )
                                : BorderRadius.only(
                                    bottomLeft: Radius.circular(
                                      AppSize.borderRadius,
                                    ),
                                    bottomRight: Radius.circular(
                                      AppSize.borderRadius,
                                    ),
                                  ),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSize.w26,
                              vertical: AppSize.h8,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Text(
                                    '${index + 1}.${auctionProvider.bidsUserList[index]['userName']}',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Text(
                                  auctionProvider.bidsUserList[index]['amount']
                                          ?.toString() ??
                                      '-',
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  Gap(AppSize.h10),
                  Text.rich(
                    TextSpan(
                      text: '${context.l10n.note}:',
                      style: const TextStyle(
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.bold,
                      ),
                      children: [
                        TextSpan(
                          text: context.l10n.theUsersListed,
                          style: const TextStyle(
                            color: AppColors.ff6C757D,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(AppSize.h10),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      if (auctionProvider.isBid) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSize.w8,
                                ),
                                child: AppButton(
                                  onPressed: () {
                                    auctionProvider.updateBid(value: false);
                                  },
                                  text: context.l10n.exitAuction,
                                  isFillButton: false,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSize.w8,
                                ),
                                child: AppButton(
                                  onPressed: () {
                                    showBidBottomSheet(context);
                                  },
                                  text: context.l10n.changeBid,
                                ),
                              ),
                            ),
                          ],
                        );
                      } else {
                        return AppButton(
                          text: context.l10n.startBiding,
                          onPressed: () {
                            auctionProvider.updateBid(value: true);
                          },
                        );
                      }
                    },
                  ),
                  Gap(AppSize.h16),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
