import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';

///Rested Shipment Card widgets
class WaitingShipmentCard extends StatelessWidget {
  ///Constructor
  const WaitingShipmentCard({
    super.key,
    this.vehiclesCount,
    this.startTitle,
    this.startDate,
    this.endTitle,
    this.endDate,
    this.startLocation,
    this.endLocation,
  });

  ///for Vehicles count
  final int? vehiclesCount;

  ///for Start Title
  final String? startTitle;

  ///for Start Date
  final String? startDate;

  ///for End Title
  final String? endTitle;

  ///For End Date
  final String? endDate;

  ///For Start Location
  final (String?, String?)? startLocation;

  ///For End Location
  final (String?, String?)? endLocation;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.white,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w10,
          vertical: AppSize.h10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: AppSize.h10),
            Text(
              context.l10n.yourShipment,
              style: context.textTheme.titleLarge,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NoOfVehicleWidget(
                  noOfVehicle: vehiclesCount.toString(),
                  isTitleWidget: true,
                ),
                GestureDetector(
                  onTap: () {
                    // Handle edit action here
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppSize.h10),
                    child: Text(
                      context.l10n.edit,
                      style: context.textTheme.titleLarge?.copyWith(
                        color: AppColors.ff0087C7,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: AppSize.h16),
            // Locations and dates row
            LocationInfoWidget(
              startLatitude: startLocation?.$1,
              startLongitude: startLocation?.$2,
              endLatitude: endLocation?.$1,
              endLongitude: endLocation?.$2,
              startLocationTitle: startTitle?.toString() ?? '-',
              startLocationDate: startDate?.toString() ?? '-',
              endLocationTitle: endTitle?.toString() ?? '-',
              endLocationDate: endDate?.toString() ?? '-',
            ),
          ],
        ),
      ),
    );
  }
}
