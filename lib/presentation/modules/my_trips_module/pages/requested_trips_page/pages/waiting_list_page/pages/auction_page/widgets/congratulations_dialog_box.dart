import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';

/// Show filter bottom sheet
Future<T?> congratulationsDialogBox<T>(BuildContext context) {
  return showDialog<T>(
    context: context,
    builder: (context) {
      return AlertDialog(
        insetPadding: EdgeInsets.all(AppSize.r20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppSize.r12)),
        ),
        icon: AppAssets.iconsSuccess.image(
          height: AppSize.h70,
          fit: BoxFit.fitHeight,
        ),
        title: Text(
          context.l10n.congratulation,
          style: context.textTheme.titleLarge,
        ),
        content: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w1),
          child: Text(
            context.l10n.youHaveGotTransportation,
            textAlign: TextAlign.center,
            style: context.textTheme.titleMedium?.copyWith(
              color: AppColors.ff6C757D,
            ),
          ),
        ),
        actions: [
          AppButton(
            text: context.l10n.continues,
            onPressed: () {
              Navigator.pop(context);
              // AppNavigationService.pushNamed(
              //   context,
              //   ShipmentConfirmationScreen(
              //     bookingProvider: BookingProvider([], sessionId: ''),
              //     homeProvider: context.read<HomeProvider>(),
              //   ),
              // );
            },
          ),
        ],
      );
    },
  );
}
