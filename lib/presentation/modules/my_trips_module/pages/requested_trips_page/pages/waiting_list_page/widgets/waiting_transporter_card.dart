import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/dashed_divider.dart';
import 'package:transport_match/widgets/title_info.dart';

/// Waiting Transporter card
class WaitingTransporterCard extends StatelessWidget {
  /// Constructor
  const WaitingTransporterCard({
    required this.accept,
    required this.reject,
    super.key,
  });

  ///on Pressed accept
  final VoidCallback accept;

  ///on Pressed accept Reject
  final VoidCallback reject;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h10),
      padding: EdgeInsets.all(AppSize.h10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row with "Your Shipment" and "Edit"
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  context.l10n.opnTrackTransportation,
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.normal,
                    overflow: TextOverflow.ellipsis,
                    color: AppColors.black,
                  ),
                ),
              ),
              Text(
                r'$ 10,000',
                style: context.textTheme.titleLarge?.copyWith(
                  color: AppColors.ff67509C,
                ),
              ),
            ],
          ),
          Gap(AppSize.h8),
          Row(
            children: [
              const Icon(Icons.star, color: AppColors.ffFFC107),
              Gap(AppSize.w2),
              Text(
                '4.3',
                style: context.textTheme.bodySmall?.copyWith(
                  color: AppColors.ff6C757D,
                ),
              ),
              Gap(AppSize.w10),
              Text(
                'Avail. Slots: ',
                style: context.textTheme.bodySmall?.copyWith(
                  color: AppColors.ff6C757D,
                ),
              ),
              Text(
                '2',
                style: context.textTheme.bodySmall?.copyWith(
                  color: AppColors.ff6C757D,
                ),
              ),
            ],
          ),

          Gap(AppSize.h16),

          // Locations row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: AppSize.w8,
            children: [
              // kilometer is use for location
              TitleInfo(title: context.l10n.origin, subTitle: 'Otay Mesa'),
              Flexible(
                child: Row(
                  children: [
                    AppAssets.iconsLocationOrigin.image(height: AppSize.h16),
                    const DashedDivider(),
                    AppAssets.iconsLocation.image(height: AppSize.h16),
                  ],
                ),
              ),
              TitleInfo(
                title: context.l10n.destination,
                subTitle: 'California',
              ),
            ],
          ),
          Gap(AppSize.h16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TitleInfo(title: context.l10n.small, subTitle: r'$2.8/km'),
              TitleInfo(title: context.l10n.medium, subTitle: r'$3.5/km'),
              TitleInfo(title: context.l10n.large, subTitle: r'$4.9/km'),
            ],
          ),
          Gap(AppSize.h10),
          Divider(thickness: AppSize.h1, color: AppColors.unSelectedColor),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              TextButton(
                onPressed: accept,
                child: Text(
                  context.l10n.accept,
                  style: context.textTheme.titleLarge?.copyWith(
                    color: AppColors.successColor,
                  ),
                ),
              ),
              TextButton(
                onPressed: reject,
                child: Text(
                  context.l10n.reject,
                  style: context.textTheme.titleLarge?.copyWith(
                    color: AppColors.red,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
