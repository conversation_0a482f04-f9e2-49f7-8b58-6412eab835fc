import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/widgets/waiting_shipment_card.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/waiting_list_page/widgets/waiting_transporter_card.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

///Waiting Transporter List ui
class WaitingTransporterListScreen extends StatefulWidget {
  ///Constructor
  const WaitingTransporterListScreen({super.key});

  @override
  State<WaitingTransporterListScreen> createState() =>
      _WaitingTransporterListScreenState();
}

class _WaitingTransporterListScreenState
    extends State<WaitingTransporterListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.ffF8F9FA,
      appBar: CustomAppBar(title: context.l10n.transportList),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  const WaitingShipmentCard(
                    vehiclesCount: 1,
                    startTitle: 'Otay Mesa',
                    startDate: 'Nov 21',
                    endTitle: 'California',
                    endDate: 'Nov 28',
                    startLocation: ('21.2546', '72.4564'),
                    endLocation: ('21.2546', '72.4564'),
                  ),
                  Gap(AppSize.h16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.l10n.chooseTransporter,
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.black,
                          fontSize: AppSize.w20,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {},
                        child: Text(
                          context.l10n.filterBy,
                          style: context.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.ff0087C7,
                            fontSize: AppSize.w20,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Gap(AppSize.h16),
                ],
              ),
            ),
            SliverList.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {},
                  child: WaitingTransporterCard(
                    accept: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(AppSize.r12),
                              bottom: Radius.circular(AppSize.r12),
                            ),
                          ),
                          insetPadding: EdgeInsets.symmetric(
                            horizontal: AppSize.w16,
                          ),
                          actions: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.h8,
                                    ),
                                    child: AppButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                        AppNavigationService.pushNamed(
                                          context,
                                          AppRoutes.tripsAuctionScreen,
                                          // const AuctionScreen(),
                                        );
                                      },
                                      text: context.l10n.enterAuction,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.h8,
                                    ),
                                    child: AppButton(
                                      onPressed: () {},
                                      text: context.l10n.reject,
                                      textStyle: context.textTheme.bodyMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: AppColors.errorColor,
                                            fontSize: AppSize.sp16,
                                          ),
                                      foregroundColor: AppColors.errorColor,
                                      isFillButton: false,
                                      borderColor: AppColors.errorColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                          content: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSize.w8,
                              vertical: AppSize.h8,
                            ),
                            child: Text(
                              context.l10n.dueToMoreUser,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: AppColors.ff6C757D,
                                height: 1.8,
                                fontSize: AppSize.h16,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    reject: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(AppSize.r12),
                              bottom: Radius.circular(AppSize.r12),
                            ),
                          ),
                          insetPadding: EdgeInsets.symmetric(
                            horizontal: AppSize.w16,
                          ),
                          actions: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.h8,
                                    ),
                                    child: AppButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      text: context.l10n.wait,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: AppSize.h8,
                                    ),
                                    child: AppButton(
                                      onPressed: () {},
                                      text: context.l10n.joinWaitlist,
                                      foregroundColor: AppColors.red,
                                      isFillButton: false,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                          icon: AppAssets.iconsNoProvider.image(
                            height: AppSize.h80,
                          ),
                          title: Text(
                            context.l10n.areUSure,
                            style: context.textTheme.titleLarge,
                          ),
                          content: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSize.w8,
                            ),
                            child: Text(
                              context.l10n.uAreRejecting,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: AppColors.ff6C757D,
                                height: 1.8,
                                fontSize: AppSize.h16,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
