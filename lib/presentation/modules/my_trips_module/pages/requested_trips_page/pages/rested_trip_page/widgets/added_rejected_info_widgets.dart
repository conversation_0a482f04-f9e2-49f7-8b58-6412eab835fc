import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/car_info_screen.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// VehiclesInfoWidget
class AddedRejectedInfoWidgets extends StatefulWidget {
  /// Constructor
  const AddedRejectedInfoWidgets({
    this.isRejected = false,
    this.isPending = false,
    super.key,
    required this.data,
  });

  final List<VehicleInfoModel> data;
  final bool isRejected;
  final bool isPending;

  @override
  State<AddedRejectedInfoWidgets> createState() =>
      _AddedRejectedInfoWidgetsState();
}

class _AddedRejectedInfoWidgetsState extends State<AddedRejectedInfoWidgets> {
  late PageController _pageController;

  @override
  void initState() {
    _pageController = PageController();
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.data.isEmpty
        ? const SizedBox()
        : Padding(
            padding: EdgeInsets.only(top: AppSize.h6),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.isRejected
                          ? context.l10n.rejectedVehiclesInfo
                          : widget.isPending
                          ? context.l10n.pendingVehiclesInfo
                          : context.l10n.addedVehiclesInfo,
                      style: context.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: AppSize.sp16,
                      ),
                    ),
                    AnimatedBuilder(
                      animation: _pageController,
                      builder: (context, child) {
                        return Text(
                          '${(_pageController.positions.isEmpty ? 0 : _pageController.page?.round() ?? 0) + 1}'
                          '/${widget.data.length}',
                          style: context.textTheme.bodyLarge?.copyWith(
                            fontSize: AppSize.sp16,
                            fontWeight: FontWeight.w400,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                Gap(AppSize.h8),
                SizedBox(
                  height: 190.h,
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: widget.data.length,
                    itemBuilder: (context, index) {
                      final carData = widget.data[index];
                      return Container(
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          border: Border.all(
                            color: widget.isRejected
                                ? AppColors.red
                                : AppColors.ffDEE2E6,
                          ),
                          borderRadius: BorderRadius.circular(AppSize.r4),
                        ),
                        margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
                        padding: EdgeInsets.all(AppSize.sp16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                VehiclesInfoField(
                                  title: context.l10n.carBrand,
                                  value:
                                      carData.car?.brand ?? carData.brand ?? '',
                                ),
                                GestureDetector(
                                  onTap: () => AppNavigationService.pushNamed(
                                    context,
                                    AppRoutes.carInfoSheet,
                                    extra: CarInfoParams(
                                      carDetail: CarDetailModel(
                                        brandName:
                                            carData.car?.brand ??
                                            carData.brand ??
                                            '',
                                        carName:
                                            carData.car?.model ??
                                            carData.model ??
                                            '',
                                        yearValue:
                                            carData.car?.year ??
                                            carData.year?.toString() ??
                                            '',
                                        serialNumber:
                                            carData.serialNumber ?? '',
                                        carSize:
                                            carData.car?.size ?? carData.size,
                                        carDescription: carData.carDescription,
                                        brand: '',
                                        car: null,
                                        year:
                                            carData.year?.toString() ??
                                            carData.car?.year?.toString() ??
                                            '',
                                        isWinchRequired:
                                            carData.isWinchRequired,
                                        images: carData.images,
                                      ),
                                    ),
                                  ),
                                  behavior: HitTestBehavior.opaque,
                                  child: Text(
                                    context.l10n.details,
                                    style: context.textTheme.titleLarge
                                        ?.copyWith(
                                          color: AppColors.primaryColor,
                                          fontSize: AppSize.sp16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                            Gap(AppSize.h8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                VehiclesInfoField(
                                  title: context.l10n.carModel,
                                  value:
                                      carData.car?.model ?? carData.model ?? '',
                                ),
                                VehiclesInfoField(
                                  title: context.l10n.carYear,
                                  value:
                                      carData.car?.year ??
                                      carData.year?.toString() ??
                                      '',
                                ),
                              ],
                            ),
                            Gap(AppSize.h8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                if (carData.serialNumber.isNotEmptyAndNotNull)
                                  VehiclesInfoField(
                                    title: '${context.l10n.carSerial} #',
                                    value: carData.serialNumber ?? '',
                                  ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                Gap(AppSize.h10),
                AnimatedBuilder(
                  animation: _pageController,
                  builder: (context, child) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        for (var i = 0; i < widget.data.length; i++)
                          Container(
                            margin: EdgeInsets.symmetric(
                              horizontal: AppSize.w4,
                            ),
                            width: AppSize.w10,
                            height: AppSize.w10,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: i == (_pageController.page?.round() ?? 0)
                                  ? Colors.blue
                                  : Colors.grey[400],
                            ),
                          ),
                      ],
                    );
                  },
                ),
              ],
            ),
          );
  }
}
