import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/rested_requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/provider/rested_trip_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/common_card_widgets.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';

///Rested Trip page
class RestedTripTab extends StatelessWidget {
  ///Constructor
  const RestedTripTab(this.requestedTripsProvider, {super.key});
  final RequestedTripsProvider requestedTripsProvider;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => RestedTripProvider(
        () => requestedTripsProvider.getRequestedTripList(
          2,
          isWantShowLoader: true,
        ),
      ),
      builder: (context, child) {
        final restedTripProvider = context.watch<RestedTripProvider>();
        return ValueListenableBuilder(
          valueListenable: requestedTripsProvider.restedTripList,
          builder: (context, list, child) {
            return ValueListenableBuilder(
              valueListenable: requestedTripsProvider.nextRestedUrl,
              builder: (context, nextUrl, nextUrlChild) {
                return ValueListenableBuilder(
                  valueListenable: requestedTripsProvider.isShowRestedLoader,
                  builder: (context, isShowLoader, child) {
                    return AppLoader(
                      isShowLoader: isShowLoader,
                      child: EasyRefresh(
                        triggerAxis: Axis.vertical,
                        header: AppCommonFunctions.getLoadingHeader(),
                        footer: AppCommonFunctions.getLoadingFooter(),
                        controller: restedTripProvider.refreshController,
                        onRefresh: () async =>
                            requestedTripsProvider.getRequestedTripList(2),
                        onLoad: () async => nextUrl.isNotEmptyAndNotNull
                            ? requestedTripsProvider.getRequestedTripList(
                                2,
                                isPagination: true,
                              )
                            : null,
                        child: !isShowLoader && list.isEmpty
                            ? const NoTripWidget()
                            : nextUrlChild!,
                      ),
                    );
                  },
                );
              },
              child: SingleChildScrollView(
                child: Column(
                  children: List.generate(
                    list.length,
                    (index) => GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.tripsRestedRequestedTripsScreen,
                        extra: RestedRequestedTripsParams(
                          value: 2,
                          id: list[index].id?.toString() ?? '0',
                          index: index,
                          requestedTripsProvider: requestedTripsProvider,
                        ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(bottom: AppSize.h16),
                        child: CommonCardWidgets(
                          requestedTripPageIndex: 2,
                          index: index,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
