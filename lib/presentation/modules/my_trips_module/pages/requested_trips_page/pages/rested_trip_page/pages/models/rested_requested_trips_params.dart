import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';

/// Parameter model for rested requested trips screen.
class RestedRequestedTripsParams {
  /// Constructor
  const RestedRequestedTripsParams({
    required this.value,
    required this.id,
    this.index,
    required this.requestedTripsProvider,
  });

  /// Value for UI state
  final int value;

  /// Trip ID
  final String id;

  /// Index
  final int? index;

  /// Requested trips provider instance
  final RequestedTripsProvider requestedTripsProvider;
}
