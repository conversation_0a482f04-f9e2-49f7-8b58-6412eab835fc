import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/edit_rested_trip_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/rested_requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/widgets/added_rejected_info_widgets.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Rested Requested Trips page
class RestedRequestedTripsScreen extends StatefulWidget {
  /// Rested Requested Trips Constructor
  const RestedRequestedTripsScreen({
    required this.restedRequestedTripsParams,
    super.key,
  });

  final RestedRequestedTripsParams restedRequestedTripsParams;

  @override
  State<RestedRequestedTripsScreen> createState() =>
      _RestedRequestedTripsScreenState();
}

class _RestedRequestedTripsScreenState
    extends State<RestedRequestedTripsScreen> {
  final refreshController = EasyRefreshController();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) => _getTripDetail());
    super.initState();
  }

  Future<void> _getTripDetail() async {
    await widget.restedRequestedTripsParams.requestedTripsProvider
        .getTripDetails(
          widget.restedRequestedTripsParams.id,
          context,
          value: widget.restedRequestedTripsParams.value,
          requestedTripsProvider:
              widget.restedRequestedTripsParams.requestedTripsProvider,
        );
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.restedRequestedTripsParams.requestedTripsProvider,
      child: Scaffold(
        backgroundColor: AppColors.ffF8F9FA,
        appBar: CustomAppBar(title: context.l10n.requestedTrips),
        body: Selector<RequestedTripsProvider, (bool, bool, TripModel?)>(
          selector: (p0, requestedTripsProvider) => (
            requestedTripsProvider.isReqDataShowLoader,
            requestedTripsProvider.isShowTripDetailLoader,
            requestedTripsProvider.tripData,
          ),
          builder: (context, loaderData, child) {
            final tripData = loaderData.$3;
            return AppLoader(
              isShowLoader: loaderData.$1 || loaderData.$2,
              child: loaderData.$1 || loaderData.$2
                  ? const SizedBox.expand()
                  : EasyRefresh(
                      triggerAxis: Axis.vertical,
                      header: AppCommonFunctions.getLoadingHeader(),
                      footer: AppCommonFunctions.getLoadingFooter(),
                      controller: refreshController,
                      onRefresh: () async => _getTripDetail(),
                      child:
                          (!(loaderData.$1 || loaderData.$2) &&
                              tripData == null)
                          ? Center(child: Text(context.l10n.noTripFound))
                          : SingleChildScrollView(
                              padding: EdgeInsets.symmetric(
                                horizontal: AppSize.appPadding,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                spacing: AppSize.h10,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(top: AppSize.h16),
                                    child: Text(
                                      context.l10n.userLocation,
                                      style: context.textTheme.titleLarge,
                                    ),
                                  ),
                                  AppTextFormField(
                                    fillColor: AppColors.ffE6E6E6,
                                    borderSide: const BorderSide(
                                      color: AppColors.unSelectedColor,
                                    ),
                                    controller: TextEditingController(
                                      text:
                                          tripData?.userStartLocation?.street ??
                                          tripData
                                              ?.startStopLocation
                                              ?.fullAddress ??
                                          '',
                                    ),
                                    readOnly: true,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(top: AppSize.h6),
                                    child: AppTextFormField(
                                      fillColor: AppColors.ffE6E6E6,
                                      borderSide: const BorderSide(
                                        color: AppColors.unSelectedColor,
                                      ),
                                      controller: TextEditingController(
                                        text:
                                            tripData?.userEndLocation?.street ??
                                            tripData
                                                ?.endStopLocation
                                                ?.fullAddress ??
                                            '',
                                      ),
                                      readOnly: true,
                                    ),
                                  ),

                                  /// these widgets are shown as pending vehicle
                                  AddedRejectedInfoWidgets(
                                    data:
                                        tripData?.carDetails
                                            ?.where(
                                              (element) =>
                                                  element.verificationStatus ==
                                                  CarVerificationStatus
                                                      .PENDING
                                                      .name,
                                            )
                                            .toList() ??
                                        [],
                                    isPending: true,
                                  ),

                                  /// These widgets are shown as confirmed vehicle
                                  AddedRejectedInfoWidgets(
                                    data:
                                        tripData?.carDetails
                                            ?.where(
                                              (element) =>
                                                  element.verificationStatus ==
                                                      CarVerificationStatus
                                                          .VERIFIED
                                                          .name ||
                                                  element.verificationStatus ==
                                                      null,
                                            )
                                            .toList() ??
                                        [],
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      border: Border.all(
                                        color: AppColors.ffDEE2E6,
                                      ),
                                      borderRadius: BorderRadius.circular(
                                        AppSize.r4,
                                      ),
                                    ),
                                    margin: EdgeInsets.only(top: AppSize.h6),
                                    padding: EdgeInsets.all(AppSize.sp16),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            context.l10n.pickupAndDeliveryDates,
                                            style: context.textTheme.titleLarge,
                                          ),
                                          Gap(AppSize.h16),
                                          Text(
                                            context.l10n.pickupDate,
                                            style: context.textTheme.titleLarge
                                                ?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.ff6C757D,
                                                ),
                                          ),
                                          Gap(AppSize.h4),
                                          Text(
                                            '${AppStrings.bullet}${tripData?.customerStartDate?.monthDateFormate}',
                                            style: context.textTheme.titleLarge
                                                ?.copyWith(
                                                  fontSize: AppSize.sp16,
                                                ),
                                          ),
                                          Gap(AppSize.h10),
                                          Text(
                                            context.l10n.deliveryDate,
                                            style: context.textTheme.titleLarge
                                                ?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.ff6C757D,
                                                ),
                                          ),
                                          Gap(AppSize.h4),
                                          Text(
                                            '${AppStrings.bullet}${tripData?.customerEndDate?.monthDateFormate}',
                                            style: context.textTheme.titleLarge
                                                ?.copyWith(
                                                  fontSize: AppSize.sp16,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  //this widgets are show  added and rejected
                                  AddedRejectedInfoWidgets(
                                    data:
                                        tripData?.carDetails
                                            ?.where(
                                              (element) =>
                                                  element.verificationStatus ==
                                                  CarVerificationStatus
                                                      .REJECTED
                                                      .name,
                                            )
                                            .toList() ??
                                        [],
                                    isRejected: true,
                                  ),

                                  Gap(AppSize.h20),
                                ],
                              ),
                            ),
                    ),
            );
          },
        ),
        bottomNavigationBar:
            Selector<RequestedTripsProvider, (bool, TripModel?)>(
              selector: (p0, requestedTripsProvider) => (
                requestedTripsProvider.isReqDataShowLoader,
                requestedTripsProvider.tripData,
              ),
              builder: (context, loaderData, child) {
                final tripData = loaderData.$2;
                return tripData == null
                    ? const SizedBox.shrink()
                    : Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSize.appPadding,
                        ),
                        child: Row(
                          spacing: AppSize.w10,
                          children: [
                            Expanded(
                              child: AppButton(
                                text: context.l10n.resumeTrip,
                                horizontalPad: 0,
                                onPressed: () {
                                  if (!loaderData.$1) {
                                    AppNavigationService.pushNamed(
                                      context,
                                      AppRoutes.tripsEditRestedTripScreen,
                                      extra: EditRestedTripParams(
                                        tripModelData: tripData,
                                        isFromDetailScreen: true,
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                            Expanded(
                              child: AppButton(
                                text: context.l10n.cancelBooking,
                                horizontalPad: 0,
                                isFillButton: false,
                                borderColor: AppColors.errorColor,
                                textStyle: context.textTheme.bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.errorColor,
                                      fontSize: AppSize.sp16,
                                    ),
                                onPressed: () {
                                  if (!loaderData.$1) {
                                    widget
                                        .restedRequestedTripsParams
                                        .requestedTripsProvider
                                        .removeReqTrip(
                                          widget
                                              .restedRequestedTripsParams
                                              .index,
                                          widget.restedRequestedTripsParams.id,
                                          context,
                                          isBack: true,
                                          requestedTripPageIndex: 2,
                                        );
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      );
              },
            ),
      ),
    );
  }
}
