import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/models/requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/widgets/common_card_widgets.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';

///Exclusive Trip screen
class ExclusiveTripTab extends StatefulWidget {
  ///Exclusive Trip Constructor
  const ExclusiveTripTab(this.requestedTripsProvider, {super.key});
  final RequestedTripsProvider requestedTripsProvider;

  @override
  State<ExclusiveTripTab> createState() => _ExclusiveTripTabState();
}

class _ExclusiveTripTabState extends State<ExclusiveTripTab> {
  final refreshController = EasyRefreshController();
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.requestedTripsProvider.getRequestedTripList(
        0,
        isWantShowLoader: true,
      );
    });
    super.initState();
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.requestedTripsProvider.exclusiveTripList,
      builder: (context, list, child) {
        return ValueListenableBuilder(
          valueListenable: widget.requestedTripsProvider.nextExclusiveUrl,
          builder: (context, nextUrl, nextUrlChild) {
            return ValueListenableBuilder(
              valueListenable:
                  widget.requestedTripsProvider.isShowExclusiveLoader,
              builder: (context, isShowLoader, child) {
                return Selector<RequestedTripsProvider, bool>(
                  selector: (p0, p1) => p1.isReqDataShowLoader,
                  builder: (context, isReqDataShowLoader, child) {
                    return AppLoader(
                      isShowLoader: isShowLoader || isReqDataShowLoader,
                      child: EasyRefresh(
                        triggerAxis: Axis.vertical,
                        header: AppCommonFunctions.getLoadingHeader(),
                        footer: AppCommonFunctions.getLoadingFooter(),
                        controller: refreshController,
                        onRefresh: () async => widget.requestedTripsProvider
                            .getRequestedTripList(0),
                        onLoad: () async => nextUrl.isNotEmptyAndNotNull
                            ? widget.requestedTripsProvider
                                  .getRequestedTripList(0, isPagination: true)
                            : null,
                        child:
                            !(isShowLoader || isReqDataShowLoader) &&
                                list.isEmpty
                            ? const NoTripWidget()
                            : nextUrlChild!,
                      ),
                    );
                  },
                );
              },
            );
          },
          child: SingleChildScrollView(
            child: Column(
              children: List.generate(
                list.length,
                (index) => GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () =>
                      AppNavigationService.pushNamed<RequestedTripsProvider>(
                        context,
                        AppRoutes.tripsRequestedTripsScreen,
                        extra: RequestedTripsParams(
                          tripId: list[index].id?.toString() ?? '0',
                        ),
                      ),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h16),
                    child: CommonCardWidgets(
                      requestedTripPageIndex: 0,
                      index: index,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
