import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/exclusive_trip_page/exclusive_trip_tab.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/rested_trip_tab.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/common_tab_widgets.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/keep_alive_wrapper.dart';

///AcceptedTrips Tab Ui
class RequestedTripsTab extends StatelessWidget {
  ///AcceptedTrips Tab Constructor
  const RequestedTripsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => RequestedTripsProvider(),
      child: Builder(
        builder: (context) {
          final requestedTripsProvider = context.read<RequestedTripsProvider>();
          return Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppSize.appPadding,
            ).add(EdgeInsets.only(top: AppSize.w16)),
            child: Column(
              spacing: AppSize.h16,
              children: [
                Row(
                  spacing: AppSize.h8,
                  children: List.generate(
                    2,
                    (index) => Selector<RequestedTripsProvider, int>(
                      selector: (p0, requestedTripsProvider) =>
                          requestedTripsProvider.requestedTabIndex,
                      builder: (context, requestedTabValue, child) {
                        return CommonTabWidgets(
                          text: switch (index) {
                            0 => context.l10n.exclusiveTrip,
                            // 1 => context.l10n.waitingList,
                            _ => context.l10n.restedTrip,
                          },
                          tabIndex: index,
                          onTap: () => requestedTripsProvider
                              .requestedTabValueChange(index),
                          selectedIndex: requestedTabValue,
                        );
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: PageView.builder(
                    controller: requestedTripsProvider.pageviewController,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) => KeepAliveWrapper(
                      child: [
                        ExclusiveTripTab(requestedTripsProvider),
                        // WaitingListTab(requestedTripsProvider),
                        RestedTripTab(requestedTripsProvider),
                      ][index],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
