// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/provider/app_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class LanguageChangeSheet extends StatelessWidget {
  const LanguageChangeSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final currentLocale = Localizations.localeOf(context);

    return Container(
      padding: EdgeInsets.all(AppSize.bottomSheetPadding),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppSize.r20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(
            child: Container(
              width: AppSize.w40,
              height: AppSize.h4,
              decoration: BoxDecoration(
                color: AppColors.ffDEE2E6,
                borderRadius: BorderRadius.circular(AppSize.r2),
              ),
            ),
          ),
          Gap(AppSize.h20),
          Text(
            l10n.selectLanguage,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: AppSize.sp18,
              fontWeight: FontWeight.w600,
              color: AppColors.ff2B2829,
            ),
          ),
          Gap(AppSize.h20),
          _LanguageOption(
            title: 'English',
            isSelected: currentLocale.languageCode == 'en',
            onTap: () async {
              if (currentLocale.languageCode != 'en') {
                Injector.instance<AppProvider>().changeLocale(
                  const Locale('en'),
                );
                Navigator.pop(context);
                'Language changed successfully'.showSuccessAlert();
              }
            },
          ),
          Gap(AppSize.h12),
          _LanguageOption(
            title: 'Spanish',
            isSelected: currentLocale.languageCode == 'es',
            onTap: () async {
              if (currentLocale.languageCode != 'es') {
                Injector.instance<AppProvider>().changeLocale(
                  const Locale('es'),
                );
                Navigator.pop(context);
                'Idioma cambiado exitosamente'.showSuccessAlert();
              }
            },
          ),
          Gap(AppSize.h20),
        ],
      ),
    );
  }
}

class _LanguageOption extends StatelessWidget {
  const _LanguageOption({
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w16,
          vertical: AppSize.h12,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primaryColorLight.withValues(alpha: 0.3)
              : AppColors.white,
          borderRadius: BorderRadius.circular(AppSize.r8),
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : AppColors.ffDEE2E6,
          ),
        ),
        child: Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: AppSize.sp16,
                fontWeight: FontWeight.w500,
                color: isSelected ? AppColors.primaryColor : AppColors.ff2B2829,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primaryColor,
                size: AppSize.w20,
              ),
          ],
        ),
      ),
    );
  }
}
