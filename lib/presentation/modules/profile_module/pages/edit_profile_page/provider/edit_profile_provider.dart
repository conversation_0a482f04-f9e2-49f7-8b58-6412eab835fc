import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/verify_otp_model.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/models/edit_profile_param.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

/// EditProfileProvider
class EditProfileProvider extends ChangeNotifier {
  EditProfileProvider(EditProfileParam editProfileParam) {
    nameController.text = editProfileParam.name;
    emailController.text = editProfileParam.email;
  }

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? updateProfileToken;

  /// Update user data
  /// [context] is the BuildContext
  /// [profileProvider] is the ProfilerProvider instance
  Future<void> updateUserData({
    required BuildContext context,
    required void Function(UserModel userData) onEditCallBack,
  }) async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      updateProfileToken?.cancel();
      updateProfileToken = CancelToken();
      final data = {ApiKeys.firstName: nameController.text.trim()};
      final request = ApiRequest(
        path:
            '${EndPoints.getUserInfo}${Injector.instance<AppDB>().userModel?.user?.id}/',
        data: data,
        cancelToken: updateProfileToken,
      );
      final res =
          await Injector.instance<AccountRepository>().updateUserInfo(request);

      if (_isClosed) return;

      await res.when(
        success: (data) async {
          isShowLoader.value = false;
          if (_isClosed) return;
          if (updateProfileToken?.isCancelled ?? true) return;
          if (data.user != null) {
            onEditCallBack(data.user!);
          }
          notify();
          Injector.instance<AppDB>().userModel = data;
          AppNavigationService.pop(context);
        },
        error: (exception) async {
          isShowLoader.value = false;
          if (_isClosed) return;
          if (updateProfileToken?.isCancelled ?? true) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoader.value = false;
      if (_isClosed) return;
      if (updateProfileToken?.isCancelled ?? true) return;
      'updateUserData error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;

    // Cancel all tokens
    updateProfileToken?.cancel();

    // Dispose all controllers
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();

    // Dispose all ValueNotifiers
    isShowLoader.dispose();

    super.dispose();
  }
}
