import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class SetNewPasswordProvider extends ChangeNotifier {
  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Text controllers for password fields
  final oldPasswordController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final newPasswordKey = GlobalKey<FormState>();

  /// Visibility flags for password fields
  final isOldPasswordShow = ValueNotifier<bool>(false);
  final isPasswordShow = ValueNotifier<bool>(false);
  final isConfirmPasswordShow = ValueNotifier<bool>(false);

  /// Loading state flag
  final isShowLoader = ValueNotifier<bool>(false);

  /// Token for reset password API
  CancelToken? resetPasswordCancelToken;

  /// Reset password API call
  /// [context] is the BuildContext
  Future<void> resetPasswordAPIcall({required BuildContext context}) async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      resetPasswordCancelToken?.cancel();
      resetPasswordCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.resetType: PasswordType.PROFILE_RESET_PASSWORD.name,
        ApiKeys.password: passwordController.text,
        ApiKeys.oldPassword: oldPasswordController.text,
      };
      final request = ApiRequest(
        path: EndPoints.resetPassword,
        data: data,
        cancelToken: resetPasswordCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().restPassword(request);

      if (_isClosed) return;

      await res.when(
        success: (data) async {
          if (_isClosed) return;
          if (resetPasswordCancelToken?.isCancelled ?? true) return;
          context.l10n.passChanged.showSuccessAlert();
          AppNavigationService.pop(context);
          isShowLoader.value = false;
        },
        error: (exception) {
          isShowLoader.value = false;
          if (_isClosed) return;
          if (resetPasswordCancelToken?.isCancelled ?? true) return;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoader.value = false;
      if (_isClosed) return;
      if (resetPasswordCancelToken?.isCancelled ?? true) return;
      'resetPasswordAPIcall error: $e'.logE;
      '$e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;

    // Cancel all tokens
    resetPasswordCancelToken?.cancel();

    // Dispose all controllers
    oldPasswordController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();

    // Dispose all ValueNotifiers
    isOldPasswordShow.dispose();
    isPasswordShow.dispose();
    isConfirmPasswordShow.dispose();
    isShowLoader.dispose();

    super.dispose();
  }
}
