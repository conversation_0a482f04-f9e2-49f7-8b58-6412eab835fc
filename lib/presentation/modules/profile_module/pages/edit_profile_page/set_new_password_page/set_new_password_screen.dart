import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/set_new_password_page/provider/set_new_password_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/utils/validators/password_match_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class SetNewPasswordScreen extends StatelessWidget {
  const SetNewPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider(
      create: (context) => SetNewPasswordProvider(),
      child: Builder(
        builder: (formContext) {
          final setNewPasswordProvider = formContext
              .read<SetNewPasswordProvider>();
          return Form(
            key:setNewPasswordProvider.newPasswordKey ,
            child: Scaffold(
              appBar: const CustomAppBar(title: ''),
              backgroundColor: AppColors.ffF8F9FA,
              body: ValueListenableBuilder(
                valueListenable: setNewPasswordProvider.isShowLoader,
                builder: (context, value, child) =>
                    AppLoader(isShowLoader: value, child: child!),
                child: GestureDetector(
                  onTap: AppCommonFunctions.closeKeyboard,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(AppSize.appPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: AppSize.h8,
                      children: [
                        Text(
                          l10n.set_new_password,
                          style: context.textTheme.titleLarge?.copyWith(
                            fontSize: AppSize.sp24,
                          ),
                        ),
                        Text(
                          l10n.go_ahead_and_set_a_new_password,
                          style: context.textTheme.bodySmall?.copyWith(
                            fontSize: AppSize.sp16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        AppPadding.symmetric(
                          vertical: AppSize.h32,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              //* Password
                              ValueListenableBuilder(
                                valueListenable:
                                    setNewPasswordProvider.isOldPasswordShow,
                                builder: (context, isOldPasswordShow, _) {
                                  return AppTextFormField(
                                    hintText: l10n.enter_your_password,
                                    fillColor: AppColors.ffF8F9FA,
                                    title: l10n.old_password,
                                    titleColor: AppColors.black,
                                    textAction: TextInputAction.next,
                                    obscureText: !setNewPasswordProvider
                                        .isOldPasswordShow
                                        .value,
                                    controller: setNewPasswordProvider
                                        .oldPasswordController,
                                    validator: (p0) =>
                                        passwordValidator().call(p0),
                                    suffixIcon: GestureDetector(
                                      onTap: () {
                                        setNewPasswordProvider
                                            .isOldPasswordShow
                                            .value = !setNewPasswordProvider
                                            .isOldPasswordShow
                                            .value;
                                      },
                                      child: Icon(
                                        isOldPasswordShow
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                        color: AppColors.ff6C757D,
                                      ),
                                    ),
                                  );
                                },
                              ),

                              //* Password
                              AppPadding.symmetric(
                                vertical: AppSize.h16,
                                child: ValueListenableBuilder(
                                  valueListenable:
                                      setNewPasswordProvider.isPasswordShow,
                                  builder: (context, isPasswordShow, _) {
                                    return AppTextFormField(
                                      hintText: l10n.enter_your_password,
                                      fillColor: AppColors.ffF8F9FA,
                                      title: l10n.new_password,
                                      titleColor: AppColors.black,
                                      textAction: TextInputAction.next,
                                      obscureText: !setNewPasswordProvider
                                          .isPasswordShow
                                          .value,
                                      controller: setNewPasswordProvider
                                          .passwordController,
                                      validator: (p0) =>
                                          passwordValidator().call(p0),
                                      suffixIcon: GestureDetector(
                                        onTap: () {
                                          setNewPasswordProvider
                                              .isPasswordShow
                                              .value = !setNewPasswordProvider
                                              .isPasswordShow
                                              .value;
                                        },
                                        child: Icon(
                                          isPasswordShow
                                              ? Icons.visibility_outlined
                                              : Icons.visibility_off_outlined,
                                          color: AppColors.ff6C757D,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),

                              //* Confirm Password
                              ValueListenableBuilder(
                                valueListenable: setNewPasswordProvider
                                    .isConfirmPasswordShow,
                                builder: (context, isConfirmPasswordShow, _) {
                                  return AppTextFormField(
                                    hintText: l10n.enter_your_password,
                                    fillColor: AppColors.ffF8F9FA,
                                    title: l10n.confirm_password,
                                    titleColor: AppColors.black,
                                    obscureText: !setNewPasswordProvider
                                        .isConfirmPasswordShow
                                        .value,
                                    controller: setNewPasswordProvider
                                        .confirmPasswordController,
                                    validator: (value) =>
                                        ConfirmPasswordValidator(
                                          errorText: l10n
                                              .please_enter_confirm_password,
                                          password: setNewPasswordProvider
                                              .passwordController
                                              .text
                                        ).call(value),
                                    suffixIcon: GestureDetector(
                                      onTap: () {
                                        setNewPasswordProvider
                                            .isConfirmPasswordShow
                                            .value = !setNewPasswordProvider
                                            .isConfirmPasswordShow
                                            .value;
                                      },
                                      child: Icon(
                                        isConfirmPasswordShow
                                            ? Icons.visibility_outlined
                                            : Icons.visibility_off_outlined,
                                        color: AppColors.ff6C757D,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              bottomNavigationBar: AppButton(
                text: l10n.change_password,
                onPressed: () {
                  final form = setNewPasswordProvider.newPasswordKey.currentState;
                  if (form?.validate()??false) {
                    if (setNewPasswordProvider
                        .confirmPasswordController
                        .text ==
                        setNewPasswordProvider
                            .oldPasswordController.text) {
                      l10n.newPasswordAndOldPasswordCannotBeSame.showErrorAlert();
                    } else {
                      setNewPasswordProvider
                          .resetPasswordAPIcall(
                        context: context,
                      );
                    }
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
