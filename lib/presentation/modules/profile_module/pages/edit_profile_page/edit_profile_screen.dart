import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/models/edit_profile_param.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/provider/edit_profile_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class EditProfileScreen extends StatelessWidget {
  const EditProfileScreen({super.key, required this.editProfileParam});
  final EditProfileParam editProfileParam;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider(
      create: (context) => EditProfileProvider(editProfileParam),
      child: Builder(
        builder: (context) {
          final editProfileProvider = context.read<EditProfileProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(title: l10n.edit_details),
            body: ValueListenableBuilder(
              valueListenable: editProfileProvider.isShowLoader,
              builder: (context, isLoad, child) =>
                  AppLoader(isShowLoader: isLoad, child: child!),
              child: GestureDetector(
                onTap: AppCommonFunctions.closeKeyboard,
                child: Form(
                  key: editProfileProvider.formKey,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(AppSize.appPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppTextFormField(
                          hintText: l10n.enter_your_name,
                          fillColor: AppColors.ffF8F9FA,
                          title: l10n.name,
                          titleColor: AppColors.black,
                          textAction: TextInputAction.next,
                          controller: editProfileProvider.nameController,
                          validator: (p0) => nameValidator().call(p0),
                        ),

                        //* Email
                        AppPadding(
                          top: AppSize.h16,
                          bottom: AppSize.h16,
                          child: AppTextFormField(
                            controller: editProfileProvider.emailController,
                            hintText: l10n.enter_your_email,
                            title: l10n.email,
                            titleColor: AppColors.black,
                            textAction: TextInputAction.next,
                            keyboardType: TextInputType.emailAddress,
                            fillColor: AppColors.ffE6E6E6,
                            readOnly: true,
                            textColor: AppColors.ffADB5BD,
                            borderSide: BorderSide(
                              width: AppSize.w1,
                              color: AppColors.ffADB5BD,
                            ),
                          ),
                        ),

                        AppTextFormField(
                          controller: editProfileProvider.passwordController,
                          hintText: '*******',
                          title: l10n.password,
                          titleColor: AppColors.black,
                          fillColor: AppColors.ffE6E6E6,
                          readOnly: true,
                          obscureText: true,
                          textColor: AppColors.ffADB5BD,
                          borderSide: BorderSide(
                            width: AppSize.w1,
                            color: AppColors.ffADB5BD,
                          ),
                        ),
                        Gap(AppSize.h8),
                        Align(
                          alignment: Alignment.centerRight,
                          child: InkWell(
                            onTap: () {
                              AppNavigationService.pushNamed(
                                context,
                                AppRoutes.profileSetNewPasswordScreen,

                                //
                                // SetNewPasswordScreen(
                                //   email: editProfileProvider
                                //       .emailController.text
                                //       .trim(),
                                // ),
                              );
                            },
                            child: Text(
                              l10n.change_password,
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w500,
                                fontSize: AppSize.sp12,
                                color: AppColors.ff0087C7,
                                decorationColor: AppColors.ff0087C7,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            bottomNavigationBar: AppButton(
              text: l10n.save,
              onPressed: () {
                if (editProfileProvider.formKey.currentState!.validate()) {
                  editProfileProvider.updateUserData(
                    context: context,
                    onEditCallBack: editProfileParam.onEditCallBack,
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }
}
