//

// import 'package:flutter/material.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/past_purchase_page/model/past_purchase_model.dart';
// import 'package:transport_match/utils/logger.dart';

// /// PastPurchaseProvider
// class PastPurchaseProvider extends ChangeNotifier {
//   /// Flag to check if provider is closed
//   bool _isClosed = false;

//   /// Notify listeners if not closed
//   void notify() {
//     if (_isClosed) return;
//     try {
//       notifyListeners();
//     } catch (e) {
//       'notify error: $e'.logE;
//     }
//   }

//   /// List of past purchase models
//   List<PastPurchaseModel> data = [
//     PastPurchaseModel(id: 0),
//     PastPurchaseModel(id: 1),
//     PastPurchaseModel(id: 2),
//     PastPurchaseModel(id: 3),
//   ];

//   /// Update visibility of vehicle info
//   /// [id] is the purchase ID
//   /// [isVisible] flag to set visibility
//   void updateVisibility({required int id, bool isVisible = false}) {
//     if (_isClosed) return;

//     try {
//       final index = data.indexWhere((element) => element.id == id);
//       if (index >= 0) {
//         data[index].isVehicleInfoVisible = isVisible;
//         if (!data[index].isVehicleInfoVisible) data[index].currentPage = 0;
//       }
//       notify();
//     } catch (e) {
//       'updateVisibility error: $e'.logE;
//     }
//   }

//   /// Update current page of vehicle info
//   /// [id] is the purchase ID
//   /// [page] is the page number
//   void updatePage({required int id, int page = 0}) {
//     if (_isClosed) return;

//     try {
//       final index = data.indexWhere((element) => element.id == id);
//       if (index >= 0) data[index].currentPage = page;
//       notify();
//     } catch (e) {
//       'updatePage error: $e'.logE;
//     }
//   }

//   @override
//   void dispose() {
//     _isClosed = true;
//     super.dispose();
//   }
// }
