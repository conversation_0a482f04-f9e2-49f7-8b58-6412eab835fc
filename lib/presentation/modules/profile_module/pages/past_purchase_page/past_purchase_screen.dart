// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:provider/provider.dart';
// import 'package:transport_match/extensions/ext_build_context.dart';
// import 'package:transport_match/l10n/l10n.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/past_purchase_page/provider/past_purchase_provider.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/past_purchase_page/widgets/vehicles_info_content_widget.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/past_purchase_page/widgets/vehicles_info_widget.dart';
// import 'package:transport_match/utils/app_colors.dart';
// import 'package:transport_match/utils/app_common_functions.dart';
// import 'package:transport_match/utils/app_size.dart';
// import 'package:transport_match/utils/gen/assets.gen.dart';
// import 'package:transport_match/widgets/app_padding.dart';
// import 'package:transport_match/widgets/custom_app_bar.dart';
// import 'package:transport_match/widgets/dashed_divider.dart';

// /// PastPurchaseScreen
// class PastPurchaseScreen extends StatelessWidget {
//   /// PastPurchaseScreen
//   const PastPurchaseScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final l10n = context.l10n;
//     return ChangeNotifierProvider(
//       create: (context) => PastPurchaseProvider(),
//       child: Consumer<PastPurchaseProvider>(
//         builder: (context, pastPurchaseProvider, child) {
//           return GestureDetector(
//             onTap: AppCommonFunctions.closeKeyboard,
//             child: Scaffold(
//               appBar: CustomAppBar(title: l10n.past_purchase),
//               backgroundColor: AppColors.ffF8F9FA,
//               body: SafeArea(
//                 child: AppPadding.symmetric(
//                   horizontal: AppSize.appPadding,
//                   child: ListView.separated(
//                     itemCount: pastPurchaseProvider.data.length,
//                     separatorBuilder: (context, index) => Gap(AppSize.h16),
//                     itemBuilder: (context, index) {
//                       final data = pastPurchaseProvider.data[index];
//                       return Container(
//                         padding: EdgeInsets.symmetric(vertical: AppSize.h16),
//                         decoration: BoxDecoration(
//                           color: AppColors.white,
//                           borderRadius: BorderRadius.circular(AppSize.r4),
//                         ),
//                         child: Column(
//                           children: [
//                             AppPadding.symmetric(
//                               horizontal: AppSize.h16,
//                               child: Column(
//                                 children: [
//                                   Row(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       Expanded(
//                                         flex: 2,
//                                         child: Column(
//                                           crossAxisAlignment:
//                                               CrossAxisAlignment.start,
//                                           children: [
//                                             Text(
//                                               l10n.transporter,
//                                               style: context
//                                                   .textTheme.titleLarge
//                                                   ?.copyWith(
//                                                 color: AppColors.ffADB5BD,
//                                                 fontWeight: FontWeight.w500,
//                                                 fontSize: AppSize.sp12,
//                                               ),
//                                             ),
//                                             Text(
//                                               context.l10n.inTrackTransport,
//                                               style: context
//                                                   .textTheme.titleMedium
//                                                   ?.copyWith(
//                                                 fontSize: AppSize.sp16,
//                                               ),
//                                             ),
//                                             Text.rich(
//                                               TextSpan(
//                                                 text:
//                                                     '${l10n.no_of_vehicles}: ',
//                                                 children: [
//                                                   TextSpan(
//                                                     text: '2',
//                                                     style: context
//                                                         .textTheme.titleLarge
//                                                         ?.copyWith(
//                                                       color: AppColors.ff343A40,
//                                                       fontWeight:
//                                                           FontWeight.w700,
//                                                       fontSize: AppSize.sp12,
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                               style: context
//                                                   .textTheme.titleLarge
//                                                   ?.copyWith(
//                                                 color: AppColors.ffADB5BD,
//                                                 fontWeight: FontWeight.w500,
//                                                 fontSize: AppSize.sp12,
//                                               ),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                       Gap(AppSize.w16),
//                                       Expanded(
//                                         child: Column(
//                                           crossAxisAlignment:
//                                               CrossAxisAlignment.end,
//                                           children: [
//                                             Text(
//                                               l10n.total_trip_cost,
//                                               textAlign: TextAlign.right,
//                                               style: context
//                                                   .textTheme.titleLarge
//                                                   ?.copyWith(
//                                                 color: AppColors.ffADB5BD,
//                                                 fontWeight: FontWeight.w500,
//                                                 fontSize: AppSize.sp12,
//                                               ),
//                                             ),
//                                             Text(
//                                               r'$ 20,000',
//                                               textAlign: TextAlign.right,
//                                               style: context
//                                                   .textTheme.titleMedium
//                                                   ?.copyWith(
//                                                 fontSize: AppSize.sp16,
//                                                 fontWeight: FontWeight.w600,
//                                                 color: AppColors.secondaryColor,
//                                               ),
//                                             ),
//                                             Text(
//                                               r'$10,000/Car',
//                                               textAlign: TextAlign.right,
//                                               style: context
//                                                   .textTheme.titleLarge
//                                                   ?.copyWith(
//                                                 color: AppColors.ffADB5BD,
//                                                 fontWeight: FontWeight.w500,
//                                                 fontSize: AppSize.sp12,
//                                               ),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   Gap(AppSize.h16),
//                                   Align(
//                                     alignment: Alignment.centerLeft,
//                                     child: VehiclesInfoContentWidget(
//                                       heading: l10n.equipment_type,
//                                       subText: 'Car Hauler',
//                                       handleOverflow: false,
//                                     ),
//                                   ),
//                                   Gap(AppSize.h16),
//                                   Row(
//                                     children: [
//                                       Expanded(
//                                         child: Column(
//                                           crossAxisAlignment:
//                                               CrossAxisAlignment.start,
//                                           children: [
//                                             Text(
//                                               'Ofay Mesa',
//                                               maxLines: 3,
//                                               style: context
//                                                   .textTheme.bodyMedium
//                                                   ?.copyWith(
//                                                 fontSize: AppSize.sp14,
//                                                 overflow: TextOverflow.ellipsis,
//                                               ),
//                                             ),
//                                             Row(
//                                               children: [
//                                                 AppAssets.iconsCalendar.image(
//                                                   height: AppSize.h18,
//                                                   width: AppSize.h18,
//                                                 ),
//                                                 Gap(AppSize.w4),
//                                                 Flexible(
//                                                   child: Text(
//                                                     'Nov 21',
//                                                     maxLines: 3,
//                                                     style: context
//                                                         .textTheme.bodyMedium
//                                                         ?.copyWith(
//                                                       fontSize: AppSize.sp12,
//                                                       fontWeight:
//                                                           FontWeight.w500,
//                                                       color:
//                                                           AppColors.greyColor,
//                                                       overflow:
//                                                           TextOverflow.ellipsis,
//                                                     ),
//                                                   ),
//                                                 ),
//                                               ],
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                       Gap(AppSize.w16),
//                                       AppAssets.iconsPin.image(
//                                         height: AppSize.h18,
//                                         width: AppSize.h18,
//                                       ),
//                                       Gap(AppSize.w4),
//                                       const Expanded(
//                                         child: DashedDivider(),
//                                       ),
//                                       Gap(AppSize.w4),
//                                       AppAssets.iconsLocation.image(
//                                         height: AppSize.h16,
//                                         width: AppSize.h16,
//                                       ),
//                                       Gap(AppSize.w16),
//                                       Expanded(
//                                         child: Column(
//                                           crossAxisAlignment:
//                                               CrossAxisAlignment.end,
//                                           children: [
//                                             Text(
//                                               'California',
//                                               maxLines: 3,
//                                               style: context
//                                                   .textTheme.bodyMedium
//                                                   ?.copyWith(
//                                                 fontSize: AppSize.sp14,
//                                                 overflow: TextOverflow.ellipsis,
//                                               ),
//                                             ),
//                                             Row(
//                                               mainAxisAlignment:
//                                                   MainAxisAlignment.end,
//                                               children: [
//                                                 AppAssets.iconsCalendar.image(
//                                                   height: AppSize.h16,
//                                                   width: AppSize.h16,
//                                                 ),
//                                                 Gap(AppSize.w4),
//                                                 Flexible(
//                                                   child: Text(
//                                                     'Nov 28',
//                                                     maxLines: 3,
//                                                     style: context
//                                                         .textTheme.bodyMedium
//                                                         ?.copyWith(
//                                                       fontSize: AppSize.sp12,
//                                                       fontWeight:
//                                                           FontWeight.w500,
//                                                       color:
//                                                           AppColors.greyColor,
//                                                       overflow:
//                                                           TextOverflow.ellipsis,
//                                                     ),
//                                                   ),
//                                                 ),
//                                               ],
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   Gap(AppSize.h16),
//                                   if (data.isVehicleInfoVisible)
//                                     Row(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.spaceBetween,
//                                       spacing: AppSize.w4,
//                                       children: [
//                                         Text(
//                                           l10n.vehicles_info,
//                                           style: context.textTheme.bodyMedium
//                                               ?.copyWith(
//                                             fontSize: AppSize.sp16,
//                                             fontWeight: FontWeight.w700,
//                                             color: AppColors.ff212529,
//                                           ),
//                                         ),
//                                         Text(
//                                           '${data.currentPage + 1}/5',
//                                           style: context.textTheme.bodyMedium
//                                               ?.copyWith(
//                                             fontSize: AppSize.sp14,
//                                             fontWeight: FontWeight.w400,
//                                             color: AppColors.ff212529,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                 ],
//                               ),
//                             ),
//                             if (data.isVehicleInfoVisible) ...[
//                               Gap(AppSize.h4),
//                               SizedBox(
//                                 height: AppSize.h140,
//                                 child: PageView.builder(
//                                   itemCount: 5,
//                                   onPageChanged: (value) {
//                                     pastPurchaseProvider.updatePage(
//                                       id: data.id,
//                                       page: value,
//                                     );
//                                   },
//                                   itemBuilder: (context, index) {
//                                     return VehicleInfoWidget(
//                                       onDetailTap: () {},
//                                       info: VehicleInfo(
//                                         carBrand: 'Mini',
//                                         carModel: 'Country man S',
//                                         carSerial: '1G4AJ47A0BH191698',
//                                         carYear: '2020',
//                                       ),
//                                     );
//                                   },
//                                 ),
//                               ),
//                               Gap(AppSize.h16),
//                               SizedBox(
//                                 height: AppSize.h8,
//                                 child: ListView.separated(
//                                   scrollDirection: Axis.horizontal,
//                                   physics: const NeverScrollableScrollPhysics(),
//                                   shrinkWrap: true,
//                                   itemCount: 5,
//                                   itemBuilder: (context, index) {
//                                     return CircleAvatar(
//                                       radius: AppSize.r4,
//                                       backgroundColor: index == data.currentPage
//                                           ? AppColors.primaryColor
//                                           : AppColors.ffCED4DA,
//                                     );
//                                   },
//                                   separatorBuilder: (context, index) =>
//                                       Gap(AppSize.w8),
//                                 ),
//                               ),
//                             ],
//                             Gap(AppSize.h16),
//                             InkWell(
//                               onTap: () {
//                                 pastPurchaseProvider.updateVisibility(
//                                   id: data.id,
//                                   isVisible: !data.isVehicleInfoVisible,
//                                 );
//                               },
//                               child: Row(
//                                 mainAxisSize: MainAxisSize.min,
//                                 children: [
//                                   Icon(
//                                     data.isVehicleInfoVisible
//                                         ? Icons.close
//                                         : Icons.add,
//                                     color: AppColors.primaryColor,
//                                     size: AppSize.h18,
//                                   ),
//                                   Gap(AppSize.w4),
//                                   Text(
//                                     data.isVehicleInfoVisible
//                                         ? l10n.close
//                                         : l10n.view_details,
//                                     style:
//                                         context.textTheme.bodyMedium?.copyWith(
//                                       fontSize: AppSize.sp14,
//                                       fontWeight: FontWeight.w600,
//                                       color: AppColors.primaryColor,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                       );
//                     },
//                   ),
//                 ),
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
