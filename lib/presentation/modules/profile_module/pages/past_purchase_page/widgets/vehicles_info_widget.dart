//

// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:transport_match/extensions/ext_build_context.dart';
// import 'package:transport_match/l10n/l10n.dart';
// import 'package:transport_match/presentation/modules/profile_module/pages/past_purchase_page/widgets/vehicles_info_content_widget.dart';
// import 'package:transport_match/utils/app_colors.dart';
// import 'package:transport_match/utils/app_size.dart';

// class VehicleInfo {
//   VehicleInfo({
//     required this.carBrand,
//     required this.carModel,
//     required this.carSerial,
//     required this.carYear,
//   });
//   final String carBrand;
//   final String carModel;
//   final String carSerial;
//   final String carYear;
// }

// class VehicleInfoWidget extends StatelessWidget {
//   const VehicleInfoWidget({
//     required this.info,
//     super.key,
//     this.onDetailTap,
//   });

//   final VehicleInfo info;
//   final VoidCallback? onDetailTap;

//   @override
//   Widget build(BuildContext context) {
//     final l10n = context.l10n;
//     return Container(
//       padding: EdgeInsets.all(AppSize.h12),
//       margin: EdgeInsets.symmetric(horizontal: AppSize.w16),
//       decoration: BoxDecoration(
//         color: AppColors.white,
//         borderRadius: BorderRadius.circular(AppSize.r4),
//         border: Border.all(color: AppColors.ffDEE2E6),
//       ),
//       child: Column(
//         children: [
//           Align(
//             alignment: Alignment.centerRight,
//             child: InkWell(
//               onTap: onDetailTap,
//               child: Text(
//                 l10n.details,
//                 style: context.textTheme.titleLarge?.copyWith(
//                   color: AppColors.ff0087C7,
//                   fontWeight: FontWeight.w600,
//                   fontSize: AppSize.sp14,
//                 ),
//               ),
//             ),
//           ),
//           Gap(AppSize.h8),
//           Flexible(
//             child: Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Expanded(
//                   child: VehiclesInfoContentWidget(
//                     heading: l10n.car_brand,
//                     subText: info.carBrand,
//                   ),
//                 ),
//                 Gap(AppSize.w6),
//                 Expanded(
//                   child: VehiclesInfoContentWidget(
//                     heading: l10n.car_model,
//                     subText: info.carModel,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           Gap(AppSize.h8),
//           Flexible(
//             child: Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Expanded(
//                   flex: 3,
//                   child: VehiclesInfoContentWidget(
//                     heading: l10n.car_serial,
//                     subText: info.carSerial,
//                   ),
//                 ),
//                 Gap(AppSize.w6),
//                 Expanded(
//                   child: VehiclesInfoContentWidget(
//                     heading: l10n.car_year,
//                     subText: info.carYear,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
