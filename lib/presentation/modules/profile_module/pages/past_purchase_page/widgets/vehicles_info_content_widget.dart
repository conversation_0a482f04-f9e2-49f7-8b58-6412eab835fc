//

// import 'package:flutter/material.dart';
// import 'package:transport_match/extensions/ext_build_context.dart';
// import 'package:transport_match/utils/app_colors.dart';
// import 'package:transport_match/utils/app_size.dart';

// /// VehiclesInfoContentWidget
// class VehiclesInfoContentWidget extends StatelessWidget {
//   /// VehiclesInfoContentWidget
//   const VehiclesInfoContentWidget({
//     required this.heading,
//     required this.subText,
//     this.handleOverflow = true,
//     super.key,
//   });

//   final String heading;
//   final String subText;
//   final bool handleOverflow;
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Flexible(
//           child: Text(
//             heading,
//             style: context.textTheme.titleLarge?.copyWith(
//               color: AppColors.ffADB5BD,
//               fontWeight: FontWeight.w500,
//               fontSize: AppSize.sp12,
//               overflow: handleOverflow ? TextOverflow.ellipsis : null,
//             ),
//           ),
//         ),
//         Flexible(
//           child: Text(
//             subText,
//             style: context.textTheme.titleMedium?.copyWith(
//               overflow: handleOverflow ? TextOverflow.ellipsis : null,
//               fontSize: AppSize.sp16,
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
