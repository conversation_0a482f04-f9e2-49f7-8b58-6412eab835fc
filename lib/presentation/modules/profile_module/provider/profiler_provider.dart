import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/verify_otp_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/logger.dart';

class ProfilerProvider extends ChangeNotifier {
  ProfilerProvider() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) => getUserData(),
    );
  }

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  UserModel? user = Injector.instance<AppDB>().userModel?.user;

  ValueNotifier<bool> isShowLoader = ValueNotifier(false);

  /// Get user data from API
  CancelToken? getUserCancelToken;

  Future<void> getUserData() async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      getUserCancelToken?.cancel();
      getUserCancelToken = CancelToken();
      final request = ApiRequest(
        path:
            '${EndPoints.getUserInfo}${Injector.instance<AppDB>().userModel?.user?.id}/',
        cancelToken: getUserCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().updateUserInfo(request);

      if (_isClosed) return;

      await res.when(
        success: (data) async {
          if (_isClosed || (getUserCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          Injector.instance<AppDB>().userModel = data;
          user = data.user;
          notify();
        },
        error: (exception) async {
          if (_isClosed || (getUserCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getUserCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'getUserData error: $e'.logE;
    }
  }

  /// Logout user from the app
  CancelToken? logoutToken;
  Future<void> logout() async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      logoutToken?.cancel();
      logoutToken = CancelToken();
      final deviceId = await AppCommonFunctions.getDeviceId();
      if (_isClosed) return;

      Map<String, dynamic> data;
      data = {
        ApiKeys.deviceId: deviceId,
        ApiKeys.refreshToken: Injector.instance<AppDB>().refreshToken,
      };
      final request = ApiRequest(
        path: EndPoints.logout,
        data: data,
        cancelToken: logoutToken,
      );
      final res = await Injector.instance<AccountRepository>().logout(request);

      if (_isClosed) return;

      await Injector.instance<AppDB>().logoutUser();
      await AppNavigationService.pushAndRemoveAllPreviousRoute(
        rootNavKey.currentContext!,
        AppRoutes.authBase,
        isBaseRoute: true,
      );
      await res.when(
        success: (data) async {
          if (_isClosed || (logoutToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
        },
        error: (exception) async {
          if (_isClosed || (logoutToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (logoutToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'logout error: $e'.logE;
    }
  }

  /// Delete user account
  CancelToken? deleteAccountToken;
  Future<void> deleteAccount() async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      deleteAccountToken?.cancel();
      deleteAccountToken = CancelToken();
      final request = ApiRequest(
        path:
            '${EndPoints.deleteAccount}${Injector.instance<AppDB>().userModel?.user?.id}/delete/',
        cancelToken: deleteAccountToken,
      );
      final res =
          await Injector.instance<AccountRepository>().deleteAccount(request);

      if (_isClosed) return;

      await res.when(
        success: (data) async {
          if (_isClosed || (deleteAccountToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          await Injector.instance<AppDB>().logoutUser();
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            rootNavKey.currentContext!,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        },
        error: (exception) async {
          if (_isClosed || (deleteAccountToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (deleteAccountToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      'deleteAccount error: $e'.logE;
    }
  }

  /// Get Stripe customer portal URL
  /// [context] is the BuildContext
  CancelToken? stripeCustomerPortalUrlToken;
  Future<void> stripeCustomerPortalUrl(BuildContext context) async {
    if (_isClosed) return;

    try {
      isShowLoader.value = true;
      stripeCustomerPortalUrlToken?.cancel();
      stripeCustomerPortalUrlToken = CancelToken();
      final request = ApiRequest(
        path: EndPoints.stripeCustomerPortalUrl,
        cancelToken: stripeCustomerPortalUrlToken,
      );
      final res = await Injector.instance<AccountRepository>()
          .stripeCustomerPortalUrl(request);

      if (_isClosed) return;

      res.when(
        success: (data) {
          if (_isClosed ||
              (stripeCustomerPortalUrlToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          final url = data['stripe_customer_portal_link']['url'] as String?;
          if (url.isNotEmptyAndNotNull) {
            context.l10n.pleaseWaitWeAreRedirectingStripe.showSuccessAlert();
            AppNavigationService.pushNamed(
              context,
              AppRoutes.commonWebViewScreen,
              extra: CommonWebViewParams(
                url: url ?? '',
                context: context,
              ),
            );
            // Navigator.push(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) => CommonWebView(
            //       url: url ?? '',
            //     ),
            //   ),
            // );
          }
        },
        error: (exception) {
          if (_isClosed ||
              (stripeCustomerPortalUrlToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (stripeCustomerPortalUrlToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader.value = false;
      'stripeCustomerPortalUrl error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;

    // Cancel all tokens
    getUserCancelToken?.cancel();
    logoutToken?.cancel();
    deleteAccountToken?.cancel();
    stripeCustomerPortalUrlToken?.cancel();

    // Dispose all ValueNotifiers
    isShowLoader.dispose();

    super.dispose();
  }
}
