import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// Notifications Widgets
class NotificationsWidgets extends StatelessWidget {
  /// Constructor
  const NotificationsWidgets({
    super.key,
    required this.icon,
    required this.time,
    required this.textNormal,
    required this.textBold,
  });

  /// for icon widget
  final Widget icon;

  /// for time
  final String time;

  /// for textSpan normal
  final String textNormal;

  /// for textSpan Bold
  final String textBold;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DecoratedBox(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(AppSize.r10),
            ),
            child: Padding(
              padding: EdgeInsets.all(AppSize.r10),
              child: icon,
            ),
          ),
          Flexible(
            fit: FlexFit.tight,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    textBold,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: AppSize.h2),
                    child: ReadMoreText(
                      textNormal,
                      style: TextStyle(fontSize: AppSize.sp13),
                      trimMode: TrimMode.Line,
                      trimLines: 3,
                      lessStyle: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.primaryColor,
                      ),
                      trimCollapsedText: context.l10n.readMore,
                      trimExpandedText: ' ${context.l10n.readLess}',
                      moreStyle: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                  // Padding(
                  //   padding: EdgeInsets.symmetric(vertical: AppSize.h2),
                  //   child: Text(
                  //     textNormal,
                  //     style: TextStyle(fontSize: AppSize.sp13),
                  //   ),
                  // ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      time,
                      style: TextStyle(fontSize: AppSize.h12),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
