import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/notifications_module/models/notification_model.dart';
import 'package:transport_match/presentation/modules/notifications_module/provider/notification_provider.dart';
import 'package:transport_match/presentation/modules/notifications_module/widgets/notifications_widgets.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Notifications screen ui
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => NotificationProvider(),
      child: Builder(
        builder: (context) {
          final notificationProvider = context.read<NotificationProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              canPop: false,
              title: context.l10n.allNotifications,
              horizontalPadding: AppSize.w10,
            ),
            body:
                Selector<
                  NotificationProvider,
                  (bool, List<NotificationModel>, String?)
                >(
                  selector: (p0, notificationProvider) => (
                    notificationProvider.isShowLoader,
                    notificationProvider.notificationList,
                    notificationProvider.nextUrl,
                  ),
                  builder: (context, value, child) => AppLoader(
                    isShowLoader: value.$1,
                    child: EasyRefresh(
                      triggerAxis: Axis.vertical,
                      header: AppCommonFunctions.getLoadingHeader(),
                      footer: AppCommonFunctions.getLoadingFooter(),
                      controller: notificationProvider.refreshController,
                      onRefresh: () async =>
                          notificationProvider.getNotification(),
                      onLoad: () async => value.$3.isNotEmptyAndNotNull
                          ? notificationProvider.getNotification(
                              isPagination: true,
                            )
                          : null,
                      child: !value.$1 && value.$2.isEmpty
                          ? ListView(
                              children: [
                                SizedBox(
                                  height:
                                      MediaQuery.of(context).size.height * 0.75,
                                  child: Center(
                                    child: Text(
                                      context.l10n.noNotificationsFound,
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : ListView.builder(
                              itemCount: value.$2.length,
                              padding: EdgeInsets.symmetric(
                                horizontal: AppSize.appPadding,
                                vertical: AppSize.h16,
                              ),
                              itemBuilder: (context, index) {
                                final notification = value.$2[index];
                                final createdAt = notification.createdAt;
                                final currentGroupLabel = notificationProvider
                                    .getGroupLabel(createdAt!);

                                var showGroupHeader = false;
                                if (index == 0) {
                                  showGroupHeader = true;
                                } else {
                                  final previous = value.$2[index - 1];
                                  final prevGroupLabel = notificationProvider
                                      .getGroupLabel(
                                        previous.createdAt ?? DateTime.now(),
                                      );
                                  showGroupHeader =
                                      prevGroupLabel != currentGroupLabel;
                                }

                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (showGroupHeader)
                                      Padding(
                                        padding: EdgeInsets.only(
                                          bottom: AppSize.h10,
                                        ),
                                        child: Text(
                                          currentGroupLabel,
                                          style: context.textTheme.titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w400,
                                                fontSize: AppSize.sp16,
                                                color: AppColors.ff6C757D,
                                              ),
                                        ),
                                      ),
                                    InkWell(
                                      onTap:
                                          notification.relatedObjectData != null
                                          ? () => notificationProvider
                                                .onNotificationTap(
                                                  notificationData: notification
                                                      .relatedObjectData!,
                                                  context: context,
                                                  notificationType:
                                                      notification
                                                          .notificationType ??
                                                      NotificationType
                                                          .TRIP
                                                          .name,
                                                )
                                          : null,
                                      child: NotificationsWidgets(
                                        icon:
                                            (notification.notificationType ==
                                                        NotificationType
                                                            .TRIP
                                                            .name
                                                    ? AppAssets.iconsTripColor
                                                    : notification.notificationType ==
                                                              NotificationType
                                                                  .BOOKING
                                                                  .name ||
                                                          notification
                                                                  .notificationType ==
                                                              NotificationType
                                                                  .BOOKING_DETAIL
                                                                  .name
                                                    ? AppAssets.iconsBooking
                                                    : AppAssets.iconsDollar)
                                                .image(
                                                  width: AppSize.w24,
                                                  height: AppSize.h24,
                                                  color: AppColors.primaryColor,
                                                ),
                                        textNormal:
                                            notification.description ?? '',
                                        textBold: notification.title ?? '',
                                        time:
                                            AppCommonFunctions.timeAgoConverter(
                                              dateString: createdAt
                                                  .toUtc()
                                                  .toIso8601String(),
                                            ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                    ),
                  ),
                ),
          );
        },
      ),
    );
  }
}
