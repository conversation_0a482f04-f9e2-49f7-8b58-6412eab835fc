import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/verify_otp_model.dart';

class SignUpAndSignInModel {
  const SignUpAndSignInModel({
    this.user,
    this.access,
    this.refresh,
  });

  factory SignUpAndSignInModel.fromJson(Map<String, dynamic> json) {
    return SignUpAndSignInModel(
      user: json['user'] == null
          ? null
          : UserModel.fromJson(Map<String, dynamic>.from(json['user'] as Map)),
      access: json['access']?.toString(),
      refresh: json['refresh']?.toString(),
    );
  }

  final UserModel? user;
  final String? access;
  final String? refresh;

  Map<String, dynamic> toJson() => {
        'user': user?.toJson(),
        'access': access,
        'refresh': refresh,
      };
}

// THIS IS FOR PROVIDER ONLY, NOT FOR USER
// class UserDetailData {
//   const UserDetailData({
//     this.id,
//     this.stripeAccountId,
//     this.isAccountActivated,
//     this.companyName,
//     this.commercialName,
//     this.taxId,
//     this.webPage,
//     this.accountVerificationStatus,
//     this.isProfileCompleted,
//   });
// class UserDetailData extends Immutable {
//   const UserDetailData({
//     this.id,
//     this.stripeAccountId,
//     this.isAccountActivated,
//     this.companyName,
//     this.commercialName,
//     this.taxId,
//     this.webPage,
//     this.accountVerificationStatus,
//     this.isProfileCompleted,
//   });
//
//   factory UserDetailData.fromJson(Map<String, dynamic> json) {
//     return UserDetailData(
//       id: json['id'] == null ? -1 : int.tryParse(json['id'].toString()),
//       stripeAccountId: json['stripe_account_id'] == null
//           ? ''
//           : json['stripe_account_id'].toString(),
//       isAccountActivated: json['is_account_activated'] == null
//           ? false
//           : bool.tryParse(json['is_account_activated'].toString()),
//       companyName:
//           json['company_name'] == null ? '' : json['company_name'].toString(),
//       commercialName: json['commercial_name'] == null
//           ? ''
//           : json['commercial_name'].toString(),
//       taxId: json['tax_id'] == null ? '' : json['tax_id'].toString(),
//       webPage: json['web_page'] == null ? '' : json['web_page'].toString(),
//       accountVerificationStatus: json['account_verification_status'] == null
//           ? ''
//           : json['account_verification_status'].toString(),
//       isProfileCompleted: json['is_profile_completed'] == null
//           ? false
//           : bool.tryParse(json['is_profile_completed'].toString()),
//     );
//   }
//
//   final int? id;
//   final String? stripeAccountId;
//   final bool? isAccountActivated;
//   final String? companyName;
//   final String? commercialName;
//   final String? taxId;
//   final String? webPage;
//   final String? accountVerificationStatus;
//   final bool? isProfileCompleted;
//
//   Map<String, dynamic> toJson() => {
//         'id': id,
//         'stripe_account_id': stripeAccountId,
//         'is_account_activated': isAccountActivated,
//         'company_name': companyName,
//         'commercial_name': commercialName,
//         'tax_id': taxId,
//         'web_page': webPage,
//         'account_verification_status': accountVerificationStatus,
//         'is_profile_completed': isProfileCompleted,
//       };
// }
