import 'dart:convert';

import 'package:firebase_notifications_handler/firebase_notifications_handler.dart';
import 'package:go_router/go_router.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/models/requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/pages/rested_trip_page/pages/models/rested_requested_trips_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

/// This is a Top-level function where it is used for handling background or
/// Terminated state notifications. This is optional if you don't use onBackgroundMessage stream
// @pragma('vm:entry-point')
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   'Handling a background message: ${message.messageId}'.logD;
//   await FirebaseMessaging.instance.getInitialMessage().then((event) {
//     '=====>>> ===>>>  tg:  $event'.logD;
//     if (event != null) {
//       NotificationHelper.notificationOnTapHandler(remoteMessage: event);
//     }
//   });
// }

/// This class is helper for initialize Notification with get permission of user,
/// To handle foreground/background/terminated state notification.
class NotificationHelper {
  // /// initialize and setup the notification for device.
  // static Future<void> initializeNotification() async {
  //   // Getting the instance of firebase messaging
  //   final messaging = FirebaseMessaging.instance;
  //   await messaging.setForegroundNotificationPresentationOptions(
  //     alert: true,
  //     badge: true,
  //     sound: true,
  //   );
  //   // initialize local notification for foreground notification.
  //   await LocalNotificationHelper.localNotificationHelper.initialize();
  //   /// Request the notification permission to user,
  //   /// in Android 12 or below,by default Notification permission is granted.
  //   if (Platform.isIOS) {
  //     await messaging.requestPermission();
  //   }
  //   /// To send notification to specific device, we need specific device token.
  //   /// To get device token, we can use following method
  //   (await messaging.getToken()).logD;
  //   // we can also send notification using subscribe the topic.
  //   // await FirebaseMessaging.instance.subscribeToTopic("myTopic");
  //   /// To handle Foreground notification
  //   FirebaseMessaging.onMessage.listen((event) {
  //     '=====>>> fg:  ${event.data}'.logD;
  //     if (event.notification != null) {
  //       if (Platform.isAndroid) {
  //         LocalNotificationHelper.localNotificationHelper
  //             .showNotification(event);
  //       }
  //       //   final data = jsonDecode(
  //       //     (event.data)['data'].toString(),
  //       //   );
  //       //   final type = data['type'];
  //       //   if (data.toString().contains('type')) {}
  //     }
  //   });
  //   /// To handle Background/terminated app notification (This is optional.)
  //   FirebaseMessaging.onBackgroundMessage(
  //     _firebaseMessagingBackgroundHandler,
  //   );
  //   // To handle the Notification Tap Event on Background.
  //   FirebaseMessaging.onMessageOpenedApp.listen((event) {
  //     '=====>>> bg: $event'.logD;
  //     notificationOnTapHandler(remoteMessage: event);
  //     // final data = jsonDecode(
  //     //   event.data['data'].toString(),
  //     // );
  //   });
  //   // To handle the Notification Tap Event on Terminated state.
  //   await FirebaseMessaging.instance.getInitialMessage().then((event) {
  //     '=====>>> tg:  $event'.logD;
  //     if (event != null) {
  //       notificationOnTapHandler(remoteMessage: event);
  //     }
  //   });
  //   // } else {
  //   //   // here you can give a message to the user if user not granted the permission.
  //   //   log('User declined the permission');
  //   // }
  // }
  // /// Notification tap handler
  // static Future<void> notificationOnTapHandler({
  //   RemoteMessage? remoteMessage,
  //   NotificationResponse? localData,
  //   bool isLocal = false,
  // }) async {
  //   '==>> notificationOnTapHandler is called : ${remoteMessage?.data ?? localData?.payload}'
  //       .logD;
  //   final data = remoteMessage?.data ?? jsonDecode(localData?.payload ?? '{}');
  //   final type = data?['type'];
  //   switch (type) {
  //     case AppStrings.chat:
  //       {
  //         await AppNavigationService.pushNamed(
  //           rootNavKey.currentContext!,
  //           const ChatScreen(
  //             title: 'title',
  //             receiverId: 10,
  //             bookingDetailId: 10,
  //             customerChatRoomParameter: null,
  //           ),
  //         );
  //       }
  //     case AppStrings.checkList:
  //       {
  //         await AppNavigationService.pushNamed(
  //           rootNavKey.currentContext!,
  //           ChecklistScreen(
  //             carId: (data['booked_car_id'] as String?) ?? '',
  //             clientName: (data['client_name'] as String?) ?? '',
  //             checkListId: (data['related_object_id'] as String?) ?? '',
  //           ),
  //         );
  //       }
  //     default:
  //       await AppNavigationService.pushNamed(
  //         rootNavKey.currentContext!,
  //         TripDetailsScreen(
  //           isCompleted: false,
  //           id: (data?['booking_id'] as String?) ?? '',
  //         ),
  //       );
  //   }
  //   // if (isLocal == false) {
  //   //   if (remoteMessage != null) {
  //   //     if (remoteMessage.data.isNotEmpty) {
  //   //       remoteMessage.data.logD;
  //   //       // final data = jsonDecode(remoteMessage.data['data'].toString());
  //   //     }
  //   //   }
  //   // } else {
  //   //   if (localData != null) {
  //   //     'local data ===>> ${localData.payload}'.logD;
  //   //     if (localData.payload.isNotEmptyAndNotNull) {}
  //   //   }
  //   // }
  // }

  static Future<void> onNotificationTap(RemoteMessage notificationData) async {
    final data = notificationData.data;
    final type = data[AppStrings.type];
    '===>>>> notification data $data'.logE;
    switch (type) {
      /// when notification type is chat
      case AppStrings.chat:
        {
          final sender = jsonDecode((data[AppStrings.sender] as String?) ?? '');
          final chatRoom = jsonDecode(
            (data[AppStrings.chatRoom] as String?) ?? '',
          );
          final participants = chatRoom[AppStrings.participants];
          if (sender is Map<String, dynamic> &&
              chatRoom is Map<String, dynamic> &&
              participants is List) {
            /// context
            final context = rootNavKey.currentContext!;

            /// get current route or screen
            final currentRoute = GoRouter.of(
              context,
            ).routerDelegate.currentConfiguration.last.matchedLocation;

            /// pop screen when we already in chat screen to avoid duplicate screen
            /// and avoid socket connection issue
            final isChatScreen = currentRoute == AppRoutes.tripsChatPath;
            if (isChatScreen) AppNavigationService.pop(context);

            await AppNavigationService.pushNamed(
              context,
              AppRoutes.tripsChatScreen,
              extra: ChatParams(
                title: (sender[AppStrings.firstName] as String?) ?? '',
                receiverId: sender[AppStrings.id] as int? ?? 0,
                bookingDetailId:
                    (chatRoom[AppStrings.bookingDetailStr] as int?) ?? 0,
                customerChatRoomParameter: ChatRoomDetailModel.fromJson(
                  chatRoom,
                ),
              ),

              //  ChatScreen(
              //   title: (sender[AppStrings.firstName] as String?) ?? '',
              //   receiverId: participants
              //           .where(
              //             (e) => e[AppStrings.id] != sender[AppStrings.id],
              //           )
              //           .firstOrNull?[AppStrings.user] as int? ??
              //       0,
              //   bookingDetailId:
              //       (chatRoom[AppStrings.bookingDetail] as int?) ?? 0,
              //   customerChatRoomParameter:
              //       ChatRoomDetailModel.fromJson(chatRoom),
              // ),
            );
          }
        }

      /// when notification type is checklist
      case AppStrings.checkList:
        {
          await AppNavigationService.pushNamed(
            rootNavKey.currentContext!,
            AppRoutes.tripsChecklistScreen,
            extra: ChecklistParams(
              carId: (data[AppStrings.bookedCarId] as String?) ?? '',
              clientName: (data[AppStrings.clientName] as String?) ?? '',
              checkListId: (data[AppStrings.relatedObjectId] as String?) ?? '',
            ),

            // extra:
            // ChecklistScreen(
            //   carId: (data[AppStrings.bookedCarId] as String?) ?? '',
            //   clientName: (data[AppStrings.clientName] as String?) ?? '',
            //   checkListId: (data[AppStrings.relatedObjectId] as String?) ?? '',
            // ),
          );
        }

      /// when notification type is trip
      case AppStrings.booking ||
          AppStrings.bookingDetail ||
          AppStrings.trip ||
          AppStrings.car:
        final booking = data[AppStrings.relatedObjectData] != null
            ? jsonDecode((data[AppStrings.relatedObjectData] as String?) ?? '')
            : null;
        if (data[AppStrings.notToRedirect] == null) {
          if (booking is Map<String, dynamic>) {
            if (booking[AppStrings.notToRedirect] == null) {
              /// when notification booking type is requested trip
              if (booking[AppStrings.bookingStatus] ==
                      NotificationBookingType.EXCLUSIVE.name ||
                  booking[AppStrings.bookingStatus] ==
                      NotificationBookingType.WAITING_LIST.name) {
                await AppNavigationService.pushNamed(
                  rootNavKey.currentContext!,
                  AppRoutes.tripsRequestedTripsScreen,
                  extra: RequestedTripsParams(
                    tripId: booking[AppStrings.bookingId]?.toString() ?? '0',
                    // isWaitingList:
                    //     booking[AppStrings.relatedObjectType] !=
                    //     AppStrings.exclusive,
                  ),
                );

                /// use for rested trip
              } else if (booking[AppStrings.bookingStatus] ==
                  NotificationBookingType.RESTED.name) {
                await AppNavigationService.pushNamed(
                  rootNavKey.currentContext!,
                  AppRoutes.tripsRestedRequestedTripsScreen,
                  extra: RestedRequestedTripsParams(
                    value: 2,
                    id: booking[AppStrings.id]?.toString() ?? '0',
                    requestedTripsProvider: RequestedTripsProvider(),
                  ),
                  //
                  // ChangeNotifierProvider(
                  //   create: (context) => RequestedTripsProvider(),
                  //   builder: (context, child) {
                  //     return RestedRequestedTripsScreen(
                  //       value: 2,
                  //       id: booking[AppStrings.id]?.toString() ?? '0',
                  //       requestedTripsProvider:
                  //           context.read<RequestedTripsProvider>(),
                  //     );
                  //   },
                  // ),
                );
              } else {
                /// other wise navigate to trip detail screen
                await AppNavigationService.pushNamed(
                  rootNavKey.currentContext!,
                  AppRoutes.tripsTripDetailsScreen,
                  extra: TripDetailsParams(
                    isCompleted: false,
                    id:
                        booking[AppStrings.id]?.toString() ??
                        booking[AppStrings.bookingId]?.toString() ??
                        '',
                  ),
                  //
                  // TripDetailsScreen(
                  //   isCompleted: false,
                  //   id: booking[AppStrings.id]?.toString() ?? '',
                  // ),
                );
              }
            }
          } else {
            await AppNavigationService.pushNamed(
              rootNavKey.currentContext!,
              AppRoutes.tripsTripDetailsScreen,
              extra: TripDetailsParams(
                isCompleted: false,
                id: data[AppStrings.relatedObjectId]?.toString() ?? '',
              ),
              //
              // TripDetailsScreen(
              //   isCompleted: false,
              //   id: data[AppStrings.relatedObjectId]?.toString() ?? '',
              // ),
            );
          }
        }
    }
  }
}
