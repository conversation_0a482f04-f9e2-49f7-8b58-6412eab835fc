// ignore_for_file: lines_longer_than_80_chars

import 'package:flutter/material.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/fonts.gen.dart';

/// Theme Preferences Model
abstract class ThemePreferences {
  /// dark theme type
  ThemeData get dark;

  /// light theme type
  ThemeData get light;
}

/// Theme Customization
class CustomTheme extends ThemePreferences {
  @override
  ThemeData get light => ThemeData(
        fontFamily: AppFontFamily.inter,
        scaffoldBackgroundColor: AppColors.white,
        dialogTheme: const DialogThemeData(
          backgroundColor: Colors.white,
          titleTextStyle: TextStyle(color: AppColors.black),
          contentTextStyle: TextStyle(color: AppColors.black),
        ),
        colorScheme: const ColorScheme(
          onPrimary: AppColors.black,
          surface: AppColors.pageBGColor,
          brightness: Brightness.light,
          onSecondary: AppColors.black,
          onError: AppColors.primaryColor,
          onSurface: AppColors.black,
          error: AppColors.errorColor,
          primary: AppColors.primaryColor,
          secondary: AppColors.white,
          surfaceContainerHighest: AppColors.white,
        ),
        datePickerTheme: const DatePickerThemeData(
          backgroundColor: AppColors.pageBGColor,
        ),
        // dropdownMenuTheme: const DropdownMenuThemeData(
        //   menuStyle: MenuStyle(
        //     backgroundColor: WidgetStatePropertyAll(AppColors.black),
        //   ),
        //   inputDecorationTheme:
        //       InputDecorationTheme(fillColor: AppColors.black),
        // ),
        primaryColor: AppColors.primaryColor,
        primaryColorDark: AppColors.primaryColor,
        appBarTheme: const AppBarTheme(
          iconTheme: IconThemeData(color: AppColors.black),
          backgroundColor: AppColors.white,
        ),
        scrollbarTheme: ScrollbarThemeData(
          interactive: true,
          // thumbVisibility: WidgetStateProperty.all(true),
          radius: Radius.circular(AppSize.r10),
          // thumbColor: WidgetStateProperty.all(Colors.grey),
          // thickness: WidgetStateProperty.all(AppSize.sp4),
          minThumbLength: 100,
        ),
        highlightColor: Colors.transparent,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            minimumSize: Size(
              double.infinity,
              AppSize.h60,
            ),
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSize.r14),
            ),
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
        ),
        // dividerColor: AppColors.mediumGrey,
        splashColor: AppColors.transparent,
        textTheme: TextTheme(
          headlineLarge: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp30,
            fontWeight: FontWeight.w700,
          ),
          headlineMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp30,
          ),
          displayLarge: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp22,
            fontWeight: FontWeight.w700,
          ),
          displayMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp22,
          ),
          titleLarge: TextStyle(
            fontSize: AppSize.sp20,
            color: AppColors.ff212529,
            fontWeight: FontWeight.w700,
          ),
          titleSmall: TextStyle(
            color: AppColors.black,
            fontSize: AppSize.sp14,
            fontWeight: FontWeight.w400,
          ),
          titleMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp18,
            fontWeight: FontWeight.w400,
          ),
          bodyMedium: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
          ),
          bodySmall: TextStyle(
            color: AppColors.ffADB5BD,
            fontSize: AppSize.sp12,
            fontWeight: FontWeight.w500,
          ),
          bodyLarge: TextStyle(
            fontSize: AppSize.sp16,
            color: AppColors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        // shadowColor: AppColors.optionLineColor,
        disabledColor: Colors.grey,
        timePickerTheme: TimePickerThemeData(
          dayPeriodColor: AppColors.primaryColor,
          dayPeriodTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          helpTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          hourMinuteTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp24,
            fontWeight: FontWeight.w600,
            fontFamily: AppFontFamily.inter,
          ),
          timeSelectorSeparatorTextStyle: WidgetStatePropertyAll(
            TextStyle(
              color: AppColors.ff343A40,
              fontFamily: AppFontFamily.inter,
              fontSize: AppSize.sp32,
              height: 1.8,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            hintStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
            labelStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSize.r14),
          ),
          dialTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
        ),
        sliderTheme: const SliderThemeData(
          thumbColor: AppColors.primaryColor,
          activeTrackColor: AppColors.primaryColor,
          inactiveTrackColor: AppColors.ffADB5BD,
          trackHeight: 1,
          valueIndicatorColor: AppColors.primaryColor,
          valueIndicatorTextStyle: TextStyle(color: AppColors.white),
          thumbShape: RoundSliderThumbShape(),
        ),
      );

  @override
  ThemeData get dark => ThemeData(
        fontFamily: AppFontFamily.inter,
        scaffoldBackgroundColor: AppColors.transparent,
        dialogTheme: const DialogThemeData(
          backgroundColor: Colors.white,
          titleTextStyle: TextStyle(color: AppColors.black),
          contentTextStyle: TextStyle(color: AppColors.black),
        ),
        datePickerTheme: const DatePickerThemeData(
          backgroundColor: AppColors.pageBGColor,
        ),
        canvasColor: AppColors.warningColor,
        // dropdownMenuTheme: DropdownMenuThemeData(menuStyle: MenuStyle()),
        colorScheme: const ColorScheme(
          onPrimary: AppColors.white,
          // background: AppColors.secondaryBGColor,
          surface: Color.fromARGB(255, 0, 0, 0),
          brightness: Brightness.dark,
          onSecondary: AppColors.white,
          onError: AppColors.primaryColor,
          onSurface: AppColors.white,
          // onBackground: AppColors.secondaryBGColor,
          error: AppColors.black,
          primary: AppColors.primaryColor,
          secondary: AppColors.black,
          // surfaceVariant: AppColors.secondaryBGColor,
        ),
        primaryColor: AppColors.primaryColor,
        primaryColorDark: AppColors.primaryColor,
        appBarTheme: const AppBarTheme(
          iconTheme: IconThemeData(color: AppColors.white),
          backgroundColor: AppColors.black,
        ),
        scrollbarTheme: ScrollbarThemeData(
          interactive: true,
          // thumbVisibility: MaterialStateProperty.all(true),
          radius: Radius.circular(AppSize.r10),
          // thumbColor: MaterialStateProperty.all(Colors.grey),
          // thickness: MaterialStateProperty.all(AppSize.sp4),
          minThumbLength: 100,
        ),
        highlightColor: Colors.transparent,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            minimumSize: Size(
              double.infinity,
              AppSize.h60,
            ),
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSize.r14),
            ),
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.white,
            ),
          ),
        ),
        // dividerColor: AppColors.mediumGrey,
        splashColor: AppColors.transparent,
        textTheme: TextTheme(
          headlineLarge: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp30,
            fontWeight: FontWeight.w700,
          ),
          headlineMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp30,
          ),
          displayLarge: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp22,
            fontWeight: FontWeight.w700,
          ),
          displayMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp22,
          ),
          titleLarge: TextStyle(
            fontSize: AppSize.sp20,
            color: AppColors.ff212529,
            fontWeight: FontWeight.w700,
          ),
          titleMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp18,
            fontWeight: FontWeight.w400,
          ),
          titleSmall: TextStyle(
            color: AppColors.black,
            fontSize: AppSize.sp14,
            fontWeight: FontWeight.w400,
          ),
          bodyLarge: TextStyle(
            fontSize: AppSize.sp16,
            color: AppColors.black,
            fontWeight: FontWeight.w500,
          ),
          bodyMedium: TextStyle(
            color: AppColors.white,
            fontSize: AppSize.sp14,
          ),
          bodySmall: TextStyle(
            color: AppColors.ffADB5BD,
            fontSize: AppSize.sp12,
            fontWeight: FontWeight.w500,
          ),
        ),
        sliderTheme: const SliderThemeData(
          thumbColor: AppColors.primaryColor,
          activeTrackColor: AppColors.primaryColor,
          inactiveTrackColor: AppColors.ff6C757D,
          trackHeight: 1,
          valueIndicatorColor: AppColors.white,
          thumbShape: RoundSliderThumbShape(),
        ),
        // shadowColor: AppColors.optionLineColor,
        disabledColor: Colors.grey,
        timePickerTheme: TimePickerThemeData(
          dayPeriodColor: AppColors.primaryColor,
          dayPeriodTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          helpTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
          hourMinuteTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp24,
            fontWeight: FontWeight.w600,
            fontFamily: AppFontFamily.inter,
          ),
          timeSelectorSeparatorTextStyle: WidgetStatePropertyAll(
            TextStyle(
              color: AppColors.ff343A40,
              fontFamily: AppFontFamily.inter,
              fontSize: AppSize.sp32,
              height: 1.8,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            hintStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
            labelStyle: TextStyle(
              color: AppColors.ff343A40,
              fontSize: AppSize.sp14,
              fontFamily: AppFontFamily.inter,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSize.r14),
          ),
          dialTextStyle: TextStyle(
            color: AppColors.ff343A40,
            fontSize: AppSize.sp14,
            fontFamily: AppFontFamily.inter,
          ),
        ),
      );
}
