// import 'package:shared_preferences/shared_preferences.dart';

// class SharedPrefrenceHandler {
//   // Singleton instance
//   static final SharedPrefrenceHandler _instance = SharedPrefrenceHandler._internal();

//   factory SharedPrefrenceHandler() {
//     return _instance;
//   }

//   SharedPrefrenceHandler._internal();

//   // SharedPreferences instance
//   SharedPreferences? _preferences;

//   // Initialize SharedPreferences instance
//   Future<void> init() async {
//     _preferences = await SharedPreferences.getInstance();
//   }

//   // Getter for SharedPreferences instance
//   SharedPreferences? get preferences => _preferences;

//   // Set a string value
//   Future<void> setString(String key, String value) async {
//     await _preferences?.setString(key, value);
//   }

//   // Get a string value
//   String? getString(String key) {
//     return _preferences?.getString(key);
//   }

//   // Set an int value
//   Future<void> setInt(String key, int value) async {
//     await _preferences?.setInt(key, value);
//   }

//   // Get an int value
//   int? getInt(String key) {
//     return _preferences?.getInt(key);
//   }

//   // Set a bool value
//   Future<void> setBool(String key, bool value) async {
//     await _preferences?.setBool(key, value);
//   }

//   // Get a bool value
//   bool? getBool(String key) {
//     return _preferences?.getBool(key);
//   }

//   // Set a double value
//   Future<void> setDouble(String key, double value) async {
//     await _preferences?.setDouble(key, value);
//   }

//   // Get a double value
//   double? getDouble(String key) {
//     return _preferences?.getDouble(key);
//   }

//   // Set a list of strings
//   Future<void> setStringList(String key, List<String> value) async {
//     await _preferences?.setStringList(key, value);
//   }

//   // Get a list of strings
//   List<String>? getStringList(String key) {
//     return _preferences?.getStringList(key);
//   }

//   // Remove a key
//   Future<void> remove(String key) async {
//     await _preferences?.remove(key);
//   }

//   // Clear all keys
//   Future<void> clear() async {
//     await _preferences?.clear();
//   }
// }
