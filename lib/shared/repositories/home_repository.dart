import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/presentation/modules/home_module/models/car_creation_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/pickup_cost_model.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/insurance_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';

/// User trip related API methods class
final class HomeRepository {
  ///  trip repository constructor
  const HomeRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Api call get car brands
  Future<ApiResult<List<CarCreationModel>>> getCarBrands(ApiRequest request) {
    return DioRequest<List<CarCreationModel>>(
      dio: dio,
      path: EndPoints.carBrandsAll,
      listJsonMapper: CarCreationModel.fromList,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api call get car brands
  Future<ApiResult<List<CarCreationModel>>> getCarListForCreation(
    ApiRequest request,
  ) {
    return DioRequest<List<CarCreationModel>>(
      dio: dio,
      path: EndPoints.listCarForCreation,
      listJsonMapper: CarCreationModel.fromList,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<StockLocationModel>> listNearByLocation(
    ApiRequest request,
  ) {
    return DioRequest<StockLocationModel>(
      dio: dio,
      path: request.path!,
      listJsonMapper: StockLocationModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for find trip
  Future<ApiResult<ProviderDataModel>> findTransporter(
    ApiRequest request,
  ) {
    return DioRequest<ProviderDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ProviderDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for create exclusive trip or rested trip
  Future<ApiResult<Map<String, dynamic>>> createCarVerification(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for updating rested trip
  Future<ApiResult<Map<String, dynamic>>> updateRestedTrip(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// for create exclusive trip or rested trip
  Future<ApiResult<Map<String, dynamic>>> createExclusiveTrip(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for get payment information
  Future<ApiResult<PaymentDataModel>> getPayment(
    ApiRequest request,
  ) {
    return DioRequest<PaymentDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: PaymentDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for register temporary slot
  Future<ApiResult<Map<String, dynamic>>> registerTempSlot(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// for register temporary slot
  Future<ApiResult<Map<String, dynamic>>> getBookingUrl(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// to add user information
  Future<ApiResult<List<Map<String, dynamic>>>> createBookingAssign(
    ApiRequest request,
  ) {
    return DioRequest<List<Map<String, dynamic>>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => List.generate(
        p0.length,
        (index) => p0[index] as Map<String, dynamic>,
      ),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// to add user information
  Future<ApiResult<Map<String, dynamic>>> createBookingSession(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for get insurance
  Future<ApiResult<List<InsuranceModel>>> getInsurance(
    ApiRequest request,
  ) {
    return DioRequest<List<InsuranceModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => p0
          .map((e) => InsuranceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// get cost of pick up
  Future<ApiResult<PickupCostModel>> getCostOfPickUp(
    ApiRequest request,
  ) {
    return DioRequest<PickupCostModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: PickupCostModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
    ).get();
  }
}
