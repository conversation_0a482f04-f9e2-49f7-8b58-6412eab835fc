{"@@locale": "en", "helloWorld": "hello world", "@helloWorld": {}, "byeWorld": "bye world", "@byeWorld": {}, "just_now": "Just now", "@just_now": {}, "min_ago": "Minute ago", "@min_ago": {}, "hour_ago": "Hour ago", "@hour_ago": {}, "day_ago": "Day ago", "@day_ago": {}, "week_ago": "Week ago", "@week_ago": {}, "year_ago": "Year ago", "@year_ago": {}, "today": "Today", "@today": {}, "yesterday": "Yesterday", "@yesterday": {}, "copied": "<PERSON>pied", "@copied": {}, "pleaseEnterEmail": "Please enter email", "@pleaseEnterEmail": {}, "pleaseEnterValidEmail": "Please enter valid email", "@pleaseEnterValidEmail": {}, "pleaseEnterCurrentPassword": "Please enter current password", "@pleaseEnterCurrentPassword": {}, "pleaseEnterPassword": "Please enter password", "@pleaseEnterPassword": {}, "passwordMinLength": "Password should be at least 8 characters.", "@passwordMinLength": {}, "passwordShouldBeAtLeast8Characters": "Password should be at least 8 characters.", "@passwordShouldBeAtLeast8Characters": {}, "newPasswordAndOldPasswordCannotBeSame": "New password and old password cannot be same", "@newPasswordAndOldPasswordCannotBeSame": {}, "otpMinLength": "Please enter valid OTP", "@otpMinLength": {}, "pleaseEnterValidUsername": "Please enter valid username", "@pleaseEnterValidUsername": {}, "usernameMinLength": "Please enter valid username", "@usernameMinLength": {}, "pleaseEnterName": "Please enter name", "@pleaseEnterName": {}, "nameMinLength": "Please enter name", "@nameMinLength": {}, "pleaseEnterFirstName": "Please enter first name", "@pleaseEnterFirstName": {}, "firstNameMinLength": "Please enter first name", "@firstNameMinLength": {}, "pleaseEnterLastName": "Please enter last name", "@pleaseEnterLastName": {}, "lastNameMinLength": "Please enter last name", "@lastNameMinLength": {}, "passwordDoesNotMatch": "Password does not match", "@passwordDoesNotMatch": {}, "accountRegisterSuccessfully": "Account registered successfully, Please verify your email.", "@accountRegisterSuccessfully": {}, "passwordIncludeLetterNumberSymbol": "Password must include a letter, a number, and a symbol.", "@passwordIncludeLetterNumberSymbol": {}, "checkYourEmail": "Check your email", "@checkYourEmail": {}, "sentVerificationCode": "We've sent a verification code on ", "@sentVerificationCode": {}, "verifyOtp": "Verify OTP", "@verifyOtp": {}, "pleaseEnterOtp": "Please enter OTP!", "@pleaseEnterOtp": {}, "pleaseEnterValidOtp": "Please enter valid OTP!", "@pleaseEnterValidOtp": {}, "didNtReceiveEmail": "Didn't received the email? ", "@didNtReceiveEmail": {}, "clickToResend": "Click to resend", "@clickToResend": {}, "resetPasswordInstructions": "We'll send you OTP in your email", "@resetPasswordInstructions": {}, "sendAnEmail": "Send an email", "@sendAnEmail": {}, "ultimateStressFreeCarTransport": "The ultimate stress-free car\ntransport experience.", "@ultimateStressFreeCarTransport": {}, "email": "Email", "@email": {}, "enterYourEmail": "Enter your email", "@enterYourEmail": {}, "password": "Password", "@password": {}, "enterYourPassword": "Enter your password", "@enterYourPassword": {}, "enterYourConfirmPassword": "Enter your confirm password", "@enterYourConfirmPassword": {}, "forgotPassword": "Forgot Password", "@forgotPassword": {}, "forgotPasswordQuestion": "Forgot Password?", "@forgotPasswordQuestion": {}, "logIn": "Log In", "@logIn": {}, "doNtHaveAccount": "Don't have an account?", "@doNtHaveAccount": {}, "signUp": "Sign Up", "@signUp": {}, "setPassword": "Set password", "@setPassword": {}, "setNewPassword": "Go ahead and set a new password", "@setNewPassword": {}, "confirmPassword": "Confirm Password", "@confirmPassword": {}, "pleaseEnterConfirmPassword": "Please enter confirm password", "@pleaseEnterConfirmPassword": {}, "pleaseEnterDetails": "Please enter your details to create an account", "@pleaseEnterDetails": {}, "name": "Name", "@name": {}, "enterYourName": "Enter your name", "@enterYourName": {}, "createAccount": "Create an account", "@createAccount": {}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {}, "customer_support": "Customer Support", "@customer_support": {}, "payments": "Payments", "@payments": {}, "past_purchase": "Past Purchase", "@past_purchase": {}, "sign_out": "Sign Out", "@sign_out": {}, "delete_account": "Delete Account", "@delete_account": {}, "edit_details": "Edit Details", "@edit_details": {}, "change_password": "Change Password", "@change_password": {}, "save": "Save", "@save": {}, "enter_your_name": "Enter your name", "@enter_your_name": {}, "enter_your_email": "Enter your email", "@enter_your_email": {}, "enter_your_password": "Enter your password", "@enter_your_password": {}, "set_new_password": "Set new password", "@set_new_password": {}, "go_ahead_and_set_a_new_password": "Go ahead and set a new password", "@go_ahead_and_set_a_new_password": {}, "old_password": "Old Password", "@old_password": {}, "uploadCarImg": "Upload car images", "@uploadCarImg": {}, "new_password": "New Password", "@new_password": {}, "confirm_password": "Confirm Password", "@confirm_password": {}, "please_enter_confirm_password": "Please enter confirm password", "@please_enter_confirm_password": {}, "transporter": "Transporter", "@transporter": {}, "total_trip_cost": "Total Trip Cost", "@total_trip_cost": {}, "no_of_vehicles": "No. Of Vehicles", "@no_of_vehicles": {}, "equipment_type": "Equipment type", "@equipment_type": {}, "vehicles_info": "Vehicles Info", "@vehicles_info": {}, "details": "Details", "@details": {}, "car_brand": "Car brand", "@car_brand": {}, "car_model": "Car model", "@car_model": {}, "car_serial": "Car serial #", "@car_serial": {}, "car_year": "Car year", "@car_year": {}, "view_details": "View Details", "@view_details": {}, "stockLocations": "Stock Locations", "@stockLocations": {}, "stockLocation": "Stock Location", "@stockLocation": {}, "originStockLocation": "Origin Stock Location", "@originStockLocation": {}, "chooseOriginStockLocation": "Choose origin stock location", "@chooseOriginStockLocation": {}, "pleaseSelectPickupDate": "Please select pickup date", "@pleaseSelectPickupDate": {}, "pleaseSelectDeliveryDate": "Please select delivery date", "@pleaseSelectDeliveryDate": {}, "pleaseSelectDelivery": "Please select delivery date", "@pleaseSelectDelivery": {}, "dropStockLocation": "Drop Stock Location", "@dropStockLocation": {}, "chooseDropStockLocation": "Choose drop stock location", "@chooseDropStockLocation": {}, "vehicleInfo": "Vehicles Info", "@vehicleInfo": {}, "vehicleBrand": "Vehicle brand", "@vehicleBrand": {}, "chooseVehicleBrand": "Choose vehicle brand", "@chooseVehicleBrand": {}, "vehicleModel": "Vehicle model", "@vehicleModel": {}, "chooseVehicleModel": "Choose vehicle model", "@chooseVehicleModel": {}, "vehicleYear": "Vehicle year", "@vehicleYear": {}, "chooseVehicleYear": "Choose vehicle year", "@chooseVehicleYear": {}, "vehicleCondition": "Vehicle condition", "@vehicleCondition": {}, "chooseVehicleCondition": "Write vehicle condition", "@chooseVehicleCondition": {}, "pleaseDescribeTheIssue": "Please describe the issue.", "@pleaseDescribeTheIssue": {}, "writeIssueDetailsHere": "Write issue details here", "@writeIssueDetailsHere": {}, "vehicleSerialNo": "Vehicle serial no.", "@vehicleSerialNo": {}, "vehicleSerialNumber": "Vehicle serial number", "@vehicleSerialNumber": {}, "iNeedMyCarToBePickedUpAndTakenToTheStockLocation": "I need my car to be picked up and taken to the stock location.", "@iNeedMyCarToBePickedUpAndTakenToTheStockLocation": {}, "enterPickupAddress": "Enter pickup address", "@enterPickupAddress": {}, "towingCost": "Towing cost", "@towingCost": {}, "drivingCostWithOperator": "Driving cost with operator", "@drivingCostWithOperator": {}, "addAnotherVehicle": "Add Another Vehicle", "@addAnotherVehicle": {}, "pickupAndDeliveryDates": "Pickup & Delivery Dates", "@pickupAndDeliveryDates": {}, "pickupDate": "Pickup date", "@pickupDate": {}, "deliveryDate": "Delivery date", "@deliveryDate": {}, "transportAllVehicleInOneTruck": "Transport all vehicle in one truck", "@transportAllVehicleInOneTruck": {}, "vehicleVersion": "Vehicle Version", "@vehicleVersion": {}, "passChanged": "Password changed successfully", "@passChanged": {}, "findTransporter": "Find Transporter", "@findTransporter": {}, "enterVBrandName": "Enter brand of your vehicle", "@enterVBrandName": {}, "enterBrandName": "Enter brand of your {enterBrandName} vehicle", "@enterBrandName": {"placeholders": {"enterBrandName": {"type": "String"}}}, "chooseVBrandName": "Choose brand of your vehicle", "@chooseVBrandName": {}, "chooseBrandName": "Choose brand of your {brandName} vehicle", "@chooseBrandName": {"placeholders": {"brandName": {"type": "String"}}}, "enterVYear": "Enter year of your vehicle", "@enterVYear": {}, "enterVehicleYear": "Enter year of your {enterVehicleYear} vehicle", "@enterVehicleYear": {"placeholders": {"enterVehicleYear": {"type": "String"}}}, "chooseVYear": "Choose year of your vehicle", "@chooseVYear": {}, "pleaseChooseVehicleYear": "Choose year of your {vehicleYear} vehicle", "@pleaseChooseVehicleYear": {"placeholders": {"vehicleYear": {"type": "String"}}}, "enterVModel": "Enter model of your vehicle", "@enterVModel": {}, "enterVehicleModel": "Enter model of your {enterVehicleModel} vehicle", "@enterVehicleModel": {"placeholders": {"enterVehicleModel": {"type": "String"}}}, "chooseVModel": "Choose model of your vehicle", "@chooseVModel": {}, "pleaseChooseVehicleModel": "Choose model of your {vehicleModel} vehicle", "@pleaseChooseVehicleModel": {"placeholders": {"vehicleModel": {"type": "String"}}}, "pleaseEnterVSerialNumber": "Please enter serial number of your vehicle", "@pleaseEnterVSerialNumber": {}, "pleaseEnterSerialNumber": "Please enter serial number of your {serialNumber} vehicle", "@pleaseEnterSerialNumber": {"placeholders": {"serialNumber": {"type": "String"}}}, "pleasePickUpAddress": "Please enter your pickup address", "@pleasePickUpAddress": {}, "bookingDataVerification": "Booking data has been successfully submitted for car verification.", "@bookingDataVerification": {}, "exclusiveCreated": "Exclusive booking created successfully.", "@exclusiveCreated": {}, "pleasePickUpZip": "Please enter zip code of your pickup address", "@pleasePickUpZip": {}, "pleaseEnterDropUserName": "Please enter drop user name", "@pleaseEnterDropUserName": {}, "pleaseEnterDropUserDocType": "Please enter drop user document type", "@pleaseEnterDropUserDocType": {}, "pleaseEnterDropUserDoc": "Please enter drop user document", "@pleaseEnterDropUserDoc": {}, "pleaseEnterPickUserName": "Please enter pick user name", "@pleaseEnterPickUserName": {}, "pleaseEnterPickUserDocType": "Please enter pick user document type", "@pleaseEnterPickUserDocType": {}, "signOut": "Sign Out", "@signOut": {}, "cancel": "Cancel", "@cancel": {}, "newUpdateAvailable": "New Update Available", "@newUpdateAvailable": {}, "updateMessage": "We've made improvements and fixed bugs to enhance your experience.Please update the app to continue using it smoothly.", "@updateMessage": {}, "versionInfo": "📱 Current version: {currentAppVersion}\n✅ Required version: {minimumAppVersion}", "@versionInfo": {"placeholders": {"currentAppVersion": {"type": "String"}, "minimumAppVersion": {"type": "String"}}}, "updateNow": "Update Now", "@updateNow": {}, "troubleUpdating": "Having trouble updating?\n Visit the app store manually or contact support", "@troubleUpdating": {}, "signOutContent": "Are you sure you want to Sign Out?", "@signOutContent": {}, "deleteAccount": "Delete Account", "@deleteAccount": {}, "deleteAccountContent": "Are you sure you want to delete this account?", "@deleteAccountContent": {}, "delete": "Delete", "@delete": {}, "searchOriginLocation": "Search origin location", "@searchOriginLocation": {}, "searchDropLocation": "Search drop location", "@searchDropLocation": {}, "enterZipCode": "Enter ZIP code", "@enterZipCode": {}, "userPickUpLocation": "User's Pick-Up location", "@userPickUpLocation": {}, "userLocation": "User's Location", "@userLocation": {}, "userDeliveryLocation": "User's Delivery location", "@userDeliveryLocation": {}, "toContinueWithTrip": "To continue with exclusive trip you have to fill the Pick-Up and Delivery Location", "@toContinueWithTrip": {}, "otpSentSuccess": "OTP sent successfully!", "@otpSentSuccess": {}, "assigned": "Assigned", "@assigned": {}, "spotAvailableReservation": "Spot available for reservation", "@spotAvailableReservation": {}, "noOfVehicle": "No. Of Vehicles", "@noOfVehicle": {}, "vehicleType": "Vehicle Type", "@vehicleType": {}, "enterVehicleBrand": "Enter vehicle brand", "@enterVehicleBrand": {}, "enterVehicleYearLabel": "Enter vehicle year", "@enterVehicleYearLabel": {}, "enterVehicleModelLabel": "Enter vehicle model", "@enterVehicleModelLabel": {}, "exclusiveTrip": "Exclusive Trip", "@exclusiveTrip": {}, "didNtFoundCar": "Didn't found car?, Send your car info to Verification", "@didNtFoundCar": {}, "aPersonalized": "A personalized service where your vehicle is picked up from your premises and delivered to your chosen location (if accessible). With no sharing and flexible scheduling, It offers exclusivity at a higher cost.", "@aPersonalized": {}, "pleaseEnterPickUserDoc": "Please enter pick user document", "@pleaseEnterPickUserDoc": {}, "sendRequest": "Send Request", "@sendRequest": {}, "sharedTrip": "Shared Trip", "@sharedTrip": {}, "transportUrVehicle": "Transport your vehicle with others by dropping it at a designated pick-up point for delivery to a set location. This cost-effective option excludes home pick-up and delivery.", "@transportUrVehicle": {}, "winchRequired": "Winch required", "@winchRequired": {}, "noTransportersFound": "No Transporters Found", "@noTransportersFound": {}, "noProvider": "No provider", "@noProvider": {}, "thereIsNoProviderWaitList": "There is no provider with the requested characteristics, You can join waitlist or continue with exclusive trip", "@thereIsNoProviderWaitList": {}, "thereIsNoProvider": "There is no provider with the requested characteristics, You can continue with exclusive trip", "@thereIsNoProvider": {}, "enterPersonName": "Enter person's name", "@enterPersonName": {}, "chooseIdType": "Choose the ID proof type", "@chooseIdType": {}, "idType": "ID type", "@idType": {}, "idProofPhoto": "ID Proof photo", "@idProofPhoto": {}, "uploadPicture": "Upload Pictures of your ID proof", "@uploadPicture": {}, "tapThisAnd": "Tap this and choose the pictures you want to upload from  your device", "@tapThisAnd": {}, "enterUrAddress": "Enter your address", "@enterUrAddress": {}, "loading": "Loading...", "@loading": {}, "yourAddress": "Your address not found", "@yourAddress": {}, "summary": "Summary", "@summary": {}, "transportationCost": "Transportation Cost", "@transportationCost": {}, "dropOffStorageFee": "Drop-off Storage Fee", "@dropOffStorageFee": {}, "pickupStorageFee": "Pickup Storage Fee", "@pickupStorageFee": {}, "serviceFee": "Service Fee", "@serviceFee": {}, "dropTransportation": "Drop-off Transportation Cost", "@dropTransportation": {}, "includeInsurance": "Include Insurance", "@includeInsurance": {}, "noteEnsure": "Note: Ensure your vehicle against any damage during transport.", "@noteEnsure": {}, "total": "Total", "@total": {}, "noteUHaveToPay": "Note: You have to pay {pay}% amount of total amount right now at a time of booking and rest of the {remain}% amount on the time of delivery.", "@noteUHaveToPay": {"placeholders": {"pay": {"type": "String"}, "remain": {"type": "String"}}}, "actualTransportationCost": "Actual Transportation Cost", "@actualTransportationCost": {}, "extraTransportationCost": "Extra Transportation Cost", "@extraTransportationCost": {}, "insuranceCost": "Insurance Cost", "@insuranceCost": {}, "totalInsuranceCost": "Total Insurance Cost", "@totalInsuranceCost": {}, "exclusiveTripExtraCostNote": "Due to it is exclusive trip you've to pay extra transportation cost", "@exclusiveTripExtraCostNote": {}, "failedToFetchSuggestion": "Failed to fetch suggestion", "@failedToFetchSuggestion": {}, "shipmentConfirmation": "Shipment Confirmation", "@shipmentConfirmation": {}, "yourShipment": "Your Shipment", "@yourShipment": {}, "noOfTotalVehicle": "No. Of Total Vehicles", "@noOfTotalVehicle": {}, "proceedToPayment": "Proceed to Payments", "@proceedToPayment": {}, "pickupFrom": "Pickup from", "@pickupFrom": {}, "dropAt": "Drop at", "@dropAt": {}, "viewDetails": "View Details", "@viewDetails": {}, "pickupLocation": "Pickup Location", "@pickupLocation": {}, "deliveryLocation": "Delivery Location", "@deliveryLocation": {}, "notePleaseDrop": "Note: Please drop off your vehicle one day before the scheduled date and pick it up one day after delivery to allow time for a condition checklist.", "@notePleaseDrop": {}, "pleaseSelectDropDate": "Please select drop date for your vehicle at stock location", "@pleaseSelectDropDate": {}, "carBrand": "Car brand", "@carBrand": {}, "carModel": "Car model", "@carModel": {}, "carSerial": "Car serial #", "@carSerial": {}, "carYear": "Car year", "@carYear": {}, "carSize": "Car size", "@carSize": {}, "dropOffDate": "Drop-off Date", "@dropOffDate": {}, "selectVehicleDrop": "Select vehicle drop off date at stock location", "@selectVehicleDrop": {}, "selectDateForPickupCar": "Choose the date your vehicle is ready for pickup", "@selectDateForPickupCar": {}, "storageFee": "Storage Fee", "@storageFee": {}, "perDay": "per day", "@perDay": {}, "close": "Close", "@close": {}, "continues": "Continue", "@continues": {}, "transportList": "Transporters List", "@transportList": {}, "edit": "Edit", "@edit": {}, "chooseTransporter": "Choose Transporter", "@chooseTransporter": {}, "filterBy": "Filter <PERSON>", "@filterBy": {}, "noProviderFound": "No provider found", "@noProviderFound": {}, "userInfo": "User Info", "@userInfo": {}, "carDropPerson": "Car Drop person info", "@carDropPerson": {}, "thisIsDropInfo": "This is the info of the person who is going to drop the car", "@thisIsDropInfo": {}, "carPickupPerson": "Car Pickup person info", "@carPickupPerson": {}, "thisIsDeliveryInfo": "This is the info of the person who is going to take delivery", "@thisIsDeliveryInfo": {}, "uAlreadyAssign": "You already assign this vehicle, Please select another one", "@uAlreadyAssign": {}, "noSlotAvailable": "No slot available for your next car", "@noSlotAvailable": {}, "pleaseAssignCar": "Please assign car for slot", "@pleaseAssignCar": {}, "pleaseChooseCar": "Please choose a car for a slot", "@pleaseChooseCar": {}, "clearAll": "Clear All", "@clearAll": {}, "teamCapacity": "Team Capacity", "@teamCapacity": {}, "rating": "Rating", "@rating": {}, "rated": "Rated", "@rated": {}, "pricing": "Pricing", "@pricing": {}, "lowestPerKM": "Lowest Per K.M", "@lowestPerKM": {}, "in2Days": "In 2 Days", "@in2Days": {}, "rateProvider": "Rate Provider", "@rateProvider": {}, "payRemainAmount": "<PERSON> <PERSON><PERSON> Amount", "@payRemainAmount": {}, "remainPayments": "Remaining Payments", "@remainPayments": {}, "cancelTrip": "Cancel Trip", "@cancelTrip": {}, "inTrackTransport": "InTrack Transport", "@inTrackTransport": {}, "addNotes": "Add Notes", "@addNotes": {}, "notes": "Notes", "@notes": {}, "pleaseAddNotes": "Please add notes", "@pleaseAddNotes": {}, "reportsFromTransporter": "Reports from transporter", "@reportsFromTransporter": {}, "writeNotes": "Write Notes", "@writeNotes": {}, "writeUrMessage": "Write your message here", "@writeUrMessage": {}, "upcoming": "Upcoming", "@upcoming": {}, "ongoing": "Ongoing", "@ongoing": {}, "completed": "Completed", "@completed": {}, "chatWithProvider": "Chat with provider", "@chatWithProvider": {}, "checklist": "Checklist", "@checklist": {}, "checklists": "Checklists", "@checklists": {}, "mileageAtPickup": "Mileage at Pickup", "@mileageAtPickup": {}, "mileageAtDelivery": "Mileage at Delivery", "@mileageAtDelivery": {}, "pickupDateNPlace": "Pickup Date & Place", "@pickupDateNPlace": {}, "deliveryDateNPlace": "Delivery Date & Place", "@deliveryDateNPlace": {}, "pickupOfficer": "Pickup Officer", "@pickupOfficer": {}, "deliveryOfficer": "Delivery Officer", "@deliveryOfficer": {}, "yes": "Yes", "@yes": {}, "no": "No", "@no": {}, "uploadPictureOfId": "Upload Pictures of your ID proof", "@uploadPictureOfId": {}, "tapThisNChoose": "Tap this and choose the pictures you want to upload from  your device", "@tapThisNChoose": {}, "editDetails": "Edit Details", "@editDetails": {}, "originLocation": "Origin Location", "@originLocation": {}, "dropLocation": "Drop Location", "@dropLocation": {}, "addMoreCars": "Add more cars", "@addMoreCars": {}, "paymentSummary": "Payment Summary", "@paymentSummary": {}, "paidAmount": "<PERSON><PERSON>", "@paidAmount": {}, "remainAmount": "<PERSON><PERSON><PERSON>", "@remainAmount": {}, "cancelBooking": "Cancel Booking", "@cancelBooking": {}, "chooseWhatUFeel": "Choose what you feel", "@chooseWhatUFeel": {}, "writeSuggestion": "Write Suggestion", "@writeSuggestion": {}, "writeUrSuggestion": "Write your suggestion if you have", "@writeUrSuggestion": {}, "submitReview": "Submit Review", "@submitReview": {}, "viewCheckList": "View Check List", "@viewCheckList": {}, "pleaseEnterPickLocation": "Please enter your pickup location", "@pleaseEnterPickLocation": {}, "pleaseEnterDeliveryLocation": "Please enter your delivery location", "@pleaseEnterDeliveryLocation": {}, "submitForApproval": "Submit Detail for Approval", "@submitForApproval": {}, "assignedCars": "Assigned Cars", "@assignedCars": {}, "howManyCar": "How many cars you want to ship with this provider", "@howManyCar": {}, "chooseVehicle": "Choose vehicle", "@chooseVehicle": {}, "assignMoreCar": "Assign <PERSON>", "@assignMoreCar": {}, "user": "User", "@user": {}, "userEmail": "User Email", "@userEmail": {}, "creditDebitCards": "Credit & Debit Cards", "@creditDebitCards": {}, "bank": "Bank", "@bank": {}, "addAnotherMethod": "Add Another Another Method", "@addAnotherMethod": {}, "congratulation": "Congratulations", "@congratulation": {}, "youHaveGotTransportation": "You have got the transportation spot. you can process further by making Payments", "@youHaveGotTransportation": {}, "changeBid": "Change Bid", "@changeBid": {}, "yourCurrentBid": "Your Current Bid", "@yourCurrentBid": {}, "minimumBidRequired": "Minimum bid required for your car to be transported", "@minimumBidRequired": {}, "minimumRequirementFor": "Minimum requirement for your car to be transported.", "@minimumRequirementFor": {}, "chooseYourBidIncrement": "Choose your bid increment", "@chooseYourBidIncrement": {}, "yourNeWBidWillBe": "Your New Bid will be", "@yourNeWBidWillBe": {}, "note": "Note", "@note": {}, "requestedTrips": "Requested Trips", "@requestedTrips": {}, "acceptedTrips": "Accepted Trips", "@acceptedTrips": {}, "yourPositionInList": "Your position in List", "@yourPositionInList": {}, "enterAuction": "Enter Auction", "@enterAuction": {}, "reject": "Reject", "@reject": {}, "dueToMoreUser": "Due to more users than available spots, Priority for reserving spots will be given to those who offer a higher Payments for the spots", "@dueToMoreUser": {}, "wait": "Wait", "@wait": {}, "joinWaitlist": "Join <PERSON>", "@joinWaitlist": {}, "areUSure": "Are you sure?", "@areUSure": {}, "uAreRejecting": "You are rejecting this offer do you want to wait for another offer or want to go to waitlist", "@uAreRejecting": {}, "resumeTrip": "Resume Trip", "@resumeTrip": {}, "editTrip": "Edit Trip", "@editTrip": {}, "noTripFound": "No trip found", "@noTripFound": {}, "auction": "Auction", "@auction": {}, "closingIn": "Closing In", "@closingIn": {}, "opnTrackTransportation": "OnTrack Transportation", "@opnTrackTransportation": {}, "availableSlot": "Available Slots", "@availableSlot": {}, "noOfAvailableSlot": "No.Of Available Slots", "@noOfAvailableSlot": {}, "carsWanted": "Cars wanted to Move", "@carsWanted": {}, "yourCurrentSpot": "Your Current Spot", "@yourCurrentSpot": {}, "listOfCurrentBid": "List of Current bids", "@listOfCurrentBid": {}, "theUsersListed": "The users listed inside the box are the ones that will be transported.", "@theUsersListed": {}, "exitAuction": "Exit Auction", "@exitAuction": {}, "startBiding": "Start Biding", "@startBiding": {}, "uHaveNotBid": "You haven't bid yet", "@uHaveNotBid": {}, "accept": "Accept", "@accept": {}, "origin": "Origin", "@origin": {}, "destination": "Destination", "@destination": {}, "small": "Small", "@small": {}, "medium": "Medium", "@medium": {}, "large": "Large", "@large": {}, "enterVehicleVersion": "Enter Vehicle Version", "@enterVehicleVersion": {}, "enterVehicleCondition": "Enter Vehicle Condition", "@enterVehicleCondition": {}, "pauseTrip": "<PERSON><PERSON>", "@pauseTrip": {}, "yesCancel": "Yes, <PERSON>cel", "@yesCancel": {}, "restTrip": "Rest Trip", "@restTrip": {}, "areUSureCancel": "Are you sure want to\ncancel this trip?", "@areUSureCancel": {}, "addedVehiclesInfo": "Approved Vehicles Info", "@addedVehiclesInfo": {}, "rejectedVehiclesInfo": "Rejected Vehicles Info", "@rejectedVehiclesInfo": {}, "pendingVehiclesInfo": "Pending Vehicles Info", "@pendingVehiclesInfo": {}, "allNotifications": "All Notifications", "@allNotifications": {}, "noNotificationsFound": "No notification found", "@noNotificationsFound": {}, "sooner": "Sooner", "@sooner": {}, "language": "Language", "@language": {}, "selectLanguage": "Select Language", "@selectLanguage": {}, "locationServicesDisabled": "Location services are disabled. Please enable them to use this feature.", "@locationServicesDisabled": {}, "locationPermissionsDenied": "Location permissions are denied. Please enable them to use this feature.", "@locationPermissionsDenied": {}, "locationPermissionsDeniedForever": "Location permissions are permanently denied. Please enable them in settings.", "@locationPermissionsDeniedForever": {}, "failedToGetLocation": "Failed to get current location. Please try again.", "@failedToGetLocation": {}, "searchAddress": "Search Address", "@searchAddress": {}, "search": "Search", "@search": {}, "selectAnotherAddress": "Something went wrong, Please select another address", "@selectAnotherAddress": {}, "somethingWentWrong": "Something went wrong, Please try again", "@somethingWentWrong": {}, "selectLocation": "Select Location", "@selectLocation": {}, "selectLocationOnMap": "Please select a location on the map", "@selectLocationOnMap": {}, "failedToGetLocationDetails": "Failed to get location details", "@failedToGetLocationDetails": {}, "tripDetails": "Trip Details", "@tripDetails": {}, "dropAndPickupPerson": "Drop and Pickup person", "@dropAndPickupPerson": {}, "addDropAndPickupPerson": "+ Add drop and pickup person", "@addDropAndPickupPerson": {}, "day": "day", "@day": {}, "clientName": "Client Name", "@clientName": {}, "checklistType": "Checklist Type", "@checklistType": {}, "pickup": "Pickup", "@pickup": {}, "delivery": "Delivery", "@delivery": {}, "fuelLevel": "Fuel level", "@fuelLevel": {}, "mileageAt": "Mileage at {type} (In miles/hr)", "@mileageAt": {"placeholders": {"type": {"type": "String"}}}, "dateLabel": "{type} Date", "@dateLabel": {"placeholders": {"type": {"type": "String"}}}, "carSizeLabel": "Car size:", "@carSizeLabel": {}, "carSizeSmall": "Small", "@carSizeSmall": {}, "carSizeMedium": "Medium", "@carSizeMedium": {}, "carSizeLarge": "Large", "@carSizeLarge": {}, "insurance": "Insurance", "@insurance": {}, "insuranceProvider": "Insurance provider", "@insuranceProvider": {}, "asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount": "As we have two different pricing we will take the largest sum amount as towing cost.", "@asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount": {}, "youHaveOptForWinchServiceSoYouCanNotDisableThisOption": "You have opt for winch service so you can not disable this option.", "@youHaveOptForWinchServiceSoYouCanNotDisableThisOption": {}, "type": "Type", "@type": {}, "performedDuring": "Performed During", "@performedDuring": {}, "report": "Report", "@report": {}, "viewLess": "View less", "@viewLess": {}, "viewMore": "View more", "@viewMore": {}, "waitingList": "Waiting List", "@waitingList": {}, "restedTrip": "Rested Trip", "@restedTrip": {}, "resetPassword": "Reset password", "@resetPassword": {}, "providerInfo": "Provider Info", "@providerInfo": {}, "noTransporterFoundYet": "No transporter found yet", "@noTransporterFoundYet": {}, "pleaseEnterValidPickupAddress": "Please enter valid pickup address", "@pleaseEnterValidPickupAddress": {}, "stopLocation": "Stop Location", "@stopLocation": {}, "pleaseAssignCarWithin": "Please assign all remaining cars to a transporter within {type}.", "@pleaseAssignCarWithin": {"placeholders": {"type": {"type": "String"}}}, "noStockLocationFound": "No stock location found, Please choose another location", "@noStockLocationFound": {}, "pleaseSelectInsurance": "Please select insurance for your vehicle", "@pleaseSelectInsurance": {}, "thanksUForFeedback": "Thank you for your feedback", "@thanksUForFeedback": {}, "noCheckListFound": "Your vehicle isn’t performed yet. After it gets performed, You’ll have to approve a checklist.", "@noCheckListFound": {}, "bookingNoteCreatedSuccess": "Booking note created successfully", "@bookingNoteCreatedSuccess": {}, "bookingNoteDeletedSuccess": "Booking note deleted successfully", "@bookingNoteDeletedSuccess": {}, "transporterDoesNotHave": "Transporter does not have winch option.", "@transporterDoesNotHave": {}, "transporterDoesNotMeet": "Transporter does not meet vehicle requirements.", "@transporterDoesNotMeet": {}, "noAvailableSlot": "No available slots for remaining vehicles.", "@noAvailableSlot": {}, "allVehicleAreAlready": "All vehicles are already assigned.", "@allVehicleAreAlready": {}, "enableToUploadImages": "Enable to upload images, Please try again latter", "@enableToUploadImages": {}, "pleaseAddBothPickupNDropInfo": "Please add both pickup and drop info", "@pleaseAddBothPickupNDropInfo": {}, "description": "Description", "@description": {}, "pleaseDescribeIssue": "Please describe your issue", "@pleaseDescribeIssue": {}, "photosOptional": "Photos (Optional)", "@photosOptional": {}, "submit": "Submit", "@submit": {}, "yourComplainSubmittedSuccessfully": "Your Complain Submitted Successfully", "@yourComplainSubmittedSuccessfully": {}, "chooseAnAction": "Choose an Action", "@chooseAnAction": {}, "camera": "Camera", "@camera": {}, "gallery": "Gallery", "@gallery": {}, "maxImagesError": "Maximum 7 images can be selected.", "@maxImagesError": {}, "imagesNotUploaded": "Images are not uploaded", "@imagesNotUploaded": {}, "pleaseEnterDescription": "Please enter your description", "@pleaseEnterDescription": {}, "vehicleSize": "Vehicle Size", "@vehicleSize": {}, "vehicleDescription": "Vehicle Description", "@vehicleDescription": {}, "isWinchRequired": "Is winch required?", "@isWinchRequired": {}, "pickupService": "Pickup Service", "@pickupService": {}, "minimumCost": "Minimum Cost", "@minimumCost": {}, "pickupAddress": "Pickup Address", "@pickupAddress": {}, "charge": "Charge", "@charge": {}, "vehicleCharges": "Vehicle Charges", "@vehicleCharges": {}, "openMap": "Open Map", "@openMap": {}, "select": "Select", "@select": {}, "winchSupport": "Winch Support", "@winchSupport": {}, "exception": "Exception", "@exception": {}, "exceptionsSupport": "Exceptions Support", "@exceptionsSupport": {}, "writeUrMessageHere": "Write your message here", "@writeUrMessageHere": {}, "originStockAdmin": "Origin stock admin", "@originStockAdmin": {}, "dropStockAdmin": "Drop stock admin", "@dropStockAdmin": {}, "readMore": "read more", "@readMore": {}, "readLess": "read less", "@readLess": {}, "customerDetail": "Customer Detail", "@customerDetail": {}, "addCustomerDetail": "Add Customer Detail", "@addCustomerDetail": {}, "mobileNumber": "Mobile Number", "@mobileNumber": {}, "enterUrMobileNumber": "Enter your mobile number", "@enterUrMobileNumber": {}, "pleaseEnterUrMobileNumber": "Please enter your mobile number", "@pleaseEnterUrMobileNumber": {}, "pleaseEnterValidMobileNumber": "Please enter valid mobile number", "@pleaseEnterValidMobileNumber": {}, "chatInactive": "<PERSON><PERSON> is inactive, You can not chat here anymore!", "@chatInactive": {}, "noTripDatFound": "No trip data found", "@noTripDatFound": {}, "noMessageYet": "No messages yet", "@noMessageYet": {}, "noteForMobile": "Note: The mobile number is only visible to those transporters and stock administrators who are associated with the trip in which you select the pickup vehicle from your location or winch option, or transporters associated with exclusive booking.", "@noteForMobile": {}, "pleaseReview": "* Please review the checklist carefully. Once you've verified all the items, Proceed by confirming the checklist. We won't be able to continue until the checklist is approved.", "@pleaseReview": {}, "affectedTime": "Affected time", "@affectedTime": {}, "hour": "hour", "@hour": {}, "addComments": "Add Comments", "@addComments": {}, "comments": "Comments", "@comments": {}, "pleaseWaitWeAreRedirectingStripe": "Please wait we are redirecting you to Stripe customer portal", "@pleaseWaitWeAreRedirectingStripe": {}, "pleaseWaitWeAreRedirectingPayment": "Please wait we are redirecting you to Payment page", "@pleaseWaitWeAreRedirectingPayment": {}, "customerDetailSavedSuccess": "Customer detail saved successfully", "@customerDetailSavedSuccess": {}, "pleaseSearchOriginStopLocation": "Please first search origin stock location before you choose the stock location", "@pleaseSearchOriginStopLocation": {}, "pleaseSearchDropStopLocation": "Please first search drop stock location before you choose the stock location", "@pleaseSearchDropStopLocation": {}, "anyUndeclared": "Any undeclared additional items or modifications may result in the cancellation of the trip ", "@anyUndeclared": {}, "withoutRefund": "without refund.", "@withoutRefund": {}, "itIsUserResponsibility": " It is the user`s responsibility to disclose all relevant details before the journey begins.", "@itIsUserResponsibility": {}, "pleaseAcceptDeclaration": "Please accept above declaration", "@pleaseAcceptDeclaration": {}, "removeAllVehicle": "- Remove all vehicle", "@removeAllVehicle": {}, "chooseSlot": "Choose <PERSON>", "@chooseSlot": {}, "car": "Car", "@car": {}, "totalCostSlot": "Total cost for", "@totalCostSlot": {}, "slots": "Slots", "@slots": {}, "bookingStatus": "Booking status", "@bookingStatus": {}, "status": "Status", "@status": {}, "myRating": "My Rating", "@myRating": {}, "insuranceCharges": "Insurance charges varies upon your vehicle sizes", "@insuranceCharges": {}, "attachPhotos": "Attach photos of your vehicle additional compartments or modifications allowing the team to review and validate that the vehicle can be transported without issues.", "@attachPhotos": {}, "attachedPhotos": "Attached photos", "@attachedPhotos": {}, "addAnyDiscrepancies": "Add any discrepancies or additional observations about your vehicle (e.g., Missing items, Damage, or anything not listed above).", "@addAnyDiscrepancies": {}, "vehicleImages": "Vehicle images", "@vehicleImages": {}}