import 'package:flutter/foundation.dart' show immutable;
import 'package:get_it/get_it.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/presentation/provider/app_provider.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';

/// Use Case injection
@immutable
class RepositoryInjector {
  /// Construct
  RepositoryInjector(this.instance) {
    _init();
  }

  /// GetIt instance
  final GetIt instance;

  /// TODO : Remove the instanceName parameter when production server is in use
  void _init() {
    Injector.instance.isReady<AppDB>().then(
      (value) {
        instance.registerSingleton(AppProvider());
      },
    );
    Injector.instance.registerFactory(
      () => AccountRepository(dio: instance(instanceName: 'open')),
    );
    Injector.instance.registerFactory(
      () => HomeRepository(dio: instance(instanceName: 'open')),
    );
    Injector.instance.registerFactory(
      () => TripRepository(dio: instance(instanceName: 'open')),
    );
  }
}
