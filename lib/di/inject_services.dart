import 'package:flutter/foundation.dart' show immutable;
import 'package:get_it/get_it.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/services/socket_service/socket_service.dart';

/// Services injector
@immutable
class ServicesInjector {
  /// Constructor
  ServicesInjector(this.instance) {
    _init();
  }

  /// GetIt instance
  final GetIt instance;

  void _init() {
    instance
      ..registerSingletonAsync(AppDB.getInstance)
      ..registerSingleton<SocketService>(SocketService());
  }
}
